#!/bin/bash

# 构建和测试脚本
# 用于验证ExoPlayer SimpleCache集成

echo "🚀 开始构建CabyCare Android项目..."

cd /Users/<USER>/github/CabyCare/android

# 清理项目
echo "🧹 清理项目..."
./gradlew clean

# 检查依赖
echo "📦 检查依赖..."
./gradlew dependencies --configuration implementation | grep media3

# 编译项目
echo "🔨 编译项目..."
./gradlew assembleDebug

if [ $? -eq 0 ]; then
    echo "✅ 构建成功！"
    echo ""
    echo "📋 集成完成的功能："
    echo "   • ExoPlayer SimpleCache 自动缓存"
    echo "   • CacheDataSource 支持 HLS 流缓存"
    echo "   • 缓存状态监控和显示"
    echo "   • 缓存管理界面"
    echo "   • 自动缓存清理机制"
    echo ""
    echo "🎯 主要改进："
    echo "   • 删除了手动缓存机制"
    echo "   • 使用ExoPlayer内置的缓存系统"
    echo "   • 自动处理HLS段的缓存和回收"
    echo "   • 智能缓存策略 (LRU)"
    echo "   • 不需要离线播放开关"
    echo ""
    echo "📱 可以安装到设备进行测试："
    echo "   adb install app/build/outputs/apk/debug/app-debug.apk"
else
    echo "❌ 构建失败！请检查错误信息。"
    exit 1
fi
