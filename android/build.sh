#!/bin/bash

# CabyCare Android 构建脚本
# 设置Java环境并构建项目

echo "🚀 开始构建 CabyCare Android..."

# 设置Java环境
export JAVA_HOME=/opt/homebrew/opt/openjdk@17/libexec/openjdk.jdk/Contents/Home
export PATH="/opt/homebrew/opt/openjdk@17/bin:$PATH"

# 检查Java版本
echo "📋 Java版本信息:"
java -version

echo ""
echo "🔨 开始构建..."

# 构建debug版本
./gradlew assembleDebug

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 构建成功！"
    echo "📱 APK文件位置: app/build/outputs/apk/debug/app-debug.apk"
    
    # 显示APK信息
    APK_PATH="app/build/outputs/apk/debug/app-debug.apk"
    if [ -f "$APK_PATH" ]; then
        APK_SIZE=$(du -h "$APK_PATH" | cut -f1)
        echo "📦 APK大小: $APK_SIZE"
    fi
else
    echo ""
    echo "❌ 构建失败！"
    exit 1
fi
