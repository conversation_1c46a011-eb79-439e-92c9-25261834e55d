# Built application files
*.apk
*.aar
*.ap_
*.aab

# Files for the ART/Dalvik VM
*.dex

# Java class files
*.class

# Generated files
bin/
gen/
out/
#  Uncomment the following line in case you need and you don't have the release build type files in your app
# release/

# Gradle files
.gradle/
build/

# Local configuration file (sdk path, etc)
local.properties

# Proguard folder generated by Eclipse
proguard/

# Log Files
*.log

# Android Studio Navigation editor temp files
.navigation/

# Android Studio captures folder
captures/

# IntelliJ
*.iml
.idea/workspace.xml
.idea/tasks.xml
.idea/gradle.xml
.idea/assetWizardSettings.xml
.idea/dictionaries
.idea/libraries
# Android Studio 3 in .gitignore file.
.idea/caches
.idea/modules.xml
# Comment next line if keeping position of elements in Navigation Editor is relevant for you
.idea/navEditor.xml

# Keystore files
# Uncomment the following lines if you do not want to check your keystore files in.
#*.jks
#*.keystore

# External native build folder generated in Android Studio 2.2 and later
.externalNativeBuild
.cxx/

# Google Services (e.g. APIs or Firebase)
# google-services.json

# Freeline
freeline.py
freeline/
freeline_project_description.json

# fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output
fastlane/readme.md

# Version control
vcs.xml

# lint
lint/intermediates/
lint/generated/
lint/outputs/
lint/tmp/
# lint/reports/

# Android Profiling
*.hprof

# Kotlin
*.kt.class

# Compose
compose_compiler/

# Backup files
*.bak
*.tmp
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
*.swp
*.swo

# Gradle Wrapper
!gradle/wrapper/gradle-wrapper.jar
!gradle/wrapper/gradle-wrapper.properties

# Local development
error.log
debug.log
*.log.*

# Signing files
app/release/
app/debug/
signing/

# NDK
obj/

# IntelliJ IDEA
.idea/
*.iws

# Android Studio
.android/
.gradle/
local.properties

# Kotlin Multiplatform
kotlin-js-store/

# Dependency directories
node_modules/

# Build outputs
outputs/

# Test results
test-results/
androidTest-results/

# Coverage reports
coverage/
jacoco/

# Lint reports
lint-results*.xml
lint-results*.html

# Memory dumps
*.hprof

# Profiler output
*.trace

# Crashlytics
crashlytics-build.properties
fabric.properties

# Firebase
google-services.json
GoogleService-Info.plist

# Secrets and API keys
secrets.properties
api-keys.properties
keystore.properties

# Generated by KSP (Kotlin Symbol Processing)
ksp/

# Generated by Hilt
hilt_aggregated_deps/

# Room database schemas
schemas/

# Temporary files
*.tmp
*.temp

# Backup files
*.backup
*.old

# Archive files
*.zip
*.tar.gz
*.rar

# Documentation build
docs/build/

# Local configuration
config/local/
