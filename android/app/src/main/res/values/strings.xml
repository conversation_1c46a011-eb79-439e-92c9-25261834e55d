<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">CabyCare</string>
    
    <!-- Attribution Tags for AppOps -->
    <string name="attribution_video_streaming">Video Streaming</string>
    <string name="attribution_network_access">Network Access</string>
    <string name="attribution_media_playback">Media Playback</string>
    <string name="attribution_device_monitoring">Device Monitoring</string>
    
    <!-- 通用 -->
    <string name="ok">确定</string>
    <string name="cancel">取消</string>
    <string name="save">保存</string>
    <string name="delete">删除</string>
    <string name="edit">编辑</string>
    <string name="add">添加</string>
    <string name="retry">重试</string>
    <string name="loading">加载中...</string>
    <string name="error">错误</string>
    <string name="success">成功</string>
    
    <!-- 导航 -->
    <string name="nav_home">首页</string>
    <string name="nav_care">关爱</string>
    <string name="nav_animals">宠物</string>
    <string name="nav_device">设备</string>
    
    <!-- 首页 -->
    <string name="home_title">CabyCare</string>
    <string name="home_welcome">欢迎使用CabyCare</string>
    <string name="home_add_first_pet">添加您的第一只宠物开始使用</string>
    <string name="home_needs_attention">需要关注</string>
    <string name="home_today_activity">今日活动</string>
    <string name="home_health_score">健康评分</string>
    <string name="home_activity_level">活跃度</string>
    
    <!-- 关爱页面 -->
    <string name="care_title">实时关爱</string>
    <string name="care_live_streaming">直播中</string>
    <string name="care_no_video">暂无视频流</string>
    <string name="care_recent_activity">最近活动</string>
    <string name="care_quick_actions">快速操作</string>
    <string name="care_take_snapshot">截图</string>
    <string name="care_toggle_recording">录制</string>
    <string name="care_send_notification">通知</string>
    
    <!-- 宠物页面 -->
    <string name="animals_title">我的宠物</string>
    <string name="animals_add_pet">添加宠物</string>
    <string name="animals_no_pets">还没有宠物</string>
    <string name="animals_add_first_pet">添加您的第一只宠物开始使用CabyCare</string>
    <string name="animals_edit_pet">编辑猫咪信息</string>
    <string name="animals_add_new_pet">添加新猫咪</string>
    
    <!-- 设备页面 -->
    <string name="device_title">设备管理</string>
    <string name="device_overview">设备概览</string>
    <string name="device_total">总设备</string>
    <string name="device_online">在线</string>
    <string name="device_offline">离线</string>
    <string name="device_add_new">添加新设备</string>
    <string name="device_no_devices">还没有设备</string>
    <string name="device_add_first">添加您的第一个CabyCare设备</string>
    <string name="device_settings">设备设置</string>
    <string name="device_ota_update">OTA升级</string>
    
    <!-- 登录 -->
    <string name="login_welcome">欢迎使用CabyCare</string>
    <string name="login_subtitle">请登录您的账户以继续使用</string>
    <string name="login_button">登录</string>
    <string name="login_first_time">首次使用？系统将自动为您创建账户</string>
    <string name="login_terms">登录即表示您同意我们的服务条款和隐私政策</string>
    
    <!-- 视频播放 -->
    <string name="video_play">播放</string>
    <string name="video_pause">暂停</string>
    <string name="video_fullscreen">全屏</string>
    <string name="video_exit_fullscreen">退出全屏</string>
    <string name="video_live">直播</string>
    
    <!-- 错误信息 -->
    <string name="error_network">网络连接失败</string>
    <string name="error_load_failed">加载失败</string>
    <string name="error_save_failed">保存失败</string>
    <string name="error_delete_failed">删除失败</string>
    <string name="error_unknown">未知错误</string>
    
    <!-- 健康状态 -->
    <string name="health_healthy">健康</string>
    <string name="health_needs_attention">需要关注</string>
    <string name="health_sick">生病</string>
    
    <!-- 猫咪信息 -->
    <string name="cat_name">姓名</string>
    <string name="cat_age">年龄</string>
    <string name="cat_weight">体重</string>
    <string name="cat_gender">性别</string>
    <string name="cat_type">类型</string>
    <string name="cat_health_status">健康状态</string>
    <string name="cat_activity_level">活跃度</string>
    <string name="cat_male">公猫</string>
    <string name="cat_female">母猫</string>
</resources>
