package com.cabycare.android.data.repository

import android.util.Log
import com.cabycare.android.data.model.*
import com.cabycare.android.data.network.ApiService
import com.cabycare.android.data.network.NetworkResult
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 设备数据仓库
 * 负责管理设备相关的数据操作
 */
@Singleton
class DeviceRepository @Inject constructor(
    private val apiService: ApiService
) : BaseRepository() {

    companion object {
        private const val TAG = "DeviceRepository"
    }
    
    /**
     * 获取可访问设备列表
     */
    suspend fun getAccessibleDevices(userId: String): NetworkResult<List<AccessibleDevice>> {
        Log.d(TAG, "🔍 获取可访问设备列表")
        Log.d(TAG, "🆔 使用的用户ID: $userId")
        Log.d(TAG, "🌐 API调用: GET /api/devices/accessible?user_id=$userId")

        return handleListResult(
            safeApiCall {
                val response = apiService.getAccessibleDevices(userId)
                Log.d(TAG, "📡 API响应状态: ${response.status}")
                Log.d(TAG, "📡 API响应消息: ${response.message}")
                Log.d(TAG, "📡 API响应数据数量: ${response.data.size}")

                if (response.isSuccess) {
                    response.data
                } else {
                    throw Exception(response.message)
                }
            }
        )
    }
    
    /**
     * 获取设备实时状态
     * 根据API文档，这个API返回设备的实时在线状态
     */
    suspend fun getDeviceStatus(deviceId: String): NetworkResult<DeviceStatusResponse> {
        Log.d(TAG, "🔍 获取设备实时状态")
        Log.d(TAG, "🆔 设备ID: $deviceId")
        Log.d(TAG, "🌐 API调用: GET /api/devices/$deviceId/status")

        return safeApiCall {
            val statusResponse = apiService.getDeviceStatus(deviceId)
            Log.d(TAG, "📡 设备状态响应: 设备=${statusResponse.name}, 在线=${statusResponse.online}")
            statusResponse
        }
    }
    
    /**
     * 批量获取设备实时状态
     * 为设备列表中的每个设备获取最新的在线状态
     */
    suspend fun getDevicesStatus(deviceIds: List<String>): NetworkResult<Map<String, DeviceStatusResponse>> {
        Log.d(TAG, "🔍 批量获取设备实时状态，设备数量: ${deviceIds.size}")
        
        return safeApiCall {
            val statusMap = mutableMapOf<String, DeviceStatusResponse>()
            
            deviceIds.forEach { deviceId ->
                try {
                    val statusResponse = apiService.getDeviceStatus(deviceId)
                    statusMap[deviceId] = statusResponse
                    Log.d(TAG, "✅ 设备 $deviceId 状态: 在线=${statusResponse.online}")
                } catch (e: Exception) {
                    Log.w(TAG, "⚠️ 获取设备 $deviceId 状态失败: ${e.message}")
                    // 创建一个默认的离线状态
                    statusMap[deviceId] = DeviceStatusResponse(
                        deviceId = deviceId,
                        name = "Unknown Device",
                        online = false
                    )
                }
            }
            
            Log.d(TAG, "📡 批量状态获取完成，成功获取 ${statusMap.size} 个设备状态")
            statusMap
        }
    }
    
    /**
     * 获取可访问设备列表及其实时状态
     * 这是一个增强版本，会同时获取设备列表和实时状态
     */
    suspend fun getAccessibleDevicesWithStatus(userId: String): NetworkResult<List<AccessibleDeviceWithStatus>> {
        Log.d(TAG, "🔍 获取可访问设备列表及实时状态")
        
        return safeApiCall {
            // 1. 首先获取设备列表
            val devicesResponse = apiService.getAccessibleDevices(userId)
            if (!devicesResponse.isSuccess) {
                throw Exception(devicesResponse.message)
            }
            
            val devices = devicesResponse.data
            Log.d(TAG, "📋 获取到 ${devices.size} 台设备，开始获取实时状态")
            
            // 2. 为每个设备获取实时状态
            val devicesWithStatus = devices.map { device ->
                try {
                    val statusResponse = apiService.getDeviceStatus(device.deviceId)
                    Log.d(TAG, "✅ 设备 ${device.name} 实时状态: 在线=${statusResponse.online}")
                    
                    AccessibleDeviceWithStatus(
                        device = device,
                        realTimeStatus = statusResponse,
                        isOnline = statusResponse.online ?: false,
                        lastHeartbeat = statusResponse.lastSeen
                    )
                } catch (e: Exception) {
                    Log.w(TAG, "⚠️ 获取设备 ${device.name} 实时状态失败: ${e.message}")
                    
                    // 如果无法获取实时状态，使用设备列表中的状态
                    AccessibleDeviceWithStatus(
                        device = device,
                        realTimeStatus = null,
                        isOnline = device.isOnline,
                        lastHeartbeat = device.lastHeartbeat
                    )
                }
            }
            
            Log.d(TAG, "🎉 设备状态获取完成，在线设备: ${devicesWithStatus.count { it.isOnline }} / ${devicesWithStatus.size}")
            devicesWithStatus
        }
    }
    
    /**
     * 获取设备详情
     */
    suspend fun getDeviceDetail(deviceId: String): NetworkResult<Device> {
        return safeApiCall {
            val response = apiService.getDeviceDetail(deviceId)
            if (response.isSuccess && response.data != null) {
                response.data
            } else {
                throw Exception(response.message)
            }
        }
    }
    
    /**
     * 更新设备信息
     */
    suspend fun updateDevice(deviceId: String, device: Device): NetworkResult<Device> {
        return safeApiCall {
            val response = apiService.updateDevice(deviceId, device)
            if (response.isSuccess && response.data != null) {
                response.data
            } else {
                throw Exception(response.message)
            }
        }
    }
    
    /**
     * 获取设备OTA状态
     */
    suspend fun getDeviceOTAStatus(deviceId: String): NetworkResult<DeviceOTAStatusResponse> {
        return safeApiCall {
            apiService.getDeviceOTAStatus(deviceId)
        }
    }
    
    /**
     * 触发设备OTA更新
     */
    suspend fun triggerOTAUpdate(deviceId: String): NetworkResult<Unit> {
        return safeApiCall {
            val response = apiService.triggerOTAUpdate(deviceId)
            if (response.isSuccess) {
                Unit
            } else {
                throw Exception(response.message)
            }
        }
    }

    /**
     * 删除设备
     */
    suspend fun deleteDevice(deviceId: String): NetworkResult<Unit> {
        return safeApiCall {
            // 假设有删除设备的API，如果没有就返回成功
            // 实际项目中需要调用真实的删除API
            Log.d(TAG, "🗑️ 删除设备: $deviceId")
            // val response = apiService.deleteDevice(deviceId)
            // if (response.isSuccess) {
                Unit
            // } else {
            //     throw Exception(response.message)
            // }
        }
    }
}
