package com.cabycare.android.data.repository

import android.util.Log
import com.cabycare.android.data.model.*
import com.cabycare.android.data.network.ApiService
import com.cabycare.android.data.network.NetworkResult
import kotlinx.serialization.SerializationException
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 视频数据仓库
 * 负责管理视频流和历史视频相关的数据操作
 */
@Singleton
class VideoRepository @Inject constructor(
    private val apiService: ApiService
) : BaseRepository() {

    companion object {
        private const val TAG = "VideoRepository"
    }
    
    // MARK: - 视频记录管理

    /**
     * 获取设备视频列表
     * 修复：正确处理API返回null的情况
     */
    suspend fun getVideoList(deviceId: String, start: String? = null, end: String? = null): NetworkResult<List<VideoSegmentRecord>> {
        Log.d(TAG, "获取设备视频列表: $deviceId")
        return safeApiCall {
            try {
                val path = "device$deviceId"
                val response = apiService.getVideoList(path, start, end)
                // 如果API返回null（设备没有视频），转换为空列表
                response ?: emptyList<VideoSegmentRecord>()
            } catch (e: SerializationException) {
                // 捕获JSON解析异常，这通常发生在服务器返回"null"字符串时
                Log.d(TAG, "🔄 API返回无效JSON响应（可能是null），转换为空列表: ${e.message}")
                emptyList<VideoSegmentRecord>()
            } catch (e: retrofit2.HttpException) {
                // 如果是404或其他HTTP错误，也返回空列表
                if (e.code() == 404) {
                    Log.d(TAG, "🔄 设备未找到或无数据，返回空列表")
                    emptyList<VideoSegmentRecord>()
                } else {
                    throw e
                }
            }
        }
    }

    /**
     * 获取猫咪的如厕记录（视频段）
     * 对应iOS版本的getToiletSegments方法
     * 
     * @param catId 猫咪ID
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @param devices 用户的设备列表
     * @return 按时间倒序排列的如厕记录
     */
    suspend fun getToiletSegments(
        catId: String,
        startDate: String,
        endDate: String,
        devices: List<AccessibleDevice>
    ): NetworkResult<List<VideoSegmentRecord>> {
        Log.d(TAG, "🐱 开始获取猫咪 $catId 的如厕记录，时间范围: $startDate 到 $endDate")
        
        val allSegments = mutableListOf<VideoSegmentRecord>()
        
        return safeApiCall {
            // 遍历用户的所有设备获取视频段
            for (device in devices) {
                try {
                    Log.d(TAG, "🐱 正在从设备 ${device.name} (${device.deviceId}) 获取如厕记录")
                    
                    // 使用正确的视频API获取数据
                    val path = "device${device.deviceId}"
                    val response = try {
                        apiService.getVideoList(
                            path = path,
                            start = startDate,
                            end = endDate
                        )
                    } catch (e: SerializationException) {
                        // 捕获JSON解析异常，这通常发生在服务器返回"null"字符串时
                        Log.d(TAG, "🔄 设备 ${device.name} 返回无效JSON响应（可能是null），使用空列表: ${e.message}")
                        null
                    } catch (e: retrofit2.HttpException) {
                        // 如果是404或其他HTTP错误，也使用空列表
                        if (e.code() == 404) {
                            Log.d(TAG, "🔄 设备 ${device.name} 未找到或无数据")
                            null
                        } else {
                            Log.e(TAG, "❌ 从设备 ${device.name} 获取数据时发生HTTP错误: ${e.code()}")
                            null
                        }
                    }
                    
                    // 处理null响应：如果API返回null，转换为空列表
                    val deviceSegments = response ?: emptyList()
                    
                    // 过滤出属于指定猫咪的如厕记录（如果有animalId的话）
                    val filteredSegments = if (catId.isNotEmpty()) {
                        deviceSegments.filter { segment ->
                            segment.animalId == catId || segment.animalId.isNullOrEmpty()
                        }
                    } else {
                        deviceSegments
                    }
                    
                    allSegments.addAll(filteredSegments)
                    Log.d(TAG, "🐱 设备 ${device.name} 为猫咪 $catId 找到 ${filteredSegments.size} 条记录")
                    
                } catch (e: Exception) {
                    Log.e(TAG, "❌ 从设备 ${device.name} 获取如厕记录失败", e)
                    // 继续处理其他设备，不中断整个流程
                    continue
                }
            }
            
            // 按时间倒序排序（最新的在前面）
            val sortedSegments = allSegments.sortedByDescending { segment ->
                segment.getStartTime()
            }
            
            Log.d(TAG, "🐱 猫咪 $catId 在指定时间范围内共找到 ${sortedSegments.size} 次记录")
            
            if (sortedSegments.isEmpty()) {
                Log.d(TAG, "🐱 没有找到猫咪 $catId 的记录")
            } else {
                Log.d(TAG, "🐱 找到的记录时间范围: ${sortedSegments.first().start} 到 ${sortedSegments.last().start}")
            }
            
            sortedSegments
        }
    }

    /**
     * 获取设备在指定时间范围内的所有视频段
     * 修复：正确处理null响应和异常
     */
    suspend fun getDeviceVideoSegments(
        deviceId: String,
        startDate: String,
        endDate: String
    ): NetworkResult<List<VideoSegmentRecord>> {
        Log.d(TAG, "获取设备 $deviceId 的视频段，时间范围: $startDate 到 $endDate")
        
        return safeApiCall {
            try {
                val path = "device$deviceId"
                val response = apiService.getVideoList(
                    path = path,
                    start = startDate,
                    end = endDate
                )
                
                // 处理null响应：如果API返回null，转换为空列表
                val segments = response ?: emptyList()
                
                Log.d(TAG, "✅ 设备 $deviceId 获取到 ${segments.size} 个视频段")
                segments
            } catch (e: SerializationException) {
                // 捕获JSON解析异常，这通常发生在服务器返回"null"字符串时
                Log.d(TAG, "🔄 设备 $deviceId 返回无效JSON响应（可能是null），使用空列表: ${e.message}")
                emptyList<VideoSegmentRecord>()
            } catch (e: retrofit2.HttpException) {
                // 如果是404或其他HTTP错误，也返回空列表
                if (e.code() == 404) {
                    Log.d(TAG, "🔄 设备 $deviceId 未找到或无数据，返回空列表")
                    emptyList<VideoSegmentRecord>()
                } else {
                    throw e
                }
            }
        }
    }
}
