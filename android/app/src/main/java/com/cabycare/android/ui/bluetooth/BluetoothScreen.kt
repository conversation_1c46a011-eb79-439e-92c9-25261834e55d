package com.cabycare.android.ui.bluetooth

import android.bluetooth.BluetoothDevice
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.cabycare.android.bluetooth.*

/**
 * 蓝牙设备配置页面
 * 基于iOS版本的BluetoothView实现
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BluetoothScreen(
    bluetoothViewModel: BluetoothViewModel = hiltViewModel(),
    onDeviceAdded: () -> Unit = {}
) {
    val bluetoothState by bluetoothViewModel.bluetoothState.collectAsState()
    val connectionStatus by bluetoothViewModel.connectionStatus.collectAsState()
    val discoveredDevices by bluetoothViewModel.discoveredDevices.collectAsState()
    val isScanning by bluetoothViewModel.isScanning.collectAsState()
    val connectedDevice by bluetoothViewModel.connectedDevice.collectAsState()
    val errorMessage by bluetoothViewModel.errorMessage.collectAsState()
    val wifiSendStatus by bluetoothViewModel.wifiSendStatus.collectAsState()
    val userIdSendStatus by bluetoothViewModel.userIdSendStatus.collectAsState()
    val activationStatus by bluetoothViewModel.activationStatus.collectAsState()
    val bluetoothStatusMessage by bluetoothViewModel.bluetoothStatusMessage.collectAsState()

    // WiFi配置状态
    var ssid by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var showPassword by remember { mutableStateOf(false) }
    var showWiFiConfirmDialog by remember { mutableStateOf(false) }

    // 启动时开始扫描
    LaunchedEffect(Unit) {
        bluetoothViewModel.requestScanningIfNeeded()
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(20.dp)
    ) {
        // 顶部状态卡片
        StatusCard(
            bluetoothState = bluetoothState,
            connectionStatus = connectionStatus,
            bluetoothStatusMessage = bluetoothStatusMessage,
            connectedDevice = connectedDevice,
            onDisconnect = { bluetoothViewModel.disconnectDevice() }
        )

        // 蓝牙权限/状态提示
        if (bluetoothState != BluetoothState.POWERED_ON) {
            BluetoothPermissionCard(
                bluetoothState = bluetoothState,
                bluetoothStatusMessage = bluetoothStatusMessage
            )
        }

        // 设备扫描区域
        if (bluetoothState == BluetoothState.POWERED_ON) {
            DeviceScanSection(
                discoveredDevices = discoveredDevices,
                isScanning = isScanning,
                connectedDevice = connectedDevice,
                connectionStatus = connectionStatus,
                onStartScanning = { bluetoothViewModel.startScanning() },
                onStopScanning = { bluetoothViewModel.stopScanning() },
                onConnectDevice = { device -> bluetoothViewModel.connectToDevice(device) }
            )
        }

        // 设备配置区域（当设备就绪时显示）
        if (connectionStatus == ConnectionStatus.READY) {
            // 设备激活区域
            DeviceActivationSection(
                activationStatus = activationStatus,
                onActivateDevice = { bluetoothViewModel.activateDevice() }
            )

            // WiFi配置区域
            WiFiConfigSection(
                ssid = ssid,
                password = password,
                showPassword = showPassword,
                wifiSendStatus = wifiSendStatus,
                onSsidChanged = { ssid = it },
                onPasswordChanged = { password = it },
                onTogglePasswordVisibility = { showPassword = !showPassword },
                onSendWiFiConfig = { showWiFiConfirmDialog = true }
            )
        }
    }

    // 错误消息对话框
    errorMessage?.let { message ->
        AlertDialog(
            onDismissRequest = { bluetoothViewModel.clearError() },
            title = { Text("错误") },
            text = { Text(message) },
            confirmButton = {
                TextButton(onClick = { bluetoothViewModel.clearError() }) {
                    Text("确定")
                }
            }
        )
    }

    // WiFi配置确认对话框
    if (showWiFiConfirmDialog) {
        AlertDialog(
            onDismissRequest = { showWiFiConfirmDialog = false },
            title = { Text("确认发送WiFi配置") },
            text = {
                Text("即将发送以下WiFi配置到设备：\n\n网络名称：$ssid\n密码：${if (password.isEmpty()) "无密码" else password}")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        bluetoothViewModel.sendWiFiConfig(ssid, password)
                        showWiFiConfirmDialog = false
                    }
                ) {
                    Text("确认发送")
                }
            },
            dismissButton = {
                TextButton(onClick = { showWiFiConfirmDialog = false }) {
                    Text("取消")
                }
            }
        )
    }

    // 发送成功提示
    val showSuccessDialog = wifiSendStatus == SendStatus.SUCCESS || userIdSendStatus == SendStatus.SUCCESS
    if (showSuccessDialog) {
        AlertDialog(
            onDismissRequest = { 
                bluetoothViewModel.resetSendStatus()
                if (userIdSendStatus == SendStatus.SUCCESS) {
                    onDeviceAdded()
                }
            },
            title = { Text("发送成功") },
            text = {
                Text(
                    when {
                        wifiSendStatus == SendStatus.SUCCESS -> "WiFi配置发送成功！"
                        userIdSendStatus == SendStatus.SUCCESS -> "用户ID发送成功！设备配置完成。"
                        else -> "发送成功！"
                    }
                )
            },
            confirmButton = {
                TextButton(
                    onClick = { 
                        bluetoothViewModel.resetSendStatus()
                        if (userIdSendStatus == SendStatus.SUCCESS) {
                            onDeviceAdded()
                        }
                    }
                ) {
                    Text("确定")
                }
            }
        )
    }
}

/**
 * 状态卡片
 */
@Composable
fun StatusCard(
    bluetoothState: BluetoothState,
    connectionStatus: ConnectionStatus,
    bluetoothStatusMessage: String,
    connectedDevice: BluetoothDevice?,
    onDisconnect: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 蓝牙图标
                Box(
                    modifier = Modifier
                        .size(48.dp)
                        .background(
                            color = statusColor(bluetoothState, connectionStatus),
                            shape = CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = bluetoothIcon(bluetoothState, connectionStatus),
                        contentDescription = "蓝牙状态",
                        tint = Color.White,
                        modifier = Modifier.size(24.dp)
                    )
                }

                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "蓝牙设备配置",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    Text(
                        text = bluetoothStatusMessage,
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                    )
                }

                // 状态指示器
                Surface(
                    color = statusColor(bluetoothState, connectionStatus),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text(
                        text = connectionStatusText(bluetoothState, connectionStatus),
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                        fontSize = 12.sp,
                        color = Color.White,
                        fontWeight = FontWeight.Medium
                    )
                }
            }

            // 已连接设备信息
            connectedDevice?.let { device ->
                Divider(
                    modifier = Modifier.padding(vertical = 12.dp),
                    color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.2f)
                )
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column {
                        Text(
                            text = "已连接设备:",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                        )
                        Text(
                            text = device.name ?: "未知设备",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }
                    
                    TextButton(onClick = onDisconnect) {
                        Text(
                            text = "断开连接",
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }
        }
    }
}

/**
 * 蓝牙权限卡片
 */
@Composable
fun BluetoothPermissionCard(
    bluetoothState: BluetoothState,
    bluetoothStatusMessage: String
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = when (bluetoothState) {
                    BluetoothState.POWERED_OFF -> Icons.Default.BluetoothDisabled
                    BluetoothState.UNAUTHORIZED -> Icons.Default.Warning
                    BluetoothState.UNSUPPORTED -> Icons.Default.Error
                    else -> Icons.Default.Bluetooth
                },
                contentDescription = "蓝牙状态",
                tint = MaterialTheme.colorScheme.onErrorContainer,
                modifier = Modifier.size(48.dp)
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = bluetoothStatusMessage,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onErrorContainer
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = when (bluetoothState) {
                    BluetoothState.POWERED_OFF -> "请在系统设置中开启蓝牙"
                    BluetoothState.UNAUTHORIZED -> "请在应用设置中允许蓝牙权限"
                    BluetoothState.UNSUPPORTED -> "您的设备不支持蓝牙功能"
                    else -> "请检查蓝牙设置"
                },
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onErrorContainer.copy(alpha = 0.8f)
            )
        }
    }
}

/**
 * 设备扫描区域
 */
@Composable
fun DeviceScanSection(
    discoveredDevices: List<BluetoothDevice>,
    isScanning: Boolean,
    connectedDevice: BluetoothDevice?,
    connectionStatus: ConnectionStatus,
    onStartScanning: () -> Unit,
    onStopScanning: () -> Unit,
    onConnectDevice: (BluetoothDevice) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // 标题和扫描按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Radar,
                        contentDescription = "设备扫描",
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(24.dp)
                    )
                    
                    Column {
                        Text(
                            text = "AbyBox 设备",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.SemiBold
                        )
                        Text(
                            text = "发现 ${discoveredDevices.size} 台设备",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                    }
                }

                // 扫描按钮
                Button(
                    onClick = if (isScanning) onStopScanning else onStartScanning,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (isScanning) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.primary
                    )
                ) {
                    if (isScanning) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp,
                            color = Color.White
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("停止")
                    } else {
                        Icon(
                            imageVector = Icons.Default.Search,
                            contentDescription = "搜索",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("扫描")
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 设备列表
            if (discoveredDevices.isEmpty()) {
                EmptyDeviceList()
            } else {
                LazyColumn(
                    modifier = Modifier.height(300.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    items(discoveredDevices) { device ->
                        DeviceRow(
                            device = device,
                            isConnected = connectedDevice?.address == device.address,
                            isConnecting = connectionStatus == ConnectionStatus.CONNECTING && 
                                          connectedDevice?.address == device.address,
                            onConnect = { onConnectDevice(device) }
                        )
                    }
                }
            }
        }
    }
}

/**
 * 空设备列表
 */
@Composable
fun EmptyDeviceList() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Default.SearchOff,
            contentDescription = "未发现设备",
            modifier = Modifier.size(48.dp),
            tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.4f)
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "未发现设备",
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        Text(
            text = "请确保 AbyBox 设备已开启\n并处于可发现模式",
            fontSize = 14.sp,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
            lineHeight = 20.sp
        )
    }
}

/**
 * 设备行
 */
@Composable
fun DeviceRow(
    device: BluetoothDevice,
    isConnected: Boolean,
    isConnecting: Boolean,
    onConnect: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isConnected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surface
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 设备图标
                Box(
                    modifier = Modifier
                        .size(50.dp)
                        .background(
                            brush = Brush.radialGradient(
                                colors = listOf(
                                    MaterialTheme.colorScheme.primary.copy(alpha = 0.2f),
                                    MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                                )
                            ),
                            shape = CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Devices,
                        contentDescription = "设备",
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(24.dp)
                    )
                }

                // 设备信息
                Column {
                    Text(
                        text = device.name ?: "未知设备",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold
                    )
                    Text(
                        text = device.address.takeLast(8),
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                    
                    if (isConnected) {
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(4.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Box(
                                modifier = Modifier
                                    .size(8.dp)
                                    .background(
                                        color = Color.Green,
                                        shape = CircleShape
                                    )
                            )
                            Text(
                                text = "已连接",
                                fontSize = 12.sp,
                                color = Color.Green,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                }
            }

            // 连接按钮
            Button(
                onClick = onConnect,
                enabled = !isConnecting && !isConnected,
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (isConnected) Color.Green else MaterialTheme.colorScheme.primary
                )
            ) {
                when {
                    isConnecting -> {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp,
                            color = Color.White
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("连接中")
                    }
                    isConnected -> Text("已连接")
                    else -> Text("连接")
                }
            }
        }
    }
}

/**
 * 设备激活区域
 */
@Composable
fun DeviceActivationSection(
    activationStatus: ActivationStatus,
    onActivateDevice: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = "激活",
                    tint = Color.Green
                )
                Text(
                    text = "设备激活",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = "点击下方按钮激活设备，激活后可进行WiFi配置",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )

            Spacer(modifier = Modifier.height(16.dp))

            Button(
                onClick = onActivateDevice,
                modifier = Modifier.fillMaxWidth(),
                enabled = activationStatus !is ActivationStatus.ACTIVATING && 
                         activationStatus !is ActivationStatus.SUCCESS,
                colors = ButtonDefaults.buttonColors(
                    containerColor = when (activationStatus) {
                        is ActivationStatus.SUCCESS -> Color.Green
                        is ActivationStatus.FAILED -> MaterialTheme.colorScheme.error
                        else -> MaterialTheme.colorScheme.primary
                    }
                )
            ) {
                when (activationStatus) {
                    is ActivationStatus.ACTIVATING -> {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp,
                            color = Color.White
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("激活中...")
                    }
                    is ActivationStatus.SUCCESS -> {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = "成功",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("激活成功")
                    }
                    is ActivationStatus.FAILED -> {
                        Icon(
                            imageVector = Icons.Default.Error,
                            contentDescription = "失败",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("重新激活")
                    }
                    else -> Text("激活设备")
                }
            }

            if (activationStatus is ActivationStatus.FAILED) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = activationStatus.message,
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.error
                )
            }
        }
    }
}

/**
 * WiFi配置区域
 */
@Composable
fun WiFiConfigSection(
    ssid: String,
    password: String,
    showPassword: Boolean,
    wifiSendStatus: SendStatus,
    onSsidChanged: (String) -> Unit,
    onPasswordChanged: (String) -> Unit,
    onTogglePasswordVisibility: () -> Unit,
    onSendWiFiConfig: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Wifi,
                    contentDescription = "WiFi",
                    tint = MaterialTheme.colorScheme.primary
                )
                Text(
                    text = "WiFi配置",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // SSID输入
            OutlinedTextField(
                value = ssid,
                onValueChange = onSsidChanged,
                label = { Text("WiFi网络名称") },
                placeholder = { Text("请输入WiFi网络名称") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.NetworkWifi,
                        contentDescription = "网络"
                    )
                }
            )

            Spacer(modifier = Modifier.height(12.dp))

            // 密码输入
            OutlinedTextField(
                value = password,
                onValueChange = onPasswordChanged,
                label = { Text("WiFi密码") },
                placeholder = { Text("请输入WiFi密码") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                visualTransformation = if (showPassword) VisualTransformation.None else PasswordVisualTransformation(),
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.Lock,
                        contentDescription = "密码"
                    )
                },
                trailingIcon = {
                    IconButton(onClick = onTogglePasswordVisibility) {
                        Icon(
                            imageVector = if (showPassword) Icons.Default.Visibility else Icons.Default.VisibilityOff,
                            contentDescription = if (showPassword) "隐藏密码" else "显示密码"
                        )
                    }
                }
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 发送按钮
            Button(
                onClick = onSendWiFiConfig,
                modifier = Modifier.fillMaxWidth(),
                enabled = ssid.isNotEmpty() && wifiSendStatus != SendStatus.SENDING,
                colors = ButtonDefaults.buttonColors(
                    containerColor = when (wifiSendStatus) {
                        SendStatus.SUCCESS -> Color.Green
                        SendStatus.FAILED -> MaterialTheme.colorScheme.error
                        else -> MaterialTheme.colorScheme.primary
                    }
                )
            ) {
                when (wifiSendStatus) {
                    SendStatus.SENDING -> {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp,
                            color = Color.White
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("发送中...")
                    }
                    SendStatus.SUCCESS -> {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = "成功",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("发送成功")
                    }
                    SendStatus.FAILED -> {
                        Icon(
                            imageVector = Icons.Default.Error,
                            contentDescription = "失败",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("重新发送")
                    }
                    else -> Text("发送WiFi配置")
                }
            }
        }
    }
}

// 辅助函数
@Composable
private fun bluetoothIcon(bluetoothState: BluetoothState, connectionStatus: ConnectionStatus): ImageVector {
    return when {
        bluetoothState != BluetoothState.POWERED_ON -> when (bluetoothState) {
            BluetoothState.POWERED_OFF -> Icons.Default.BluetoothDisabled
            BluetoothState.UNAUTHORIZED -> Icons.Default.Warning
            BluetoothState.UNSUPPORTED -> Icons.Default.Error
            else -> Icons.Default.Bluetooth
        }
        else -> when (connectionStatus) {
            ConnectionStatus.DISCONNECTED -> Icons.Default.Bluetooth
            ConnectionStatus.CONNECTING, ConnectionStatus.DISCOVERING -> Icons.Default.BluetoothSearching
            ConnectionStatus.CONNECTED, ConnectionStatus.READY -> Icons.Default.BluetoothConnected
        }
    }
}

@Composable
private fun statusColor(bluetoothState: BluetoothState, connectionStatus: ConnectionStatus): Color {
    return when {
        bluetoothState != BluetoothState.POWERED_ON -> when (bluetoothState) {
            BluetoothState.POWERED_OFF, BluetoothState.UNAUTHORIZED, BluetoothState.UNSUPPORTED -> MaterialTheme.colorScheme.error
            else -> Color(0xFFFF9800) // Orange color
        }
        else -> when (connectionStatus) {
            ConnectionStatus.DISCONNECTED -> Color.Gray
            ConnectionStatus.CONNECTING, ConnectionStatus.DISCOVERING -> Color(0xFFFF9800) // Orange color
            ConnectionStatus.CONNECTED, ConnectionStatus.READY -> Color.Green
        }
    }
}

private fun connectionStatusText(bluetoothState: BluetoothState, connectionStatus: ConnectionStatus): String {
    return when {
        bluetoothState != BluetoothState.POWERED_ON -> when (bluetoothState) {
            BluetoothState.POWERED_ON -> "蓝牙就绪"
            BluetoothState.POWERED_OFF -> "蓝牙已关闭"
            BluetoothState.UNAUTHORIZED -> "权限未授权"
            BluetoothState.UNSUPPORTED -> "不支持蓝牙"
            else -> "初始化中"
        }
        else -> when (connectionStatus) {
            ConnectionStatus.DISCONNECTED -> "未连接"
            ConnectionStatus.CONNECTING -> "连接中"
            ConnectionStatus.CONNECTED -> "已连接"
            ConnectionStatus.DISCOVERING -> "发现服务中"
            ConnectionStatus.READY -> "就绪"
        }
    }
} 