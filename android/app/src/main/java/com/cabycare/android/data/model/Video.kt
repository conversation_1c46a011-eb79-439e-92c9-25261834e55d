package com.cabycare.android.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * 视频质量枚举
 */
enum class VideoQuality(val value: String, val displayName: String, val resolution: String) {
    LOW("low", "流畅", "480p"),
    MEDIUM("medium", "标清", "720p"),
    HIGH("high", "高清", "1080p"),
    ULTRA("ultra", "超清", "4K");
    
    companion object {
        fun fromValue(value: String): VideoQuality {
            return values().find { it.value == value } ?: MEDIUM
        }
    }
}

/**
 * 视频流状态枚举
 */
enum class VideoStreamStatus(val value: String, val displayName: String) {
    IDLE("idle", "空闲"),
    CONNECTING("connecting", "连接中"),
    STREAMING("streaming", "直播中"),
    BUFFERING("buffering", "缓冲中"),
    ERROR("error", "错误"),
    DISCONNECTED("disconnected", "已断开");
    
    companion object {
        fun fromValue(value: String): VideoStreamStatus {
            return values().find { it.value == value } ?: IDLE
        }
    }
}

/**
 * 视频段模型
 */
@Serializable
data class VideoSegment(
    val id: String,
    @SerialName("device_id")
    val deviceId: String,
    @SerialName("start_time")
    val startTime: String,
    @SerialName("end_time")
    val endTime: String,
    val duration: Long, // 持续时间（秒）
    @SerialName("file_url")
    val fileUrl: String,
    @SerialName("thumbnail_url")
    val thumbnailUrl: String? = null,
    val quality: String = VideoQuality.MEDIUM.value,
    @SerialName("file_size")
    val fileSize: Long? = null, // 文件大小（字节）
    val status: String = "available", // available, processing, error
    @SerialName("created_at")
    val createdAt: String
) {
    /**
     * 获取视频质量枚举
     */
    val qualityEnum: VideoQuality
        get() = VideoQuality.fromValue(quality)
    
    /**
     * 是否可播放
     */
    val isPlayable: Boolean
        get() = status == "available"
    
    /**
     * 格式化的持续时间
     */
    val formattedDuration: String
        get() {
            val hours = duration / 3600
            val minutes = (duration % 3600) / 60
            val seconds = duration % 60
            
            return if (hours > 0) {
                String.format("%02d:%02d:%02d", hours, minutes, seconds)
            } else {
                String.format("%02d:%02d", minutes, seconds)
            }
        }
    
    /**
     * 格式化的文件大小
     */
    val formattedFileSize: String
        get() {
            return fileSize?.let { size ->
                when {
                    size < 1024 -> "${size}B"
                    size < 1024 * 1024 -> "${size / 1024}KB"
                    size < 1024 * 1024 * 1024 -> "${size / (1024 * 1024)}MB"
                    else -> "${size / (1024 * 1024 * 1024)}GB"
                }
            } ?: "未知"
        }
}

/**
 * 视频流信息模型
 */
@Serializable
data class VideoStreamInfo(
    @SerialName("device_id")
    val deviceId: String,
    @SerialName("stream_url")
    val streamUrl: String,
    @SerialName("hls_url")
    val hlsUrl: String? = null,
    @SerialName("rtmp_url")
    val rtmpUrl: String? = null,
    val quality: String = VideoQuality.MEDIUM.value,
    val status: String = VideoStreamStatus.IDLE.value,
    @SerialName("viewer_count")
    val viewerCount: Int = 0,
    @SerialName("bitrate")
    val bitrate: Int? = null, // 比特率 (kbps)
    @SerialName("frame_rate")
    val frameRate: Int? = null, // 帧率 (fps)
    @SerialName("resolution")
    val resolution: String? = null, // 分辨率，如 "1920x1080"
    @SerialName("last_updated")
    val lastUpdated: String
) {
    /**
     * 获取视频质量枚举
     */
    val qualityEnum: VideoQuality
        get() = VideoQuality.fromValue(quality)
    
    /**
     * 获取流状态枚举
     */
    val statusEnum: VideoStreamStatus
        get() = VideoStreamStatus.fromValue(status)
    
    /**
     * 是否正在直播
     */
    val isLive: Boolean
        get() = statusEnum == VideoStreamStatus.STREAMING
    
    /**
     * 获取最佳播放URL
     */
    val bestPlayUrl: String
        get() = hlsUrl ?: streamUrl
}

/**
 * 视频播放器状态模型
 */
@Serializable
data class VideoPlayerState(
    val isPlaying: Boolean = false,
    val isLoading: Boolean = false,
    val isFullscreen: Boolean = false,
    val isMuted: Boolean = false,
    val volume: Float = 1.0f,
    val currentPosition: Long = 0L, // 当前播放位置（毫秒）
    val duration: Long = 0L, // 总时长（毫秒）
    val bufferedPosition: Long = 0L, // 缓冲位置（毫秒）
    val playbackSpeed: Float = 1.0f,
    val quality: String = VideoQuality.MEDIUM.value,
    val error: String? = null
) {
    /**
     * 播放进度百分比
     */
    val progress: Float
        get() = if (duration > 0) currentPosition.toFloat() / duration else 0f
    
    /**
     * 缓冲进度百分比
     */
    val bufferedProgress: Float
        get() = if (duration > 0) bufferedPosition.toFloat() / duration else 0f
    
    /**
     * 是否有错误
     */
    val hasError: Boolean
        get() = error != null
    
    /**
     * 格式化的当前时间
     */
    val formattedCurrentTime: String
        get() = formatTime(currentPosition)
    
    /**
     * 格式化的总时长
     */
    val formattedDuration: String
        get() = formatTime(duration)
    
    private fun formatTime(timeMs: Long): String {
        val totalSeconds = timeMs / 1000
        val hours = totalSeconds / 3600
        val minutes = (totalSeconds % 3600) / 60
        val seconds = totalSeconds % 60
        
        return if (hours > 0) {
            String.format("%02d:%02d:%02d", hours, minutes, seconds)
        } else {
            String.format("%02d:%02d", minutes, seconds)
        }
    }
}

/**
 * 视频段列表响应模型
 */
@Serializable
data class VideoSegmentsResponse(
    val code: Int,
    val status: String,
    val message: String,
    val data: VideoSegmentListData? = null
) {
    @Serializable
    data class VideoSegmentListData(
        val segments: List<VideoSegment>,
        val total: Int,
        val page: Int,
        val pageSize: Int,
        val hasMore: Boolean
    )
}
