package com.cabycare.android.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * 家庭组角色枚举
 */
enum class FamilyGroupRole(val value: Int, val displayName: String) {
    OWNER(1, "所有者"),
    ADMIN(2, "管理员"),
    MEMBER(3, "成员");

    companion object {
        fun fromInt(value: Int): FamilyGroupRole {
            return values().find { it.value == value } ?: MEMBER
        }
    }
}

/**
 * 家庭组模型
 * 对应Swift版本的FamilyGroup
 */
@Serializable
data class FamilyGroup(
    @SerialName("group_id") val groupId: String,
    @SerialName("group_name") val groupName: String,
    val description: String? = null,
    @SerialName("owner_id") val ownerId: String,
    @SerialName("member_count") val memberCount: Int = 0,
    @SerialName("device_count") val deviceCount: Int = 0,
    @SerialName("created_at") val createdAt: String,
    @SerialName("updated_at") val updatedAt: String
)

/**
 * 家庭组成员模型
 */
@Serializable
data class FamilyGroupMember(
    @SerialName("member_id") val memberId: String,
    @SerialName("user_id") val userId: String,
    val username: String,
    val email: String,
    val role: Int, // 1=owner, 2=admin, 3=member
    @SerialName("joined_at") val joinedAt: String
) {
    /**
     * 角色显示名称
     */
    val roleDisplayName: String
        get() = when (role) {
            1 -> "所有者"
            2 -> "管理员"
            3 -> "成员"
            else -> "未知"
        }
}

/**
 * 家庭组列表响应模型
 * 对应Swift版本的FamilyGroupsResponse
 */
@Serializable
data class FamilyGroupsResponse(
    val status: String,
    val message: String,
    val data: List<FamilyGroup> = emptyList()
) {
    /**
     * 请求是否成功
     */
    val isSuccess: Boolean
        get() = status == "success"
}

/**
 * 创建家庭组请求模型
 */
@Serializable
data class CreateFamilyGroupRequest(
    @SerialName("group_name") val groupName: String,
    val description: String? = null
)

/**
 * 创建家庭组响应模型
 * 对应Swift版本的CreateFamilyGroupResponse
 */
@Serializable
data class CreateFamilyGroupResponse(
    val status: String,
    val message: String,
    val data: FamilyGroup
) {
    /**
     * 请求是否成功
     */
    val isSuccess: Boolean
        get() = status == "success"
}

/**
 * 家庭组详情响应模型
 */
@Serializable
data class FamilyGroupDetailResponse(
    val status: String,
    val message: String,
    val data: FamilyGroupDetail? = null
) {
    /**
     * 请求是否成功
     */
    val isSuccess: Boolean
        get() = status == "success"
}

/**
 * 家庭组详情模型
 */
@Serializable
data class FamilyGroupDetail(
    @SerialName("group_id") val groupId: String,
    @SerialName("group_name") val groupName: String,
    val description: String? = null,
    @SerialName("owner_id") val ownerId: String,
    val members: List<FamilyGroupMember> = emptyList(),
    val devices: List<AccessibleDevice> = emptyList(),
    @SerialName("created_at") val createdAt: String,
    @SerialName("updated_at") val updatedAt: String
)
