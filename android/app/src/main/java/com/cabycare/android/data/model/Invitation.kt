package com.cabycare.android.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * 邀请状态枚举
 */
enum class InvitationStatus(val value: Int, val displayName: String) {
    PENDING(0, "待处理"),
    ACCEPTED(1, "已接受"),
    REJECTED(2, "已拒绝"),
    EXPIRED(3, "已过期");
    
    companion object {
        fun fromValue(value: Int): InvitationStatus {
            return values().find { it.value == value } ?: PENDING
        }
    }
}

/**
 * 邀请模型
 * 对应Swift版本的Invitation模型
 */
@Serializable
data class Invitation(
    val id: String,
    val groupName: String,
    val inviterName: String,
    val role: Int,
    val status: Int
) {
    /**
     * 获取状态枚举
     */
    val statusEnum: InvitationStatus
        get() = InvitationStatus.fromValue(status)
    
    /**
     * 获取角色枚举
     */
    val roleEnum: FamilyGroupRole
        get() = FamilyGroupRole.fromInt(role)
    
    /**
     * 是否可以操作（接受/拒绝）
     */
    val canOperate: Boolean
        get() = statusEnum == InvitationStatus.PENDING
}

/**
 * 邀请列表API响应模型
 */
@Serializable
data class InvitationsResponse(
    val code: Int,
    val status: String,
    val message: String,
    val data: List<InvitationResponse>? = null
)

/**
 * 邀请响应模型
 */
@Serializable
data class InvitationResponse(
    @SerialName("invitation_id")
    val invitationId: String,
    @SerialName("group_id")
    val groupId: String,
    @SerialName("group_name")
    val groupName: String,
    @SerialName("inviter_id")
    val inviterId: String,
    @SerialName("inviter_name")
    val inviterName: String,
    @SerialName("invitee_id")
    val inviteeId: String,
    @SerialName("invitee_name")
    val inviteeName: String,
    val status: Int,
    val role: Int,
    @SerialName("created_at")
    val createdAt: String,
    @SerialName("expire_at")
    val expireAt: String? = null
) {
    /**
     * 转换为Invitation模型
     */
    fun toInvitation(): Invitation {
        return Invitation(
            id = invitationId,
            groupName = groupName,
            inviterName = inviterName,
            role = role,
            status = status
        )
    }
    
    /**
     * 获取状态枚举
     */
    val statusEnum: InvitationStatus
        get() = InvitationStatus.fromValue(status)
    
    /**
     * 获取角色枚举
     */
    val roleEnum: FamilyGroupRole
        get() = FamilyGroupRole.fromInt(role)
    
    /**
     * 是否已过期
     */
    val isExpired: Boolean
        get() {
            // TODO: 实现过期时间检查逻辑
            return false
        }
}

/**
 * 邀请操作请求模型
 */
@Serializable
data class InvitationActionRequest(
    @SerialName("invitation_id")
    val invitationId: String,
    val action: String // "accept" 或 "reject"
)

/**
 * 创建邀请请求模型
 */
@Serializable
data class CreateInvitationRequest(
    @SerialName("group_id")
    val groupId: String,
    @SerialName("invitee_email")
    val inviteeEmail: String,
    val role: Int,
    val message: String? = null
)

/**
 * 通用API响应模型
 */
@Serializable
data class ApiResponse<T>(
    val code: Int,
    val status: String? = null,
    val message: String,
    val data: T? = null,
    val error: String? = null
) {
    /**
     * 请求是否成功
     */
    val isSuccess: Boolean
        get() = code == 200 || code == 0
}
