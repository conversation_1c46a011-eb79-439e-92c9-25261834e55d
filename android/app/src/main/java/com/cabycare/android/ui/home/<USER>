package com.cabycare.android.ui.home

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.cabycare.android.ui.components.CircularCatAvatarView
import com.cabycare.android.data.local.UserPreferences
import okhttp3.OkHttpClient
import javax.inject.Inject

/**
 * 首页界面
 * 显示宠物统计信息和重要提醒
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    viewModel: HomeViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    LaunchedEffect(Unit) {
        viewModel.loadData()
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 顶部标题
        Text(
            text = "CabyCare",
            fontSize = 28.sp,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(bottom = 24.dp)
        )
        
        when {
            uiState.isLoading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
            
            uiState.error != null -> {
                ErrorCard(
                    error = uiState.error ?: "Unknown error",
                    onRetry = { viewModel.loadData() }
                )
            }
            
            else -> {
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // 重点关注卡片（仅在有问题时显示）
                    if (uiState.alertCats.isNotEmpty()) {
                        item {
                            AlertCard(alertCats = uiState.alertCats)
                        }
                    }
                    
                    // 每只猫的统计卡片
                    items(uiState.catStats) { catStat ->
                        CatStatisticsCard(
                            catStat = catStat,
                            userPreferences = viewModel.userPreferences,
                            okHttpClient = viewModel.okHttpClient
                        )
                    }
                    
                    // 空状态
                    if (uiState.catStats.isEmpty() && uiState.alertCats.isEmpty()) {
                        item {
                            EmptyStateCard()
                        }
                    }
                }
            }
        }
    }
}

/**
 * 警报卡片
 */
@Composable
fun AlertCard(alertCats: List<CatAlert>) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Warning,
                    contentDescription = "警告",
                    tint = MaterialTheme.colorScheme.error,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "需要关注",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onErrorContainer
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            alertCats.forEach { alert ->
                Row(
                    modifier = Modifier.padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Pets,
                        contentDescription = "宠物",
                        tint = MaterialTheme.colorScheme.onErrorContainer,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "${alert.catName}: ${alert.message}",
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
        }
    }
}

/**
 * 猫咪统计卡片 - 显示真实的如厕统计数据
 */
@Composable
fun CatStatisticsCard(
    catStat: CatStatistic,
    userPreferences: UserPreferences,
    okHttpClient: OkHttpClient
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 猫咪基本信息
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 猫咪头像 - 使用真实的头像而不是图标
                CircularCatAvatarView(
                    avatarUrl = catStat.avatarUrl,
                    size = 48.dp,
                    userPreferences = userPreferences,
                    okHttpClient = okHttpClient
                )
                Spacer(modifier = Modifier.width(12.dp))
                Column {
                    Text(
                        text = catStat.name,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.SemiBold
                    )
                    Text(
                        text = "${catStat.age} • ${catStat.gender}",
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
                Spacer(modifier = Modifier.weight(1f))
                HealthStatusBadge(status = catStat.healthStatus)
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 如厕统计信息 - 显示真实数据
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatItem(
                    icon = Icons.Default.Schedule, // 使用时钟图标表示如厕次数
                    label = "今日如厕",
                    value = "${catStat.todayActivity}次",
                    valueColor = when {
                        catStat.todayActivity == 0 -> MaterialTheme.colorScheme.error
                        catStat.todayActivity <= 2 -> Color(0xFFFF9500) // 橙色：偏低
                        catStat.todayActivity in 3..8 -> MaterialTheme.colorScheme.primary // 正常
                        else -> Color(0xFFFF6B35) // 橙红色：偏高
                    }
                )
                StatItem(
                    icon = Icons.Default.Favorite,
                    label = "健康评分",
                    value = "${catStat.healthScore}分",
                    valueColor = when {
                        catStat.healthScore >= 85 -> Color(0xFF4CAF50) // 绿色：优秀
                        catStat.healthScore >= 70 -> MaterialTheme.colorScheme.primary // 蓝色：良好
                        catStat.healthScore >= 50 -> Color(0xFFFF9500) // 橙色：一般
                        else -> MaterialTheme.colorScheme.error // 红色：需要关注
                    }
                )
                StatItem(
                    icon = Icons.Default.TrendingUp,
                    label = "活跃度",
                    value = catStat.activityLevel,
                    valueColor = when (catStat.activityLevel) {
                        "无活动" -> MaterialTheme.colorScheme.error
                        "低" -> Color(0xFFFF9500)
                        "中等", "正常" -> MaterialTheme.colorScheme.primary
                        "活跃", "非常活跃" -> Color(0xFF4CAF50)
                        else -> MaterialTheme.colorScheme.outline
                    }
                )
            }
            
            // 如果有异常状态，显示更多提示信息
            if (catStat.todayActivity == 0 || catStat.healthScore < 50) {
                Spacer(modifier = Modifier.height(12.dp))
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            color = MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.3f),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(horizontal = 12.dp, vertical = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Info,
                        contentDescription = "提示",
                        tint = MaterialTheme.colorScheme.error,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = when {
                            catStat.todayActivity == 0 -> "今日尚未检测到如厕活动"
                            catStat.healthScore < 50 -> "健康状况需要关注"
                            else -> "状态异常，请密切观察"
                        },
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.error,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

/**
 * 健康状态徽章
 */
@Composable
fun HealthStatusBadge(status: String) {
    val (color, text) = when (status) {
        "健康" -> MaterialTheme.colorScheme.primary to "健康"
        "需要关注" -> Color(0xFFFF9500) to "关注"
        "生病" -> MaterialTheme.colorScheme.error to "生病"
        else -> MaterialTheme.colorScheme.outline to "未知"
    }
    
    Surface(
        color = color.copy(alpha = 0.1f),
        shape = RoundedCornerShape(8.dp)
    ) {
        Text(
            text = text,
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
            fontSize = 12.sp,
            color = color,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 统计项目 - 支持自定义颜色
 */
@Composable
fun StatItem(
    icon: ImageVector,
    label: String,
    value: String,
    valueColor: Color = MaterialTheme.colorScheme.onSurface
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(20.dp)
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = value,
            fontSize = 16.sp,
            fontWeight = FontWeight.SemiBold,
            color = valueColor
        )
        Text(
            text = label,
            fontSize = 12.sp,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )
    }
}

/**
 * 错误卡片
 */
@Composable
fun ErrorCard(
    error: String,
    onRetry: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Error,
                contentDescription = "错误",
                tint = MaterialTheme.colorScheme.error,
                modifier = Modifier.size(48.dp)
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "加载失败",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onErrorContainer
            )
            Text(
                text = error,
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onErrorContainer
            )
            Spacer(modifier = Modifier.height(16.dp))
            Button(onClick = onRetry) {
                Text("重试")
            }
        }
    }
}

/**
 * 空状态卡片
 */
@Composable
fun EmptyStateCard() {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Pets,
                contentDescription = "空状态",
                tint = MaterialTheme.colorScheme.outline,
                modifier = Modifier.size(64.dp)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "欢迎使用CabyCare",
                fontSize = 20.sp,
                fontWeight = FontWeight.SemiBold
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "添加您的第一只宠物开始使用",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
        }
    }
}
