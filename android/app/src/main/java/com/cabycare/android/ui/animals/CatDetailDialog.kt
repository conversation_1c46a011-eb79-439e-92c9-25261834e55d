package com.cabycare.android.ui.animals

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import coil.compose.AsyncImage
import com.cabycare.android.data.model.CatProfile
import com.cabycare.android.ui.components.CatAvatarView
import com.cabycare.android.data.local.UserPreferences
import okhttp3.OkHttpClient

/**
 * 猫咪详情对话框
 */
@Composable
fun CatDetailDialog(
    cat: CatProfile,
    onDismiss: () -> Unit,
    userPreferences: UserPreferences,
    okHttpClient: OkHttpClient
) {
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
                    .verticalScroll(rememberScrollState())
            ) {
                // 头部信息
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 24.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 头像 - 使用新的带认证的CatAvatarView
                    CatAvatarView(
                        avatarUrl = cat.avatarUrl,
                        size = 80.dp,
                        cornerRadius = 40.dp, // 圆形头像
                        userPreferences = userPreferences,
                        okHttpClient = okHttpClient
                    )
                    
                    Spacer(modifier = Modifier.width(16.dp))
                    
                    // 基本信息
                    Column {
                        Text(
                            text = cat.name,
                            fontSize = 24.sp,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = "${cat.age} • ${cat.genderEnum.displayName}",
                            fontSize = 16.sp,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                        Text(
                            text = cat.typeEnum.displayName,
                            fontSize = 14.sp,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                    }
                }
                
                // 详细信息卡片
                DetailInfoCard(
                    title = "基本信息",
                    items = listOf(
                        DetailItem(Icons.Default.MonitorWeight, "体重", cat.weight),
                        DetailItem(Icons.Default.Cake, "年龄", cat.age),
                        DetailItem(Icons.Default.Pets, "类型", cat.typeEnum.displayName),
                        DetailItem(Icons.Default.Wc, "性别", cat.genderEnum.displayName)
                    )
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 健康信息卡片
                DetailInfoCard(
                    title = "健康信息",
                    items = listOf(
                        DetailItem(Icons.Default.Favorite, "健康状态", cat.healthStatusEnum.displayName),
                        DetailItem(Icons.Default.TrendingUp, "活跃度", cat.activityLevelEnum.displayName),
                        DetailItem(Icons.Default.MedicalServices, "绝育状态", cat.neuteredStatus ?: "未记录")
                    )
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 关闭按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                    Text("关闭")
                    }
                }
            }
        }
    }
}

/**
 * 详细信息卡片
 */
@Composable
private fun DetailInfoCard(
    title: String,
    items: List<DetailItem>
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            items.forEachIndexed { index, item ->
                DetailInfoRow(item = item)
                if (index < items.size - 1) {
                    Divider(
                        modifier = Modifier.padding(vertical = 8.dp),
                        color = MaterialTheme.colorScheme.outline.copy(alpha = 0.2f)
                    )
                }
            }
        }
    }
}

/**
 * 详细信息行
 */
@Composable
private fun DetailInfoRow(item: DetailItem) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = item.icon,
            contentDescription = item.label,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(20.dp)
        )
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Text(
            text = item.label,
            fontSize = 14.sp,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
            modifier = Modifier.weight(1f)
        )
        
        Text(
            text = item.value,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurface
        )
    }
}

/**
 * 详细信息项
 */
data class DetailItem(
    val icon: ImageVector,
    val label: String,
    val value: String
)
