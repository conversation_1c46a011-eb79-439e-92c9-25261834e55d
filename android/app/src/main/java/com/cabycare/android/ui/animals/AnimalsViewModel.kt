package com.cabycare.android.ui.animals

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cabycare.android.data.model.CatProfile
import com.cabycare.android.data.network.NetworkResult
import com.cabycare.android.data.repository.CatRepository
import com.cabycare.android.data.local.UserPreferences
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import okhttp3.OkHttpClient
import javax.inject.Inject

/**
 * 宠物管理UI状态
 */
data class AnimalsUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val cats: List<CatProfile> = emptyList(),
    val showAddEditDialog: Boolean = false,
    val showDetailDialog: Boolean = false,
    val editingCat: CatProfile? = null,
    val selectedCat: CatProfile? = null,
    val isSubmitting: Boolean = false
)

/**
 * 宠物管理ViewModel
 */
@HiltViewModel
class AnimalsViewModel @Inject constructor(
    private val catRepository: CatRepository,
    val userPreferences: UserPreferences,
    val okHttpClient: OkHttpClient
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(AnimalsUiState())
    val uiState: StateFlow<AnimalsUiState> = _uiState.asStateFlow()
    
    /**
     * 加载猫咪列表
     */
    fun loadCats() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            when (val result = catRepository.getCats()) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        cats = result.data,
                        error = null
                    )
                }
                is NetworkResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = result.exception.message ?: "加载猫咪列表失败"
                    )
                }
                else -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "未知错误"
                    )
                }
            }
        }
    }
    
    /**
     * 显示添加猫咪对话框
     */
    fun showAddCatDialog() {
        _uiState.value = _uiState.value.copy(
            showAddEditDialog = true,
            editingCat = null
        )
    }
    
    /**
     * 隐藏添加/编辑猫咪对话框
     */
    fun hideAddCatDialog() {
        _uiState.value = _uiState.value.copy(
            showAddEditDialog = false,
            editingCat = null,
            isSubmitting = false
        )
    }
    
    /**
     * 编辑猫咪
     */
    fun editCat(cat: CatProfile) {
        _uiState.value = _uiState.value.copy(
            showAddEditDialog = true,
            editingCat = cat
        )
    }
    
    /**
     * 保存猫咪（新增或更新）
     */
    fun saveCat(cat: CatProfile) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isSubmitting = true, error = null)
            
            val result = if (_uiState.value.editingCat != null) {
                // 更新现有猫咪
                catRepository.updateCat(cat.id, cat)
            } else {
                // 创建新猫咪
                catRepository.createCat(cat)
            }
            
            when (result) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(
                        isSubmitting = false,
                        showAddEditDialog = false,
                        editingCat = null
                    )
                    // 重新加载列表
                    loadCats()
                }
                is NetworkResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isSubmitting = false,
                        error = result.exception.message ?: "保存失败"
                    )
                }
                else -> {
                    _uiState.value = _uiState.value.copy(
                        isSubmitting = false,
                        error = "未知错误"
                    )
                }
            }
        }
    }
    
    /**
     * 删除猫咪
     */
    fun deleteCat(catId: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            when (val result = catRepository.deleteCat(catId)) {
                is NetworkResult.Success -> {
                    // 重新加载列表
                    loadCats()
                }
                is NetworkResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = result.exception.message ?: "删除失败"
                    )
                }
                else -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "未知错误"
                    )
                }
            }
        }
    }
    
    /**
     * 显示猫咪详情
     */
    fun showCatDetail(cat: CatProfile) {
        _uiState.value = _uiState.value.copy(
            selectedCat = cat,
            showDetailDialog = true
        )
    }
    
    /**
     * 隐藏猫咪详情
     */
    fun hideCatDetail() {
        _uiState.value = _uiState.value.copy(
            selectedCat = null,
            showDetailDialog = false
        )
    }
}
