package com.cabycare.android.bluetooth

import android.Manifest
import android.annotation.SuppressLint
import android.bluetooth.*
import android.bluetooth.le.*
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import android.util.Log
import androidx.core.content.ContextCompat
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * 蓝牙管理器 - 负责扫描、连接、通信等蓝牙操作
 * 基于iOS版本的BluetoothManager实现
 */
@Singleton
class BluetoothManager @Inject constructor(
    private val context: Context
) {
    companion object {
        private const val TAG = "BluetoothManager"
        
        // 服务和特征UUID（根据实际设备修改）
        private const val DEVICE_SERVICE_UUID = "12345678-1234-1234-1234-123456789abc"
        private const val WIFI_CONFIG_CHAR_UUID = "12345678-1234-1234-1234-123456789abd"
        private const val USER_ID_CHAR_UUID = "12345678-1234-1234-1234-123456789abe"
        private const val ACTIVATION_CHAR_UUID = "12345678-1234-1234-1234-123456789abf"
        
        // 扫描时间限制
        private const val SCAN_PERIOD = 10000L // 10秒
    }
    
    private val bluetoothManager = context.getSystemService(Context.BLUETOOTH_SERVICE) as android.bluetooth.BluetoothManager
    private val bluetoothAdapter: BluetoothAdapter? = bluetoothManager.adapter
    private val json = Json { 
        ignoreUnknownKeys = true
        encodeDefaults = true
    }
    
    // 状态流
    private val _bluetoothState = MutableStateFlow(BluetoothState.UNKNOWN)
    val bluetoothState: StateFlow<BluetoothState> = _bluetoothState.asStateFlow()
    
    private val _connectionStatus = MutableStateFlow(ConnectionStatus.DISCONNECTED)
    val connectionStatus: StateFlow<ConnectionStatus> = _connectionStatus.asStateFlow()
    
    private val _discoveredDevices = MutableStateFlow<List<BluetoothDevice>>(emptyList())
    val discoveredDevices: StateFlow<List<BluetoothDevice>> = _discoveredDevices.asStateFlow()
    
    private val _isScanning = MutableStateFlow(false)
    val isScanning: StateFlow<Boolean> = _isScanning.asStateFlow()
    
    private val _connectedDevice = MutableStateFlow<BluetoothDevice?>(null)
    val connectedDevice: StateFlow<BluetoothDevice?> = _connectedDevice.asStateFlow()
    
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    private val _wifiSendStatus = MutableStateFlow(SendStatus.IDLE)
    val wifiSendStatus: StateFlow<SendStatus> = _wifiSendStatus.asStateFlow()
    
    private val _userIdSendStatus = MutableStateFlow(SendStatus.IDLE)
    val userIdSendStatus: StateFlow<SendStatus> = _userIdSendStatus.asStateFlow()
    
    private val _activationStatus = MutableStateFlow<ActivationStatus>(ActivationStatus.IDLE)
    val activationStatus: StateFlow<ActivationStatus> = _activationStatus.asStateFlow()
    
    private val _bluetoothStatusMessage = MutableStateFlow("")
    val bluetoothStatusMessage: StateFlow<String> = _bluetoothStatusMessage.asStateFlow()

    // GATT相关
    private var bluetoothGatt: BluetoothGatt? = null
    private var wifiConfigCharacteristic: BluetoothGattCharacteristic? = null
    private var userIdCharacteristic: BluetoothGattCharacteristic? = null
    private var activationCharacteristic: BluetoothGattCharacteristic? = null
    
    // 扫描相关
    private var bluetoothLeScanner: BluetoothLeScanner? = null
    private var scanHandler: android.os.Handler? = null
    
    init {
        updateBluetoothState()
    }

    /**
     * 更新蓝牙状态
     */
    private fun updateBluetoothState() {
        when {
            bluetoothAdapter == null -> {
                _bluetoothState.value = BluetoothState.UNSUPPORTED
                _bluetoothStatusMessage.value = "设备不支持蓝牙功能"
            }
            !bluetoothAdapter.isEnabled -> {
                _bluetoothState.value = BluetoothState.POWERED_OFF
                _bluetoothStatusMessage.value = "蓝牙已关闭，请开启蓝牙"
            }
            !hasBluetoothPermissions() -> {
                _bluetoothState.value = BluetoothState.UNAUTHORIZED
                _bluetoothStatusMessage.value = "需要蓝牙权限才能继续"
            }
            else -> {
                _bluetoothState.value = BluetoothState.POWERED_ON
                _bluetoothStatusMessage.value = "蓝牙就绪"
                bluetoothLeScanner = bluetoothAdapter.bluetoothLeScanner
            }
        }
    }

    /**
     * 检查蓝牙权限
     */
    private fun hasBluetoothPermissions(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            ContextCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED &&
            ContextCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED
        } else {
            ContextCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * 开始扫描设备
     */
    @SuppressLint("MissingPermission")
    fun startScanning() {
        updateBluetoothState()
        
        if (_bluetoothState.value != BluetoothState.POWERED_ON) {
            _errorMessage.value = "蓝牙状态不正确，无法开始扫描"
            return
        }
        
        if (_isScanning.value) {
            Log.d(TAG, "已经在扫描中，跳过")
            return
        }
        
        bluetoothLeScanner?.let { scanner ->
            _isScanning.value = true
            _discoveredDevices.value = emptyList()
            
            val scanFilter = ScanFilter.Builder()
                .setDeviceName("AbyBox") // 根据实际设备名称修改
                .build()
            
            val scanSettings = ScanSettings.Builder()
                .setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY)
                .build()
            
            scanner.startScan(listOf(scanFilter), scanSettings, scanCallback)
            
            // 设置扫描超时
            scanHandler = android.os.Handler(android.os.Looper.getMainLooper())
            scanHandler?.postDelayed({
                stopScanning()
            }, SCAN_PERIOD)
            
            Log.d(TAG, "开始扫描设备")
        } ?: run {
            _errorMessage.value = "蓝牙扫描器不可用"
        }
    }

    /**
     * 停止扫描设备
     */
    @SuppressLint("MissingPermission")
    fun stopScanning() {
        if (_isScanning.value) {
            bluetoothLeScanner?.stopScan(scanCallback)
            _isScanning.value = false
            scanHandler?.removeCallbacksAndMessages(null)
            Log.d(TAG, "停止扫描设备")
        }
    }

    /**
     * 扫描回调
     */
    private val scanCallback = object : ScanCallback() {
        override fun onScanResult(callbackType: Int, result: ScanResult) {
            super.onScanResult(callbackType, result)
            
            val device = result.device
            val currentDevices = _discoveredDevices.value.toMutableList()
            
            // 避免重复添加
            if (!currentDevices.any { it.address == device.address }) {
                currentDevices.add(device)
                _discoveredDevices.value = currentDevices
                Log.d(TAG, "发现设备: ${device.name ?: "Unknown"} (${device.address})")
            }
        }

        override fun onScanFailed(errorCode: Int) {
            super.onScanFailed(errorCode)
            _isScanning.value = false
            _errorMessage.value = "扫描失败，错误代码: $errorCode"
            Log.e(TAG, "扫描失败: $errorCode")
        }
    }

    /**
     * 连接到设备
     */
    @SuppressLint("MissingPermission")
    fun connectToDevice(device: BluetoothDevice) {
        // 停止扫描
        stopScanning()
        
        // 如果已经连接到设备，先断开
        disconnectDevice()
        
        _connectionStatus.value = ConnectionStatus.CONNECTING
        _connectedDevice.value = device
        
        bluetoothGatt = device.connectGatt(context, false, gattCallback)
        Log.d(TAG, "开始连接设备: ${device.name ?: "Unknown"}")
    }

    /**
     * 断开设备连接
     */
    @SuppressLint("MissingPermission")
    fun disconnectDevice() {
        bluetoothGatt?.disconnect()
        bluetoothGatt?.close()
        bluetoothGatt = null
        
        _connectionStatus.value = ConnectionStatus.DISCONNECTED
        _connectedDevice.value = null
        
        // 清空特征引用
        wifiConfigCharacteristic = null
        userIdCharacteristic = null
        activationCharacteristic = null
        
        Log.d(TAG, "断开设备连接")
    }

    /**
     * GATT回调
     */
    private val gattCallback = object : BluetoothGattCallback() {
        @SuppressLint("MissingPermission")
        override fun onConnectionStateChange(gatt: BluetoothGatt, status: Int, newState: Int) {
            when (newState) {
                BluetoothProfile.STATE_CONNECTED -> {
                    _connectionStatus.value = ConnectionStatus.CONNECTED
                    Log.d(TAG, "已连接到设备")
                    gatt.discoverServices()
                }
                BluetoothProfile.STATE_DISCONNECTED -> {
                    _connectionStatus.value = ConnectionStatus.DISCONNECTED
                    _connectedDevice.value = null
                    Log.d(TAG, "设备已断开连接")
                }
            }
        }

        override fun onServicesDiscovered(gatt: BluetoothGatt, status: Int) {
            if (status == BluetoothGatt.GATT_SUCCESS) {
                _connectionStatus.value = ConnectionStatus.DISCOVERING
                
                // 查找所需的服务和特征
                val service = gatt.getService(UUID.fromString(DEVICE_SERVICE_UUID))
                if (service != null) {
                    wifiConfigCharacteristic = service.getCharacteristic(UUID.fromString(WIFI_CONFIG_CHAR_UUID))
                    userIdCharacteristic = service.getCharacteristic(UUID.fromString(USER_ID_CHAR_UUID))
                    activationCharacteristic = service.getCharacteristic(UUID.fromString(ACTIVATION_CHAR_UUID))
                    
                    if (wifiConfigCharacteristic != null && userIdCharacteristic != null && activationCharacteristic != null) {
                        _connectionStatus.value = ConnectionStatus.READY
                        Log.d(TAG, "服务发现完成，设备就绪")
                    } else {
                        _errorMessage.value = "设备不支持所需的功能"
                        disconnectDevice()
                    }
                } else {
                    _errorMessage.value = "未找到设备服务"
                    disconnectDevice()
                }
            } else {
                _errorMessage.value = "服务发现失败"
                disconnectDevice()
            }
        }

        override fun onCharacteristicWrite(gatt: BluetoothGatt, characteristic: BluetoothGattCharacteristic, status: Int) {
            when (characteristic.uuid.toString()) {
                WIFI_CONFIG_CHAR_UUID -> {
                    _wifiSendStatus.value = if (status == BluetoothGatt.GATT_SUCCESS) {
                        SendStatus.SUCCESS
                    } else {
                        SendStatus.FAILED
                    }
                }
                USER_ID_CHAR_UUID -> {
                    _userIdSendStatus.value = if (status == BluetoothGatt.GATT_SUCCESS) {
                        SendStatus.SUCCESS
                    } else {
                        SendStatus.FAILED
                    }
                }
                ACTIVATION_CHAR_UUID -> {
                    _activationStatus.value = if (status == BluetoothGatt.GATT_SUCCESS) {
                        ActivationStatus.SUCCESS("设备激活成功")
                    } else {
                        ActivationStatus.FAILED("设备激活失败")
                    }
                }
            }
        }
    }

    /**
     * 发送WiFi配置
     */
    @SuppressLint("MissingPermission")
    fun sendWiFiConfig(ssid: String, password: String) {
        val characteristic = wifiConfigCharacteristic
        val gatt = bluetoothGatt
        
        if (characteristic == null || gatt == null) {
            _wifiSendStatus.value = SendStatus.FAILED
            _errorMessage.value = "设备未连接或不支持WiFi配置"
            return
        }
        
        _wifiSendStatus.value = SendStatus.SENDING
        
        val wifiConfig = WiFiConfig(ssid, password)
        
        // 发送WiFi配置到设备
        val jsonData = json.encodeToString(wifiConfig)
        characteristic.value = jsonData.toByteArray()
        
        gatt.writeCharacteristic(characteristic)
        Log.d(TAG, "发送WiFi配置: $ssid")
    }

    /**
     * 发送用户ID
     */
    @SuppressLint("MissingPermission")
    fun sendUserId(userId: String) {
        val characteristic = userIdCharacteristic
        val gatt = bluetoothGatt
        
        if (characteristic == null || gatt == null) {
            _userIdSendStatus.value = SendStatus.FAILED
            _errorMessage.value = "设备未连接或不支持用户ID设置"
            return
        }
        
        _userIdSendStatus.value = SendStatus.SENDING
        
        val userIdData = UserIdData(userId)
        val jsonData = json.encodeToString(userIdData)
        characteristic.value = jsonData.toByteArray()
        
        gatt.writeCharacteristic(characteristic)
        Log.d(TAG, "发送用户ID: $userId")
    }

    /**
     * 激活设备
     */
    @SuppressLint("MissingPermission")
    suspend fun activateDevice() {
        val characteristic = activationCharacteristic
        val gatt = bluetoothGatt
        
        if (characteristic == null || gatt == null) {
            _activationStatus.value = ActivationStatus.FAILED("设备未连接或不支持激活功能")
            return
        }
        
        _activationStatus.value = ActivationStatus.ACTIVATING("正在激活设备...")
        
        try {
            val activationCommand = ActivationCommand()
            val jsonData = json.encodeToString(activationCommand)
            characteristic.value = jsonData.toByteArray()
            
            val success = suspendCoroutine<Boolean> { continuation ->
                // 这里应该实现实际的激活逻辑
                // 模拟激活过程
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    continuation.resume(true)
                }, 2000)
            }
            
            if (success) {
                gatt.writeCharacteristic(characteristic)
                Log.d(TAG, "设备激活命令已发送")
            } else {
                _activationStatus.value = ActivationStatus.FAILED("激活命令发送失败")
            }
        } catch (e: Exception) {
            _activationStatus.value = ActivationStatus.FAILED("激活过程中发生错误: ${e.message}")
            Log.e(TAG, "设备激活失败", e)
        }
    }

    /**
     * 清除错误消息
     */
    fun clearError() {
        _errorMessage.value = null
    }

    /**
     * WiFi配置数据类
     */
    @Serializable
    data class WiFiConfig(
        val ssid: String,
        val password: String
    )

    /**
     * 用户ID数据类
     */
    @Serializable
    data class UserIdData(
        val userId: String
    )

    /**
     * 设备激活命令数据类
     */
    @Serializable
    data class ActivationCommand(
        val command: String = "activate"
    )
}

/**
 * 蓝牙状态枚举
 */
enum class BluetoothState {
    UNKNOWN,        // 未知状态
    POWERED_ON,     // 蓝牙已开启
    POWERED_OFF,    // 蓝牙已关闭
    UNAUTHORIZED,   // 权限未授权
    UNSUPPORTED     // 不支持蓝牙
}

/**
 * 连接状态枚举
 */
enum class ConnectionStatus {
    DISCONNECTED,   // 未连接
    CONNECTING,     // 连接中
    CONNECTED,      // 已连接
    DISCOVERING,    // 发现服务中
    READY          // 就绪
}

/**
 * 发送状态枚举
 */
enum class SendStatus {
    IDLE,      // 空闲
    SENDING,   // 发送中
    SUCCESS,   // 发送成功
    FAILED     // 发送失败
}

/**
 * 激活状态密封类
 */
sealed class ActivationStatus {
    object IDLE : ActivationStatus()
    data class ACTIVATING(val message: String) : ActivationStatus()
    data class SUCCESS(val message: String) : ActivationStatus()
    data class FAILED(val message: String) : ActivationStatus()
} 