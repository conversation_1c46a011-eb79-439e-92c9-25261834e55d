package com.cabycare.android.ui.device

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.cabycare.android.ui.bluetooth.BluetoothScreen
import com.cabycare.android.data.model.DeviceInfo

/**
 * 添加设备对话框 - 使用蓝牙扫描添加设备
 * 基于iOS实现，使用蓝牙而不是手动输入
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddDeviceDialog(
    onDismiss: () -> Unit,
    onAdd: (DeviceInfo) -> Unit
) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            usePlatformDefaultWidth = false,
            dismissOnBackPress = true,
            dismissOnClickOutside = false
        )
    ) {
        Card(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier.fillMaxSize()
            ) {
                // 标题栏
                TopAppBar(
                    title = {
                        Text(
                            text = "添加新设备",
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Bold
                        )
                    },
                    navigationIcon = {
                        IconButton(onClick = onDismiss) {
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "关闭"
                            )
                        }
                    }
                )

                // 蓝牙配置界面
                BluetoothScreen(
                    onDeviceAdded = {
                        // 设备添加成功后的回调
                        // 这里可以创建一个DeviceInfo对象并调用onAdd
                        // 由于蓝牙添加流程完成后设备已经配置好，我们可以传递一个空的DeviceInfo
                        val deviceInfo = DeviceInfo(
                            name = "AbyBox Device", // 可以从蓝牙设备名称获取
                            model = "AbyBox Pro", // 默认型号
                            hardwareSn = "", // 从蓝牙设备获取
                            timezone = "Asia/Shanghai" // 默认时区
                        )
                        onAdd(deviceInfo)
                        onDismiss()
                    }
                )
            }
        }
    }
}

/**
 * 蓝牙添加设备成功后的信息展示
 */
@Composable
fun DeviceAddedSuccessDialog(
    deviceName: String,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = "成功",
                    tint = MaterialTheme.colorScheme.primary
                )
                Text("设备添加成功")
            }
        },
        text = {
            Column {
                Text("设备 \"$deviceName\" 已成功添加到您的账户。")
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "设备配置已完成，您现在可以开始使用设备功能。",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
        },
        confirmButton = {
            Button(onClick = onDismiss) {
                Text("确定")
            }
        }
    )
}

/**
 * 设备配对说明对话框
 */
@Composable
fun DevicePairingInstructionsDialog(
    onDismiss: () -> Unit,
    onProceed: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Info,
                    contentDescription = "信息",
                    tint = MaterialTheme.colorScheme.primary
                )
                Text("设备配对说明")
            }
        },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text("在开始配对之前，请确保：")
                
                PairingStep(
                    step = "1",
                    title = "设备已通电",
                    description = "确保您的AbyBox设备已连接电源并正常启动"
                )
                
                PairingStep(
                    step = "2",
                    title = "激活配对模式",
                    description = "长按设备上的配对按钮3秒，直到指示灯开始闪烁"
                )
                
                PairingStep(
                    step = "3",
                    title = "保持距离",
                    description = "确保手机与设备距离在3米以内，避免其他蓝牙设备干扰"
                )
            }
        },
        confirmButton = {
            Button(onClick = onProceed) {
                Text("开始配对")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 配对步骤项目
 */
@Composable
fun PairingStep(
    step: String,
    title: String,
    description: String
) {
    Row(
        verticalAlignment = Alignment.Top,
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Surface(
            modifier = Modifier.size(24.dp),
            shape = RoundedCornerShape(12.dp),
            color = MaterialTheme.colorScheme.primary
        ) {
            Box(
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = step,
                    color = MaterialTheme.colorScheme.onPrimary,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }
        
        Column {
            Text(
                text = title,
                fontSize = 14.sp,
                fontWeight = FontWeight.SemiBold
            )
            Text(
                text = description,
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
        }
    }
}
