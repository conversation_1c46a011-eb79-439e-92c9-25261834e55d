package com.cabycare.android.debug

import android.util.Log
import com.cabycare.android.data.auth.LogtoConfig

/**
 * Logto调试助手
 * 用于诊断登录问题
 */
object LogtoDebugHelper {
    private const val TAG = "LogtoDebug"
    
    /**
     * 打印Logto配置信息
     */
    fun printConfig() {
        Log.d(TAG, "=== Logto配置信息 ===")
        Log.d(TAG, "LOGTO_ENDPOINT: ${LogtoConfig.LOGTO_ENDPOINT}")
        Log.d(TAG, "CLIENT_ID: ${LogtoConfig.CLIENT_ID}")
        Log.d(TAG, "REDIRECT_URI: ${LogtoConfig.REDIRECT_URI}")
        Log.d(TAG, "API_BASE_URL: ${LogtoConfig.API_BASE_URL}")
        Log.d(TAG, "SCOPES: ${LogtoConfig.SCOPES}")
        Log.d(TAG, "RESOURCES: ${LogtoConfig.RESOURCES}")
        Log.d(TAG, "==================")
    }
    
    /**
     * 验证回调URI格式
     */
    fun validateRedirectUri(): Boolean {
        val uri = LogtoConfig.REDIRECT_URI
        val isValid = uri.matches(Regex("^[a-zA-Z][a-zA-Z0-9+.-]*://.*"))
        
        Log.d(TAG, "=== 回调URI验证 ===")
        Log.d(TAG, "URI: $uri")
        Log.d(TAG, "格式有效: $isValid")
        
        if (!isValid) {
            Log.e(TAG, "❌ 回调URI格式无效！应该是 scheme://host 格式")
        } else {
            Log.i(TAG, "✅ 回调URI格式有效")
        }
        
        return isValid
    }
    
    /**
     * 检查必要的配置
     */
    fun checkConfiguration(): Boolean {
        Log.d(TAG, "=== 配置检查 ===")
        
        val checks = mutableListOf<Pair<String, Boolean>>()
        
        // 检查端点
        val hasEndpoint = LogtoConfig.LOGTO_ENDPOINT.isNotBlank()
        checks.add("Logto端点" to hasEndpoint)
        
        // 检查客户端ID
        val hasClientId = LogtoConfig.CLIENT_ID.isNotBlank()
        checks.add("客户端ID" to hasClientId)
        
        // 检查回调URI
        val hasRedirectUri = LogtoConfig.REDIRECT_URI.isNotBlank()
        checks.add("回调URI" to hasRedirectUri)
        
        // 检查回调URI格式
        val validRedirectUri = validateRedirectUri()
        checks.add("回调URI格式" to validRedirectUri)
        
        // 打印结果
        checks.forEach { (name, passed) ->
            val status = if (passed) "✅" else "❌"
            Log.d(TAG, "$status $name: $passed")
        }
        
        val allPassed = checks.all { it.second }
        Log.d(TAG, "总体配置状态: ${if (allPassed) "✅ 通过" else "❌ 失败"}")
        
        return allPassed
    }
    
    /**
     * 生成建议的回调URI
     */
    fun suggestRedirectUri(packageName: String): String {
        val suggested = "$packageName://callback"
        Log.d(TAG, "=== 回调URI建议 ===")
        Log.d(TAG, "当前包名: $packageName")
        Log.d(TAG, "建议的回调URI: $suggested")
        Log.d(TAG, "请确保在Logto控制台中配置此URI")
        return suggested
    }
}
