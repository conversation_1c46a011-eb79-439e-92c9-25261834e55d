package com.cabycare.android.ui.device

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.cabycare.android.data.model.Device

/**
 * 设备设置对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DeviceSettingsDialog(
    device: Device,
    onDismiss: () -> Unit,
    onSave: (Device) -> Unit
) {
    // 表单状态
    var deviceName by remember { mutableStateOf(device.name) }
    var timezone by remember { mutableStateOf(device.timezone) }
    var enableNotifications by remember { mutableStateOf(true) }
    var enableAutoRecording by remember { mutableStateOf(false) }
    var recordingQuality by remember { mutableStateOf("HD") }
    var motionSensitivity by remember { mutableStateOf(0.5f) }
    
    // 验证状态
    val isFormValid = deviceName.isNotBlank()
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
                    .verticalScroll(rememberScrollState())
            ) {
                // 标题
                Text(
                    text = "设备设置",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 24.dp)
                )
                
                // 设备信息
                DeviceInfoSection(
                    device = device,
                    deviceName = deviceName,
                    onDeviceNameChange = { deviceName = it },
                    timezone = timezone,
                    onTimezoneChange = { timezone = it }
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 通知设置
                NotificationSettingsSection(
                    enableNotifications = enableNotifications,
                    onEnableNotificationsChange = { enableNotifications = it }
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 录制设置
                RecordingSettingsSection(
                    enableAutoRecording = enableAutoRecording,
                    onEnableAutoRecordingChange = { enableAutoRecording = it },
                    recordingQuality = recordingQuality,
                    onRecordingQualityChange = { recordingQuality = it }
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 传感器设置
                SensorSettingsSection(
                    motionSensitivity = motionSensitivity,
                    onMotionSensitivityChange = { motionSensitivity = it }
                )
                
                Spacer(modifier = Modifier.height(32.dp))
                
                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("取消")
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Button(
                        onClick = {
                            val updatedDevice = device.copy(
                                name = deviceName,
                                timezone = timezone
                            )
                            onSave(updatedDevice)
                        },
                        enabled = isFormValid
                    ) {
                        Text("保存")
                    }
                }
            }
        }
    }
}

/**
 * 设备信息部分
 */
@Composable
fun DeviceInfoSection(
    device: Device,
    deviceName: String,
    onDeviceNameChange: (String) -> Unit,
    timezone: String,
    onTimezoneChange: (String) -> Unit
) {
    Column {
        Text(
            text = "设备信息",
            fontSize = 16.sp,
            fontWeight = FontWeight.SemiBold,
            modifier = Modifier.padding(bottom = 12.dp)
        )
        
        // 设备名称
        OutlinedTextField(
            value = deviceName,
            onValueChange = onDeviceNameChange,
            label = { Text("设备名称") },
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 12.dp),
            singleLine = true,
            leadingIcon = {
                Icon(
                    imageVector = Icons.Default.Edit,
                    contentDescription = "编辑"
                )
            }
        )
        
        // 只读信息
        OutlinedTextField(
            value = device.model,
            onValueChange = {},
            label = { Text("设备型号") },
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 12.dp),
            readOnly = true,
            leadingIcon = {
                Icon(
                    imageVector = Icons.Default.Category,
                    contentDescription = "型号"
                )
            }
        )
        
        OutlinedTextField(
            value = device.hardwareSn,
            onValueChange = {},
            label = { Text("硬件序列号") },
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 12.dp),
            readOnly = true,
            leadingIcon = {
                Icon(
                    imageVector = Icons.Default.QrCode,
                    contentDescription = "序列号"
                )
            }
        )
        
        OutlinedTextField(
            value = device.firmwareVersion,
            onValueChange = {},
            label = { Text("固件版本") },
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 12.dp),
            readOnly = true,
            leadingIcon = {
                Icon(
                    imageVector = Icons.Default.Update,
                    contentDescription = "版本"
                )
            }
        )
        
        // 时区设置
        var expandedTimezone by remember { mutableStateOf(false) }
        ExposedDropdownMenuBox(
            expanded = expandedTimezone,
            onExpandedChange = { expandedTimezone = !expandedTimezone },
            modifier = Modifier.fillMaxWidth()
        ) {
            OutlinedTextField(
                value = timezone,
                onValueChange = {},
                readOnly = true,
                label = { Text("时区") },
                trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expandedTimezone) },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.Schedule,
                        contentDescription = "时区"
                    )
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .menuAnchor()
            )
            
            ExposedDropdownMenu(
                expanded = expandedTimezone,
                onDismissRequest = { expandedTimezone = false }
            ) {
                listOf(
                    "Asia/Shanghai" to "北京时间 (UTC+8)",
                    "Asia/Tokyo" to "东京时间 (UTC+9)",
                    "America/New_York" to "纽约时间 (UTC-5)",
                    "Europe/London" to "伦敦时间 (UTC+0)"
                ).forEach { (value, display) ->
                    DropdownMenuItem(
                        text = { Text(display) },
                        onClick = {
                            onTimezoneChange(value)
                            expandedTimezone = false
                        }
                    )
                }
            }
        }
    }
}

/**
 * 通知设置部分
 */
@Composable
fun NotificationSettingsSection(
    enableNotifications: Boolean,
    onEnableNotificationsChange: (Boolean) -> Unit
) {
    Column {
        Text(
            text = "通知设置",
            fontSize = 16.sp,
            fontWeight = FontWeight.SemiBold,
            modifier = Modifier.padding(bottom = 12.dp)
        )
        
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column {
                        Text(
                            text = "推送通知",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = "接收设备状态和活动通知",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                    }
                    
                    Switch(
                        checked = enableNotifications,
                        onCheckedChange = onEnableNotificationsChange
                    )
                }
            }
        }
    }
}

/**
 * 录制设置部分
 */
@Composable
fun RecordingSettingsSection(
    enableAutoRecording: Boolean,
    onEnableAutoRecordingChange: (Boolean) -> Unit,
    recordingQuality: String,
    onRecordingQualityChange: (String) -> Unit
) {
    Column {
        Text(
            text = "录制设置",
            fontSize = 16.sp,
            fontWeight = FontWeight.SemiBold,
            modifier = Modifier.padding(bottom = 12.dp)
        )
        
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                // 自动录制开关
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column {
                        Text(
                            text = "自动录制",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = "检测到活动时自动开始录制",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                    }
                    
                    Switch(
                        checked = enableAutoRecording,
                        onCheckedChange = onEnableAutoRecordingChange
                    )
                }
                
                if (enableAutoRecording) {
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 录制质量选择
                    Text(
                        text = "录制质量",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    
                    var expandedQuality by remember { mutableStateOf(false) }
                    ExposedDropdownMenuBox(
                        expanded = expandedQuality,
                        onExpandedChange = { expandedQuality = !expandedQuality }
                    ) {
                        OutlinedTextField(
                            value = recordingQuality,
                            onValueChange = {},
                            readOnly = true,
                            label = { Text("质量") },
                            trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expandedQuality) },
                            modifier = Modifier
                                .fillMaxWidth()
                                .menuAnchor()
                        )
                        
                        ExposedDropdownMenu(
                            expanded = expandedQuality,
                            onDismissRequest = { expandedQuality = false }
                        ) {
                            listOf("低质量", "标准", "HD", "4K").forEach { quality ->
                                DropdownMenuItem(
                                    text = { Text(quality) },
                                    onClick = {
                                        onRecordingQualityChange(quality)
                                        expandedQuality = false
                                    }
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 传感器设置部分
 */
@Composable
fun SensorSettingsSection(
    motionSensitivity: Float,
    onMotionSensitivityChange: (Float) -> Unit
) {
    Column {
        Text(
            text = "传感器设置",
            fontSize = 16.sp,
            fontWeight = FontWeight.SemiBold,
            modifier = Modifier.padding(bottom = 12.dp)
        )
        
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "运动检测灵敏度",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                Text(
                    text = "当前值: ${(motionSensitivity * 100).toInt()}%",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                Slider(
                    value = motionSensitivity,
                    onValueChange = onMotionSensitivityChange,
                    valueRange = 0f..1f,
                    steps = 9
                )
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "低",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                    Text(
                        text = "高",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
            }
        }
    }
}
