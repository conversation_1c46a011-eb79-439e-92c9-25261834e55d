package com.cabycare.android.ui.auth

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cabycare.android.data.auth.AuthManager
import com.cabycare.android.data.auth.AuthState
import com.cabycare.android.data.network.NetworkResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 认证ViewModel
 * 负责处理认证相关的UI逻辑
 */
@HiltViewModel
class AuthViewModel @Inject constructor(
    private val authManager: AuthManager
) : ViewModel() {
    
    val authState: StateFlow<AuthState> = authManager.authState
    
    /**
     * 原生Logto登录
     */
    fun signInNative(activity: android.app.Activity) {
        viewModelScope.launch {
            authManager.signInNative(activity)
        }
    }

    /**
     * 开始登录流程（保留兼容性）
     */
    fun startLogin(onAuthUrlGenerated: (String) -> Unit) {
        viewModelScope.launch {
            try {
                val authUrl = authManager.startLogin()
                onAuthUrlGenerated(authUrl)
            } catch (e: Exception) {
                // 错误已在AuthManager中处理
            }
        }
    }
    
    /**
     * 处理授权回调
     */
    fun handleAuthCallback(code: String, state: String) {
        viewModelScope.launch {
            when (val result = authManager.handleAuthCallback(code, state)) {
                is NetworkResult.Success -> {
                    // 登录成功，状态已在AuthManager中更新
                }
                is NetworkResult.Error -> {
                    // 错误已在AuthManager中处理
                }
                else -> {}
            }
        }
    }
    
    /**
     * 登出
     */
    fun logout() {
        viewModelScope.launch {
            authManager.logout()
        }
    }
    
    /**
     * 刷新认证状态
     */
    fun refreshAuthState() {
        viewModelScope.launch {
            authManager.refreshAuthState()
        }
    }
    
    /**
     * 清除错误状态
     */
    fun clearError() {
        // 可以在AuthManager中添加clearError方法
    }
}
