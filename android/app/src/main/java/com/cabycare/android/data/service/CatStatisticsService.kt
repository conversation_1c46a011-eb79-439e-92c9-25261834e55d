package com.cabycare.android.data.service

import android.util.Log
import com.cabycare.android.data.model.*
import com.cabycare.android.data.repository.VideoRepository
import com.cabycare.android.data.repository.DeviceRepository
import com.cabycare.android.data.network.NetworkResult
import com.cabycare.android.ui.home.CatAlert
import com.cabycare.android.ui.home.CatStatistic
import com.cabycare.android.ui.home.AlertSeverity
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 猫咪统计数据计算服务
 * 基于iOS版本HomeViewModel的calculateStatistics方法实现
 * 负责计算猫咪的如厕统计数据和健康警报
 */
@Singleton
class CatStatisticsService @Inject constructor(
    private val videoRepository: VideoRepository,
    private val deviceRepository: DeviceRepository
) {
    
    companion object {
        private const val TAG = "CatStatisticsService"
        
        // 时间常量（与iOS版本保持一致）
        private const val TOILET_ALERT_THRESHOLD_HOURS = 8 // 8小时未如厕警告阈值
        private const val RECENT_PERIOD_HOURS = 24 // 最近24小时
        private const val COMPARISON_PERIOD_HOURS = 48 // 对比期间48小时
        
        // 体重和时长的合理范围
        private const val MIN_CAT_WEIGHT = 0.5 // 最小猫咪体重(kg)
        private const val MAX_CAT_WEIGHT = 20.0 // 最大猫咪体重(kg)
        private const val MIN_TOILET_DURATION = 10 // 最小如厕时长(秒)
        private const val MAX_TOILET_DURATION = 3600 // 最大如厕时长(秒)
    }
    
    private val dateFormatter = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    
    /**
     * 计算所有猫咪的统计数据和警报
     * 对应iOS版本的calculateStatistics方法
     */
    suspend fun calculateAllCatStatistics(
        cats: List<CatProfile>,
        userId: String
    ): Pair<List<CatStatistic>, List<CatAlert>> {
        Log.d(TAG, "🏠 开始计算 ${cats.size} 只猫咪的统计数据")
        
        // 获取用户的设备列表
        val devicesResult = deviceRepository.getAccessibleDevices(userId)
        val devices = when (devicesResult) {
            is NetworkResult.Success -> devicesResult.data
            else -> {
                Log.e(TAG, "❌ 无法获取用户设备列表")
                return Pair(emptyList(), emptyList())
            }
        }
        
        if (devices.isEmpty()) {
            Log.w(TAG, "⚠️ 用户没有设备，无法计算统计数据")
            return Pair(emptyList(), emptyList())
        }
        
        val now = Date()
        val calendar = Calendar.getInstance()
        
        // 计算时间区间（与iOS版本保持一致）
        calendar.time = now
        calendar.add(Calendar.HOUR_OF_DAY, -RECENT_PERIOD_HOURS)
        val twentyFourHoursAgo = calendar.time // 24小时前
        
        calendar.time = now
        calendar.add(Calendar.HOUR_OF_DAY, -COMPARISON_PERIOD_HOURS)
        val fortyEightHoursAgo = calendar.time // 48小时前
        
        calendar.time = now
        calendar.add(Calendar.HOUR_OF_DAY, -TOILET_ALERT_THRESHOLD_HOURS)
        val eightHoursAgo = calendar.time // 8小时前
        
        val catStats = mutableListOf<CatStatistic>()
        val alertCats = mutableListOf<CatAlert>()
        
        // 为每只猫计算统计数据
        for (cat in cats) {
            Log.d(TAG, "🐱 正在计算猫咪 ${cat.name} (${cat.id}) 的统计数据")
            
            try {
                // 获取该猫的如厕记录
                val segments = getToiletSegmentsForCat(
                    cat.id,
                    fortyEightHoursAgo,
                    now,
                    devices
                )
                
                Log.d(TAG, "🐱 猫咪 ${cat.name} 在过去48小时共有 ${segments.size} 次如厕记录")
                
                // 分离最近24小时和前24小时的数据
                val recentSegments = segments.filter { segment ->
                    val segmentTime = segment.getStartTime()
                    segmentTime.after(twentyFourHoursAgo) && segmentTime.before(now)
                }
                val previousSegments = segments.filter { segment ->
                    val segmentTime = segment.getStartTime()
                    segmentTime.after(fortyEightHoursAgo) && segmentTime.before(twentyFourHoursAgo)
                }
                
                Log.d(TAG, "🐱 猫咪 ${cat.name}: 最近24小时 ${recentSegments.size} 次，前24小时 ${previousSegments.size} 次")
                
                // 检查是否需要警告（8小时未如厕）
                val lastToiletTime = segments.firstOrNull()?.getStartTime()
                if (lastToiletTime != null && lastToiletTime.before(eightHoursAgo)) {
                    val hoursAgo = ((now.time - lastToiletTime.time) / (1000 * 60 * 60)).toInt()
                    alertCats.add(CatAlert(
                        catId = cat.id,
                        catName = cat.name,
                        message = "${cat.name} 已经 ${hoursAgo} 小时没有如厕了",
                        severity = when {
                            hoursAgo >= 24 -> AlertSeverity.CRITICAL
                            hoursAgo >= 12 -> AlertSeverity.HIGH
                            else -> AlertSeverity.MEDIUM
                        }
                    ))
                    Log.d(TAG, "⚠️ 猫咪 ${cat.name} 需要关注：${hoursAgo}小时未如厕")
                } else if (segments.isEmpty()) {
                    // 如果完全没有记录，也算作警告
                    alertCats.add(CatAlert(
                        catId = cat.id,
                        catName = cat.name,
                        message = "${cat.name} 没有如厕记录",
                        severity = AlertSeverity.MEDIUM
                    ))
                    Log.d(TAG, "⚠️ 猫咪 ${cat.name} 没有如厕记录")
                }
                
                // 计算统计数据
                val recentCount = recentSegments.size
                val recentDuration = recentSegments.sumOf { it.getDurationSeconds() }
                val previousCount = previousSegments.size
                val previousDuration = previousSegments.sumOf { it.getDurationSeconds() }
                
                // 计算体重变化
                val currentWeight = recentSegments.firstOrNull()?.weightCat
                val previousWeight = findNearestWeight(twentyFourHoursAgo, segments)
                
                var weightChange: Double? = null
                var weightChangePercentage: Double? = null
                
                if (currentWeight != null && previousWeight != null && previousWeight > 0) {
                    weightChange = currentWeight - previousWeight
                    weightChangePercentage = (weightChange / previousWeight) * 100
                }
                
                // 计算健康评分（基于如厕频率和规律性）
                val healthScore = calculateHealthScore(recentCount, previousCount, recentDuration, segments)
                
                // 计算活跃度
                val activityLevel = calculateActivityLevel(recentCount, recentDuration)
                
                // 创建统计数据
                val catStat = CatStatistic(
                    catId = cat.id,
                    name = cat.name,
                    age = cat.age,
                    gender = cat.genderEnum.displayName,
                    healthStatus = determineHealthStatus(healthScore, alertCats.any { it.catId == cat.id }),
                    todayActivity = recentCount, // 今日如厕次数
                    healthScore = healthScore,
                    activityLevel = activityLevel,
                    avatarUrl = cat.avatarUrl // 包含猫咪头像URL
                )
                
                catStats.add(catStat)
                
                Log.d(TAG, "✅ 猫咪 ${cat.name} 统计完成: 今日${recentCount}次, 健康评分${healthScore}")
                
            } catch (e: Exception) {
                Log.e(TAG, "❌ 计算猫咪 ${cat.name} 统计数据失败", e)
                
                // 即使计算失败，也创建基础的统计数据
                val basicStat = CatStatistic(
                    catId = cat.id,
                    name = cat.name,
                    age = cat.age,
                    gender = cat.genderEnum.displayName,
                    healthStatus = "数据获取中",
                    todayActivity = 0,
                    healthScore = 50, // 默认分数
                    activityLevel = "未知",
                    avatarUrl = cat.avatarUrl // 包含猫咪头像URL
                )
                catStats.add(basicStat)
            }
        }
        
        Log.d(TAG, "🏠 统计计算完成: ${catStats.size} 只猫咪的统计数据, ${alertCats.size} 只猫咪需要关注")
        
        return Pair(catStats, alertCats)
    }
    
    /**
     * 获取指定猫咪的如厕记录
     */
    private suspend fun getToiletSegmentsForCat(
        catId: String,
        startDate: Date,
        endDate: Date,
        devices: List<AccessibleDevice>
    ): List<VideoSegmentRecord> {
        val startDateStr = dateFormatter.format(startDate)
        val endDateStr = dateFormatter.format(endDate)
        
        return when (val result = videoRepository.getToiletSegments(catId, startDateStr, endDateStr, devices)) {
            is NetworkResult.Success -> result.data
            is NetworkResult.Error -> {
                Log.e(TAG, "❌ 获取猫咪 $catId 如厕记录失败: ${result.exception.message}")
                emptyList()
            }
            else -> emptyList()
        }
    }
    
    /**
     * 寻找最接近指定时间的体重记录
     * 对应iOS版本的findNearestWeight方法
     */
    private fun findNearestWeight(targetDate: Date, segments: List<VideoSegmentRecord>): Double? {
        if (segments.isEmpty()) return null
        
        return segments
            .filter { it.weightCat > 0 } // 只考虑有效的体重记录
            .minByOrNull { segment ->
                Math.abs(segment.getStartTime().time - targetDate.time)
            }?.weightCat
    }
    
    /**
     * 计算健康评分（0-100分）
     * 基于如厕频率、时长和规律性
     */
    private fun calculateHealthScore(
        recentCount: Int,
        previousCount: Int,
        recentDuration: Double,
        allSegments: List<VideoSegmentRecord>
    ): Int {
        var score = 85 // 基础分数
        
        // 基于如厕频率评分
        when (recentCount) {
            in 0..1 -> score -= 30 // 频率过低
            in 2..3 -> score -= 10 // 略低
            in 4..8 -> score += 10 // 正常范围
            in 9..12 -> score += 0 // 正常偏高
            else -> score -= 20 // 频率过高可能有问题
        }
        
        // 基于频率变化评分
        if (previousCount > 0) {
            val changeRatio = (recentCount - previousCount).toDouble() / previousCount
            when {
                changeRatio > 0.5 -> score -= 15 // 显著增加
                changeRatio < -0.5 -> score -= 20 // 显著减少
                Math.abs(changeRatio) <= 0.2 -> score += 5 // 稳定
            }
        }
        
        // 基于平均如厕时长评分
        if (recentCount > 0) {
            val avgDuration = recentDuration / recentCount
            when {
                avgDuration < 30 -> score -= 10 // 时长过短
                avgDuration in 30.0..180.0 -> score += 5 // 正常范围
                avgDuration > 300 -> score -= 15 // 时长过长
            }
        }
        
        // 基于规律性评分（检查时间间隔是否规律）
        if (allSegments.size >= 3) {
            val intervals = allSegments.zipWithNext { curr, next ->
                Math.abs(curr.getStartTime().time - next.getStartTime().time)
            }
            val avgInterval = intervals.average()
            val varianceRatio = intervals.map { Math.abs(it - avgInterval) / avgInterval }.average()
            
            when {
                varianceRatio < 0.3 -> score += 10 // 很规律
                varianceRatio < 0.5 -> score += 5 // 比较规律
                varianceRatio > 1.0 -> score -= 10 // 不规律
            }
        }
        
        return score.coerceIn(0, 100)
    }
    
    /**
     * 计算活跃度等级
     */
    private fun calculateActivityLevel(count: Int, totalDuration: Double): String {
        val avgDuration = if (count > 0) totalDuration / count else 0.0
        
        return when {
            count == 0 -> "无活动"
            count <= 2 && avgDuration < 60 -> "低"
            count in 3..5 || (count <= 2 && avgDuration >= 60) -> "中等"
            count in 6..8 || avgDuration > 120 -> "活跃"
            count > 8 -> "非常活跃"
            else -> "正常"
        }
    }
    
    /**
     * 确定健康状态
     */
    private fun determineHealthStatus(healthScore: Int, hasAlert: Boolean): String {
        return when {
            hasAlert || healthScore < 50 -> "需要关注"
            healthScore < 70 -> "一般"
            healthScore >= 85 -> "健康"
            else -> "良好"
        }
    }
} 