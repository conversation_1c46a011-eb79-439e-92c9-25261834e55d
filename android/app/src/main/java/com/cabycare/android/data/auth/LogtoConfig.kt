package com.cabycare.android.data.auth

/**
 * Logto配置常量
 * 基于iOS版本的Configuration.swift
 */
object LogtoConfig {
    // Logto服务器配置
    const val LOGTO_ENDPOINT = "https://login.caby.care"
    const val CLIENT_ID = "d2oz2qj0ppjdpf6de14ha"
    const val REDIRECT_URI = "com.cabycare.android://callback"
    
    // API配置
    const val API_BASE_URL = "https://api.caby.care"
    const val CALLBACK_URL = "$API_BASE_URL/api/callback"
    const val REFRESH_URL = "$API_BASE_URL/api/refresh"
    
    // OAuth范围
    val SCOPES = listOf(
        "openid",
        "profile", 
        "email",
        "offline_access"
    )
    
    // 资源
    val RESOURCES = listOf(
        API_BASE_URL
    )
}
