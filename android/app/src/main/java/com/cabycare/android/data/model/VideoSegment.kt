package com.cabycare.android.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.text.SimpleDateFormat
import java.util.*

/**
 * 视频段记录模型 - 完全匹配API文档 /api/records/videos/list 返回格式
 * 对应iOS VideoSegment，用于如厕记录视频段
 */
@Serializable
data class VideoSegmentRecord(
    val start: Long, // Unix时间戳（秒）
    val duration: String, // 持续时间（秒），字符串格式如 "45.5"
    val url: String, // HLS播放URL
    @SerialName("weight_litter")
    val weightLitter: Double, // 猫砂重量（公斤）
    @SerialName("weight_cat")
    val weightCat: Double, // 猫咪重量（公斤）
    @SerialName("weight_waste")
    val weightWaste: Double, // 排泄物重量（公斤）
    @SerialName("thumbnail_url")
    val thumbnailUrl: String? = null, // 缩略图URL
    @SerialName("animal_id")
    val animalId: String? = null, // 猫咪ID，可能为空

    // 新增：设备名称字段（不参与序列化，在获取数据后手动设置）
    @kotlinx.serialization.Transient
    val deviceName: String? = null // 设备名称，从AccessibleDevice获取
) {
    // 视频段唯一标识符，基于开始时间
    val id: String
        get() = start.toString()
    
    /**
     * 解析持续时间为Double
     */
    fun getDurationSeconds(): Double {
        return duration.toDoubleOrNull() ?: 0.0
    }
    
    /**
     * 解析开始时间为Date对象
     */
    fun getStartTime(): Date {
        return Date(start * 1000) // Unix时间戳转换为毫秒
    }
    
    /**
     * 计算结束时间
     */
    fun getEndTime(): Date {
        val durationMs = (getDurationSeconds() * 1000).toLong()
        return Date(start * 1000 + durationMs)
    }
    
    /**
     * 格式化的持续时间
     */
    val formattedDuration: String
        get() {
            val totalSeconds = getDurationSeconds().toInt()
            val minutes = totalSeconds / 60
            val seconds = totalSeconds % 60
            
            return if (minutes > 0) {
                if (seconds == 0) {
                    "${minutes}分钟"
                } else {
                    "${minutes}分${seconds}秒"
                }
            } else {
                "${seconds}秒"
            }
        }
    
    /**
     * 格式化的开始时间字符串（用于兼容现有代码）
     */
    val startTimeString: String
        get() {
            val formatter = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault())
            formatter.timeZone = TimeZone.getTimeZone("UTC")
            return formatter.format(getStartTime())
        }
        
    // MARK: - 兼容性属性，用于支持现有代码
    
    /**
     * 兼容属性：startTime（与API中的start字段对应，但以字符串形式返回）
     */
    val startTime: String
        get() = startTimeString
    
    /**
     * 兼容属性：deviceId（从URL中提取设备ID）
     */
    val deviceId: String
        get() {
            // 尝试从URL中提取设备ID
            // URL格式通常类似：.../get?path=records/device123&...
            val regex = "path=records/device([^&]+)".toRegex()
            val match = regex.find(url)
            return match?.groupValues?.get(1) ?: "unknown"
        }
    
    /**
     * 兼容属性：fileUrl（与url字段相同）
     */
    val fileUrl: String
        get() = url
    
    /**
     * 兼容属性：isPlayable（始终返回true，因为从API返回的都是可播放的）
     */
    val isPlayable: Boolean
        get() = true
    
    /**
     * 兼容属性：duration作为Long（将字符串持续时间转换为长整型秒数）
     */
    val durationLong: Long
        get() = getDurationSeconds().toLong()
    
    /**
     * 是否为有效的如厕记录
     */
    val isValidToiletRecord: Boolean
        get() = weightCat > 0.5 && weightCat < 20.0 && getDurationSeconds() > 10 && getDurationSeconds() < 3600
        
    /**
     * 转换为VideoSegment以兼容现有的播放器组件
     */
    fun toVideoSegment(): VideoSegment {
        return VideoSegment(
            id = this.id,
            deviceId = this.deviceId,
            startTime = this.startTime,
            endTime = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault()).apply {
                timeZone = TimeZone.getTimeZone("UTC")
            }.format(this.getEndTime()),
            duration = this.durationLong,
            fileUrl = this.url,
            thumbnailUrl = this.thumbnailUrl,
            quality = "medium",
            fileSize = null,
            status = "available",
            createdAt = this.startTime
        )
    }
}

/**
 * 每日视频统计
 */
data class DailyVideoStats(
    val date: Date,
    val videoCount: Int,
    val totalDuration: Double,
    val segments: List<VideoSegmentRecord>
) {
    /**
     * 格式化的总持续时间
     */
    val formattedDuration: String
        get() {
            val totalSeconds = totalDuration.toInt()
            val hours = totalSeconds / 3600
            val minutes = (totalSeconds % 3600) / 60
            val seconds = totalSeconds % 60
            
            return when {
                hours > 0 -> String.format("%dh %dm", hours, minutes)
                minutes > 0 -> String.format("%dm %ds", minutes, seconds)
                else -> String.format("%ds", seconds)
            }
        }
}

/**
 * 视频列表响应类型 - 支持null响应（当设备没有视频时API返回null）
 */
typealias VideoListResponse = List<VideoSegmentRecord>?

// 保留向后兼容的包装响应格式（如果某些API仍需要）
@Serializable
data class WrappedVideoListResponse(
    val status: String,
    val message: String,
    val data: List<VideoSegmentRecord> = emptyList()
) {
    val isSuccess: Boolean
        get() = status == "success"
} 