package com.cabycare.android.data.network

import android.util.Log
import com.cabycare.android.data.local.UserPreferences
import kotlinx.coroutines.flow.first
import kotlinx.serialization.json.Json
import okhttp3.Interceptor
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.kotlinx.serialization.asConverterFactory
import com.cabycare.android.data.network.LogtoTokenService
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 网络管理器
 * 负责配置Retrofit、OkHttp和网络拦截器
 */
@Singleton
class NetworkManager @Inject constructor(
    private val userPreferences: UserPreferences
) {
    companion object {
        private const val TAG = "NetworkManager"
        private const val BASE_URL = "https://api.caby.care" // 替换为实际的API地址
        private const val CONNECT_TIMEOUT = 30L
        private const val READ_TIMEOUT = 30L
        private const val WRITE_TIMEOUT = 30L
    }
    
    private val json = Json {
        ignoreUnknownKeys = true
        coerceInputValues = true
        encodeDefaults = true
    }
    
    /**
     * 认证拦截器
     * 自动在请求头中添加Authorization token，并在401错误时自动刷新token
     */
    private inner class AuthInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val originalRequest = chain.request()

            // 获取访问令牌（优先从完整用户信息获取）
            val accessToken = runCatching {
                kotlinx.coroutines.runBlocking {
                    userPreferences.getAccessTokenFromCompleteInfo().first()
                        ?: userPreferences.getAccessToken().first() // 向后兼容
                }
            }.getOrNull()

            val newRequest = if (!accessToken.isNullOrEmpty()) {
                originalRequest.newBuilder()
                    .addHeader("Authorization", "Bearer $accessToken")
                    .build()
            } else {
                originalRequest
            }

            val response = chain.proceed(newRequest)

            // 如果收到401错误，尝试刷新token并重试
            if (response.code == 401 && !accessToken.isNullOrEmpty()) {
                Log.w(TAG, "🔄 收到401错误，尝试刷新token并重试请求")
                response.close() // 关闭原始响应

                return try {
                    // 尝试刷新token
                    val newAccessToken = runCatching {
                        kotlinx.coroutines.runBlocking {
                            // 直接使用Logto token服务进行刷新
                            val completeUserInfo = userPreferences.getCompleteUserInfoSync()
                            val refreshToken = completeUserInfo?.refreshToken

                            if (!refreshToken.isNullOrEmpty()) {
                                // 创建临时的token服务进行刷新
                                val tokenService = createLogtoTokenService()
                                val response = tokenService.refreshToken(
                                    refreshToken = refreshToken,
                                    clientId = com.cabycare.android.data.auth.LogtoConfig.CLIENT_ID,
                                    scope = "openid profile email offline_access"
                                )

                                // 更新用户偏好中的令牌信息
                                userPreferences.updateTokenInfo(
                                    accessToken = response.accessToken,
                                    refreshToken = response.refreshToken ?: refreshToken,
                                    expiresIn = response.expiresIn,
                                    idToken = response.idToken
                                )

                                response.accessToken
                            } else {
                                null
                            }
                        }
                    }.getOrNull()

                    if (!newAccessToken.isNullOrEmpty()) {
                        Log.i(TAG, "✅ Token刷新成功，重试原始请求")
                        // 使用新token重试原始请求
                        val retryRequest = originalRequest.newBuilder()
                            .header("Authorization", "Bearer $newAccessToken")
                            .build()
                        chain.proceed(retryRequest)
                    } else {
                        Log.e(TAG, "❌ Token刷新失败")
                        response // 返回原始401响应
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "❌ Token刷新过程中发生异常: ${e.message}", e)
                    response // 返回原始401响应
                }
            }

            return response
        }
    }
    
    /**
     * 错误处理拦截器
     * 处理通用的HTTP错误和认证过期
     */
    private inner class ErrorHandlingInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val request = chain.request()
            val response = chain.proceed(request)
            
            when (response.code) {
                401 -> {
                    Log.w(TAG, "认证失败，令牌可能已过期")
                    // 这里可以触发令牌刷新逻辑
                }
                403 -> {
                    Log.w(TAG, "权限不足")
                }
                404 -> {
                    Log.w(TAG, "请求的资源不存在: ${request.url}")
                }
                500, 502, 503, 504 -> {
                    Log.e(TAG, "服务器错误: ${response.code}")
                }
            }
            
            return response
        }
    }
    
    /**
     * 创建OkHttp客户端
     */
    private fun createOkHttpClient(): OkHttpClient {
        val loggingInterceptor = HttpLoggingInterceptor { message ->
            Log.d(TAG, message)
        }.apply {
            level = HttpLoggingInterceptor.Level.BODY
        }
        
        return OkHttpClient.Builder()
            .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
            .addInterceptor(AuthInterceptor())
            .addInterceptor(ErrorHandlingInterceptor())
            .addInterceptor(loggingInterceptor)
            .build()
    }
    
    /**
     * 创建Retrofit实例
     */
    fun createRetrofit(): Retrofit {
        return Retrofit.Builder()
            .baseUrl(BASE_URL)
            .client(createOkHttpClient())
            .addConverterFactory(json.asConverterFactory("application/json".toMediaType()))
            .build()
    }

    /**
     * 创建Logto Token服务（用于token刷新）
     */
    private fun createLogtoTokenService(): LogtoTokenService {
        val loggingInterceptor = HttpLoggingInterceptor { message ->
            Log.d(TAG, "Token API: $message")
        }.apply {
            level = HttpLoggingInterceptor.Level.BODY
        }

        val okHttpClient = OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
            .build()

        val retrofit = Retrofit.Builder()
            .baseUrl(com.cabycare.android.data.auth.LogtoConfig.LOGTO_ENDPOINT + "/")
            .client(okHttpClient)
            .addConverterFactory(json.asConverterFactory("application/json".toMediaType()))
            .build()

        return retrofit.create(LogtoTokenService::class.java)
    }
    
    /**
     * 创建API服务
     */
    inline fun <reified T> createApiService(): T {
        return createRetrofit().create(T::class.java)
    }
}

/**
 * 网络结果封装类
 */
sealed class NetworkResult<out T> {
    data class Success<T>(val data: T) : NetworkResult<T>()
    data class Error(val exception: Throwable) : NetworkResult<Nothing>()
    data class Loading(val isLoading: Boolean = true) : NetworkResult<Nothing>()
}

/**
 * 网络请求扩展函数
 * 统一处理网络请求的异常和结果
 */
suspend fun <T> safeApiCall(
    apiCall: suspend () -> T
): NetworkResult<T> {
    return try {
        NetworkResult.Success(apiCall())
    } catch (e: Exception) {
        Log.e("NetworkManager", "API调用失败", e)
        NetworkResult.Error(e)
    }
}
