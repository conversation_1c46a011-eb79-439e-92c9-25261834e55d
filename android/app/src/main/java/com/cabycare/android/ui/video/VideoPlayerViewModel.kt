package com.cabycare.android.ui.video

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cabycare.android.data.model.VideoPlayerState
import com.cabycare.android.data.model.VideoSegment
import com.cabycare.android.data.model.VideoStreamInfo
import com.cabycare.android.data.network.NetworkResult
import com.cabycare.android.data.repository.VideoRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 视频播放器UI状态
 */
data class VideoPlayerUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val currentStream: VideoStreamInfo? = null,
    val videoSegments: List<VideoSegment> = emptyList(),
    val playerState: VideoPlayerState = VideoPlayerState(),
    val selectedDeviceId: String? = null,
    val isHistoryMode: Boolean = false
)

/**
 * 视频播放器ViewModel
 * 管理视频流、历史回放和播放状态
 */
@HiltViewModel
class VideoPlayerViewModel @Inject constructor(
    private val videoRepository: VideoRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(VideoPlayerUiState())
    val uiState: StateFlow<VideoPlayerUiState> = _uiState.asStateFlow()
    
    /**
     * 加载设备的视频流
     */
    fun loadVideoStream(deviceId: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isLoading = true,
                error = null,
                selectedDeviceId = deviceId
            )
            
            // 获取视频列表而不是流信息
            when (val result = videoRepository.getVideoList(deviceId)) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        videoSegments = result.data.map { it.toVideoSegment() } // 转换为VideoSegment
                    )
                }
                is NetworkResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = result.exception.message ?: "加载视频流失败"
                    )
                }
                else -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "未知错误"
                    )
                }
            }
        }
    }
    
    /**
     * 加载历史视频段
     */
    fun loadVideoSegments(
        deviceId: String,
        startTime: String? = null,
        endTime: String? = null
    ) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isLoading = true,
                error = null,
                selectedDeviceId = deviceId,
                isHistoryMode = true
            )
            
            when (val result = videoRepository.getVideoList(deviceId, startTime, endTime)) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        videoSegments = result.data.map { it.toVideoSegment() } // 转换为VideoSegment
                    )
                }
                is NetworkResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = result.exception.message ?: "加载历史视频失败"
                    )
                }
                else -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "未知错误"
                    )
                }
            }
        }
    }
    
    /**
     * 播放历史视频段
     */
    fun playVideoSegment(segment: VideoSegment) {
        val streamInfo = VideoStreamInfo(
            deviceId = segment.deviceId,
            streamUrl = segment.fileUrl,
            hlsUrl = segment.fileUrl,
            rtmpUrl = null,
            quality = segment.quality,
            status = "streaming",
            viewerCount = 1,
            bitrate = null,
            frameRate = null,
            resolution = null,
            lastUpdated = segment.createdAt
        )
        
        _uiState.value = _uiState.value.copy(
            currentStream = streamInfo,
            isHistoryMode = true
        )
    }
    
    /**
     * 更新播放器状态
     */
    fun updatePlayerState(newState: VideoPlayerState) {
        _uiState.value = _uiState.value.copy(playerState = newState)
    }
    
    /**
     * 切换播放/暂停
     */
    fun togglePlayPause() {
        val currentState = _uiState.value.playerState
        updatePlayerState(currentState.copy(isPlaying = !currentState.isPlaying))
    }
    
    /**
     * 设置静音状态
     */
    fun setMuted(muted: Boolean) {
        val currentState = _uiState.value.playerState
        updatePlayerState(currentState.copy(isMuted = muted))
    }
    
    /**
     * 设置音量
     */
    fun setVolume(volume: Float) {
        val currentState = _uiState.value.playerState
        updatePlayerState(currentState.copy(volume = volume.coerceIn(0f, 1f)))
    }
    
    /**
     * 设置播放位置
     */
    fun seekTo(position: Long) {
        val currentState = _uiState.value.playerState
        updatePlayerState(currentState.copy(currentPosition = position))
    }
    
    /**
     * 设置播放速度
     */
    fun setPlaybackSpeed(speed: Float) {
        val currentState = _uiState.value.playerState
        updatePlayerState(currentState.copy(playbackSpeed = speed))
    }
    
    /**
     * 切换全屏模式
     */
    fun toggleFullscreen() {
        val currentState = _uiState.value.playerState
        updatePlayerState(currentState.copy(isFullscreen = !currentState.isFullscreen))
    }
    
    /**
     * 设置视频质量
     */
    fun setVideoQuality(quality: String) {
        val currentState = _uiState.value.playerState
        updatePlayerState(currentState.copy(quality = quality))
        
        // 重新加载视频流以应用新质量
        _uiState.value.selectedDeviceId?.let { deviceId ->
            loadVideoStream(deviceId)
        }
    }
    
    /**
     * 处理播放错误
     */
    fun handlePlaybackError(error: String) {
        val currentState = _uiState.value.playerState
        updatePlayerState(currentState.copy(error = error, isPlaying = false))
    }
    
    /**
     * 清除错误状态
     */
    fun clearError() {
        val currentState = _uiState.value.playerState
        updatePlayerState(currentState.copy(error = null))
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    /**
     * 刷新当前视频流
     */
    fun refreshCurrentStream() {
        _uiState.value.selectedDeviceId?.let { deviceId ->
            if (_uiState.value.isHistoryMode) {
                loadVideoSegments(deviceId)
            } else {
                loadVideoStream(deviceId)
            }
        }
    }
    
    /**
     * 切换到直播模式
     */
    fun switchToLiveMode() {
        _uiState.value.selectedDeviceId?.let { deviceId ->
            loadVideoStream(deviceId)
        }
    }
    
    /**
     * 切换到历史模式
     */
    fun switchToHistoryMode() {
        _uiState.value.selectedDeviceId?.let { deviceId ->
            loadVideoSegments(deviceId)
        }
    }
}
