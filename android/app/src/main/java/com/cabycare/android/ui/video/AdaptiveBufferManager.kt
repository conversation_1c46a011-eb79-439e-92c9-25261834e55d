package com.cabycare.android.ui.video

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.util.Log
import androidx.media3.exoplayer.DefaultLoadControl
import androidx.media3.exoplayer.LoadControl
import androidx.media3.exoplayer.upstream.DefaultAllocator
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 自适应缓冲管理器
 * 根据网络状况和设备性能动态调整缓冲策略
 */
class AdaptiveBufferManager(private val context: Context) {
    
    companion object {
        private const val TAG = "AdaptiveBufferManager"
        
        // 网络类型枚举
        enum class NetworkType {
            WIFI,
            CELLULAR_5G,
            CELLULAR_4G,
            CELLULAR_3G,
            CELLULAR_2G,
            UNKNOWN
        }
        
        // 缓冲策略配置
        data class BufferConfig(
            val minBufferMs: Int,
            val maxBufferMs: Int,
            val bufferForPlaybackMs: Int,
            val bufferForPlaybackAfterRebufferMs: Int,
            val targetBufferBytes: Int,
            val allocatorBlockSize: Int
        )
    }
    
    private val _currentNetworkType = MutableStateFlow(NetworkType.UNKNOWN)
    val currentNetworkType: StateFlow<NetworkType> = _currentNetworkType.asStateFlow()
    
    private val _currentBufferConfig = MutableStateFlow(getDefaultBufferConfig())
    val currentBufferConfig: StateFlow<BufferConfig> = _currentBufferConfig.asStateFlow()
    
    /**
     * 获取当前网络类型
     */
    fun getCurrentNetworkType(): NetworkType {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork ?: return NetworkType.UNKNOWN
        val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return NetworkType.UNKNOWN
        
        return when {
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> NetworkType.WIFI
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> {
                // 尝试检测蜂窝网络类型
                when {
                    capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) -> {
                        // 简化的网络类型检测，实际应用中可以更精确
                        NetworkType.CELLULAR_4G
                    }
                    else -> NetworkType.CELLULAR_3G
                }
            }
            else -> NetworkType.UNKNOWN
        }
    }
    
    /**
     * 更新网络状态并调整缓冲策略
     */
    fun updateNetworkStatus() {
        val newNetworkType = getCurrentNetworkType()
        if (newNetworkType != _currentNetworkType.value) {
            _currentNetworkType.value = newNetworkType
            _currentBufferConfig.value = getBufferConfigForNetwork(newNetworkType)
            Log.d(TAG, "网络类型变更: $newNetworkType, 更新缓冲策略: ${_currentBufferConfig.value}")
        }
    }
    
    /**
     * 根据网络类型获取缓冲配置
     */
    private fun getBufferConfigForNetwork(networkType: NetworkType): BufferConfig {
        return when (networkType) {
            NetworkType.WIFI -> BufferConfig(
                minBufferMs = 30000,          // WiFi下激进缓冲：30秒最小缓冲
                maxBufferMs = 120000,         // 2分钟最大缓冲
                bufferForPlaybackMs = 3000,   // 3秒启动缓冲
                bufferForPlaybackAfterRebufferMs = 8000, // 重新缓冲需要8秒
                targetBufferBytes = 50 * 1024 * 1024, // 50MB目标缓冲
                allocatorBlockSize = 64 * 1024 // 64KB块，更大的缓冲块
            )
            
            NetworkType.CELLULAR_5G -> BufferConfig(
                minBufferMs = 25000,          // 5G网络：25秒最小缓冲
                maxBufferMs = 90000,          // 1.5分钟最大缓冲
                bufferForPlaybackMs = 3000,   // 3秒启动缓冲
                bufferForPlaybackAfterRebufferMs = 7000, // 重新缓冲需要7秒
                targetBufferBytes = 40 * 1024 * 1024, // 40MB目标缓冲
                allocatorBlockSize = 48 * 1024 // 48KB块
            )
            
            NetworkType.CELLULAR_4G -> BufferConfig(
                minBufferMs = 20000,          // 4G网络：20秒最小缓冲
                maxBufferMs = 60000,          // 1分钟最大缓冲
                bufferForPlaybackMs = 3000,   // 3秒启动缓冲
                bufferForPlaybackAfterRebufferMs = 6000, // 重新缓冲需要6秒
                targetBufferBytes = 35 * 1024 * 1024, // 35MB目标缓冲
                allocatorBlockSize = 32 * 1024 // 32KB块
            )
            
            NetworkType.CELLULAR_3G -> BufferConfig(
                minBufferMs = 25000,          // 3G网络：25秒最小缓冲（需要更多预缓冲）
                maxBufferMs = 45000,          // 45秒最大缓冲
                bufferForPlaybackMs = 5000,   // 5秒启动缓冲
                bufferForPlaybackAfterRebufferMs = 10000, // 重新缓冲需要10秒
                targetBufferBytes = 25 * 1024 * 1024, // 25MB目标缓冲
                allocatorBlockSize = 24 * 1024 // 24KB块
            )
            
            NetworkType.CELLULAR_2G -> BufferConfig(
                minBufferMs = 30000,          // 2G网络：30秒最小缓冲（最激进的缓冲）
                maxBufferMs = 40000,          // 40秒最大缓冲
                bufferForPlaybackMs = 8000,   // 8秒启动缓冲
                bufferForPlaybackAfterRebufferMs = 15000, // 重新缓冲需要15秒
                targetBufferBytes = 20 * 1024 * 1024, // 20MB目标缓冲
                allocatorBlockSize = 16 * 1024 // 16KB块
            )
            
            NetworkType.UNKNOWN -> getDefaultBufferConfig()
        }
    }
    
    /**
     * 获取默认缓冲配置（激进缓冲策略）
     */
    private fun getDefaultBufferConfig(): BufferConfig {
        return BufferConfig(
            minBufferMs = 20000,          // 默认20秒最小缓冲
            maxBufferMs = 60000,          // 1分钟最大缓冲
            bufferForPlaybackMs = 3000,   // 3秒启动缓冲
            bufferForPlaybackAfterRebufferMs = 6000, // 重新缓冲需要6秒
            targetBufferBytes = 35 * 1024 * 1024, // 35MB目标缓冲
            allocatorBlockSize = 32 * 1024 // 32KB块
        )
    }
    
    /**
     * 创建自适应LoadControl
     */
    fun createAdaptiveLoadControl(): LoadControl {
        val config = _currentBufferConfig.value
        
        return DefaultLoadControl.Builder()
            .setAllocator(DefaultAllocator(true, config.allocatorBlockSize))
            .setBufferDurationsMs(
                config.minBufferMs,
                config.maxBufferMs,
                config.bufferForPlaybackMs,
                config.bufferForPlaybackAfterRebufferMs
            )
            .setTargetBufferBytes(config.targetBufferBytes)
            .setPrioritizeTimeOverSizeThresholds(false)
            .setBackBuffer(config.minBufferMs / 2, true) // 后台缓冲为最小缓冲的一半
            .build().also {
                Log.d(TAG, "创建自适应LoadControl: $config")
            }
    }
    
    /**
     * 根据缓冲状态动态调整策略
     */
    fun adjustBufferStrategy(
        bufferPercentage: Int,
        bufferAheadMs: Long,
        consecutiveLowBufferCount: Int
    ) {
        // 如果连续出现低缓冲，增加缓冲要求而不是降低
        if (consecutiveLowBufferCount >= 3) {
            val currentConfig = _currentBufferConfig.value
            val adjustedConfig = currentConfig.copy(
                minBufferMs = minOf(60000, currentConfig.minBufferMs + 5000), // 增加5秒最小缓冲
                maxBufferMs = minOf(180000, currentConfig.maxBufferMs + 10000), // 增加10秒最大缓冲
                bufferForPlaybackMs = minOf(8000, currentConfig.bufferForPlaybackMs + 1000), // 增加1秒启动缓冲
                targetBufferBytes = minOf(80 * 1024 * 1024, currentConfig.targetBufferBytes + 10 * 1024 * 1024) // 增加10MB目标缓冲
            )
            _currentBufferConfig.value = adjustedConfig
            Log.d(TAG, "检测到持续缓冲问题，增加缓冲策略: $adjustedConfig")
        }
    }
}
