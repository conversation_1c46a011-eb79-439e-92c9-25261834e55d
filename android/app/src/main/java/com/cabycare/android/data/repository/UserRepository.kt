package com.cabycare.android.data.repository

import android.util.Log
import com.cabycare.android.data.local.UserPreferences
import com.cabycare.android.data.model.*
import com.cabycare.android.data.network.ApiService
import com.cabycare.android.data.network.NetworkResult
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用户数据仓库
 * 负责管理用户相关的数据操作
 */
@Singleton
class UserRepository @Inject constructor(
    private val apiService: ApiService,
    private val userPreferences: UserPreferences
) : BaseRepository() {

    companion object {
        private const val TAG = "UserRepository"
    }
    
    /**
     * 获取用户资料
     */
    suspend fun getUserProfile(): NetworkResult<User> {
        return safeApiCall {
            val response = apiService.getUserProfile()
            // 将UserInfoResponse转换为User对象
            User(
                userId = response.userId,
                username = response.username,
                email = response.email,
                phone = "",
                nickname = response.nickname ?: "",
                status = 1,
                createdAt = "",
                updatedAt = ""
            )
        }
    }
    
    /**
     * 更新用户资料
     */
    suspend fun updateUserProfile(user: User): NetworkResult<User> {
        return safeApiCall {
            val response = apiService.updateUserProfile(user)
            if (response.isSuccess && response.data != null) {
                response.data
            } else {
                throw Exception(response.message)
            }
        }
    }
    
    /**
     * 保存用户认证信息
     */
    suspend fun saveAuthCredentials(
        accessToken: String,
        refreshToken: String,
        user: User
    ) {
        userPreferences.saveAccessToken(accessToken)
        userPreferences.saveRefreshToken(refreshToken)
        userPreferences.saveUserInfo(user.userId, user.email, user.displayName)
    }
    
    /**
     * 清除用户认证信息
     */
    suspend fun clearAuthCredentials() {
        userPreferences.clearAuthCredentials()
    }
    
    /**
     * 检查是否已认证
     */
    fun isAuthenticated(): Flow<Boolean> {
        return userPreferences.isAuthenticated()
    }
    
    /**
     * 获取访问令牌
     */
    fun getAccessToken(): Flow<String?> {
        return userPreferences.getAccessToken()
    }
    
    /**
     * 获取刷新令牌
     */
    fun getRefreshToken(): Flow<String?> {
        return userPreferences.getRefreshToken()
    }
    
    /**
     * 获取用户ID（后端数据库中的用户ID，用于API请求）
     */
    fun getUserId(): Flow<String?> {
        return userPreferences.getUserId().map { userId ->
            Log.d(TAG, "🔍 获取用户ID: $userId")
            userId
        }
    }

    /**
     * 获取Logto ID（Logto认证服务的用户ID）
     */
    fun getLogtoId(): Flow<String?> {
        return userPreferences.getLogtoId().map { logtoId ->
            Log.d(TAG, "🔍 获取Logto ID: $logtoId")
            logtoId
        }
    }
    
    /**
     * 获取用户邮箱
     */
    fun getUserEmail(): Flow<String?> {
        return userPreferences.getUserEmail()
    }
    
    /**
     * 获取用户名
     */
    fun getUserName(): Flow<String?> {
        return userPreferences.getUserName()
    }
}
