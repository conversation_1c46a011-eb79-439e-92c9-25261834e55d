package com.cabycare.android.ui.device

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.cabycare.android.data.model.AccessibleDevice
import com.cabycare.android.data.model.AccessibleDeviceWithStatus

/**
 * 设备管理页面 - 使用蓝牙添加设备
 * 基于iOS版本的设备管理实现
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DeviceScreen(
    viewModel: DeviceViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    var showAddDeviceDialog by remember { mutableStateOf(false) }

    // 启动时加载设备
    LaunchedEffect(Unit) {
        viewModel.loadDevicesWithRealTimeStatus()
    }

    // 错误对话框
    uiState.error?.let { message ->
        AlertDialog(
            onDismissRequest = { viewModel.clearError() },
            title = { Text("错误") },
            text = { Text(message) },
            confirmButton = {
                TextButton(onClick = { viewModel.clearError() }) {
                    Text("确定")
                }
            }
        )
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 顶部标题栏
        TopAppBar(
            title = {
            Text(
                    text = "我的设备",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold
                )
            },
            actions = {
            IconButton(onClick = { viewModel.refreshDevices() }) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = "刷新"
                )
            }
        }
        )

        Spacer(modifier = Modifier.height(16.dp))

        // 设备统计卡片
        DeviceStatsCard(
            deviceCount = uiState.devices.size,
            onlineDeviceCount = uiState.onlineDeviceCount
        )

        Spacer(modifier = Modifier.height(16.dp))

        // 添加设备卡片
        AddDeviceCard(
            onAddDevice = { showAddDeviceDialog = true }
        )

        Spacer(modifier = Modifier.height(16.dp))

        // 设备列表
        when {
            uiState.isLoading && uiState.devices.isEmpty() -> {
                // 首次加载中
                LoadingDevicesView()
            }
            uiState.devices.isEmpty() && !uiState.isLoading -> {
                // 没有设备
                EmptyDevicesView(onAddDevice = { showAddDeviceDialog = true })
            }
            else -> {
                    // 设备列表
                DeviceListSection(
                    devices = uiState.devices,
                    isRefreshing = uiState.isLoading,
                    onRefresh = { viewModel.refreshDevices() },
                    onDeleteDevice = { deviceId -> viewModel.deleteDevice(deviceId) }
                )
            }
        }
    }

    // 蓝牙添加设备对话框
    if (showAddDeviceDialog) {
        AddDeviceDialog(
            onDismiss = { showAddDeviceDialog = false },
            onAdd = { deviceInfo ->
                // 通过蓝牙添加设备
                viewModel.addDevice(deviceInfo)
                showAddDeviceDialog = false
            }
        )
    }
}

/**
 * 设备统计卡片
 */
@Composable
fun DeviceStatsCard(
    deviceCount: Int,
    onlineDeviceCount: Int = 0
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Row(
            modifier = Modifier.padding(20.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 设备图标
            Box(
                modifier = Modifier
                    .size(60.dp)
                    .background(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                MaterialTheme.colorScheme.primary,
                                MaterialTheme.colorScheme.primary.copy(alpha = 0.7f)
                            )
                        ),
                        shape = CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Devices,
                    contentDescription = "设备",
                    tint = Color.White,
                    modifier = Modifier.size(30.dp)
                )
            }

            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "我的设备",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                )
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "$deviceCount 台设备",
                        fontSize = 24.sp,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    if (deviceCount > 0) {
                        Text(
                            text = "• $onlineDeviceCount 台在线",
                            fontSize = 14.sp,
                            color = if (onlineDeviceCount > 0) Color.Green else MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f),
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
                Text(
                    text = when {
                        deviceCount == 0 -> "暂无设备连接"
                        onlineDeviceCount == deviceCount -> "所有设备在线"
                        onlineDeviceCount > 0 -> "部分设备在线"
                        else -> "所有设备离线"
                    },
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.6f)
                )
            }

            // 状态指示
            Surface(
                color = if (onlineDeviceCount > 0) Color.Green else if (deviceCount > 0) Color(0xFFFF9800) else Color.Gray,
                shape = RoundedCornerShape(20.dp)
            ) {
                Text(
                    text = when {
                        deviceCount == 0 -> "无设备"
                        onlineDeviceCount == deviceCount -> "全部在线"
                        onlineDeviceCount > 0 -> "部分在线"
                        else -> "全部离线"
                    },
                    modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
                    fontSize = 12.sp,
                    color = Color.White,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

/**
 * 添加设备卡片
 */
@Composable
fun AddDeviceCard(onAddDevice: () -> Unit) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 蓝牙图标
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .background(
                        color = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                        shape = CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Bluetooth,
                    contentDescription = "蓝牙",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(24.dp)
                )
            }

            Column(modifier = Modifier.weight(1f)) {
                    Text(
                    text = "通过蓝牙添加设备",
                    fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold
                    )
                    Text(
                    text = "扫描并连接附近的AbyBox设备",
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
            }

            Button(
                onClick = onAddDevice,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
                    ) {
                        Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "添加",
                            modifier = Modifier.size(16.dp)
                        )
                    Spacer(modifier = Modifier.width(8.dp))
                Text("添加")
            }
        }
    }
}

/**
 * 加载中视图
 */
@Composable
fun LoadingDevicesView() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        CircularProgressIndicator(
            modifier = Modifier.size(48.dp),
            strokeWidth = 4.dp
        )
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = "加载设备列表中...",
            fontSize = 16.sp,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )
    }
}

/**
 * 空设备视图
 */
@Composable
fun EmptyDevicesView(onAddDevice: () -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Default.DevicesOther,
            contentDescription = "无设备",
            modifier = Modifier.size(80.dp),
            tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.4f)
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Text(
            text = "还没有设备",
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "通过蓝牙扫描并添加您的第一台AbyBox设备",
            fontSize = 14.sp,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Button(
            onClick = onAddDevice,
            modifier = Modifier.padding(horizontal = 32.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Bluetooth,
                contentDescription = "蓝牙",
                modifier = Modifier.size(20.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("开始添加设备")
        }
    }
}

/**
 * 设备列表区域
 */
@Composable
fun DeviceListSection(
    devices: List<AccessibleDeviceWithStatus>,
    isRefreshing: Boolean,
    onRefresh: () -> Unit,
    onDeleteDevice: (String) -> Unit
) {
    Column {
        // 列表标题
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "设备列表",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold
            )
            
            if (isRefreshing) {
                CircularProgressIndicator(
                    modifier = Modifier.size(20.dp),
                    strokeWidth = 2.dp
                )
            } else {
                IconButton(onClick = onRefresh) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "刷新",
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(12.dp))

        // 设备列表
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(devices) { device ->
                DeviceCard(
                    device = device,
                    onDelete = { onDeleteDevice(device.deviceId) }
                )
            }
        }
    }
}

/**
 * 设备卡片
 */
@Composable
fun DeviceCard(
    device: AccessibleDeviceWithStatus,
    onDelete: () -> Unit
) {
    var showDeleteDialog by remember { mutableStateOf(false) }

    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier.fillMaxWidth()
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 设备图标
                    Box(
                        modifier = Modifier
                            .size(40.dp)
                            .background(
                                color = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                                shape = CircleShape
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.DevicesOther,
                            contentDescription = "设备",
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(20.dp)
                        )
                    }

                    Column {
                        Text(
                            text = device.displayName,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.SemiBold
                        )
                        Text(
                            text = "型号: ${device.model}",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                        // 添加固件版本显示
                        Text(
                            text = "v${device.firmwareVersion}",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                        // 显示状态源
                        Text(
                            text = device.statusSource,
                            fontSize = 10.sp,
                            color = if (device.hasRealTimeData) Color.Green else Color(0xFFFF9800),
                            fontWeight = FontWeight.Medium
                        )
                    }
                }

                // 操作按钮
                Row {
                    IconButton(onClick = { showDeleteDialog = true }) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "删除",
                            tint = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 设备状态信息行
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                // 在线状态
                Row(
                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .background(
                                color = if (device.isOnline) Color.Green else Color.Red,
                                shape = CircleShape
                            )
                    )
                    Text(
                        text = if (device.isOnline) "在线" else "离线",
                        fontSize = 12.sp,
                        color = if (device.isOnline) Color.Green else Color.Red,
                        fontWeight = FontWeight.Medium
                    )
                }
                
                // 显示电池电量（如果有）
                device.batteryLevel?.let { battery ->
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Battery6Bar,
                            contentDescription = "电池",
                            tint = when {
                                battery > 50 -> Color.Green
                                battery > 20 -> Color(0xFFFF9800)
                                else -> Color.Red
                            },
                            modifier = Modifier.size(16.dp)
                        )
                        Text(
                            text = "${battery}%",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                    }
                }
                
                // 显示温度（如果有）
                device.temperature?.let { temp ->
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(4.dp),
                        verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                            imageVector = Icons.Default.Thermostat,
                            contentDescription = "温度",
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(16.dp)
                        )
                        Text(
                            text = "${temp.toInt()}°C",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                    }
                }
            }
            
            // 最后心跳时间（如果有）
            device.lastHeartbeat?.let { heartbeat ->
            Spacer(modifier = Modifier.height(8.dp))
                Row(
                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Favorite,
                        contentDescription = "心跳",
                        tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f),
                        modifier = Modifier.size(14.dp)
            )
            Text(
                        text = "最后心跳: $heartbeat",
                        fontSize = 10.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                    )
                }
            }
        }
    }

    // 删除确认对话框
    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteDialog = false },
            title = { Text("删除设备") },
            text = { Text("确定要删除设备 \"${device.displayName}\" 吗？此操作无法撤销。") },
            confirmButton = {
                TextButton(
                    onClick = {
                        onDelete()
                        showDeleteDialog = false
                    }
                ) {
                    Text("删除", color = MaterialTheme.colorScheme.error)
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteDialog = false }) {
                    Text("取消")
                }
            }
        )
    }
}
