package com.cabycare.android.ui.theme

import androidx.compose.ui.graphics.Color

// Primary colors - 基于iOS版本的主色调 (#002447 和 #fbcd08)
val PrimaryLight = Color(0xFF002447)  // iOS版本的themePrimary: #002447
val PrimaryDark = Color(0xFFfbcd08)   // iOS版本的themeSecondary: #fbcd08 (深色模式使用)
val PrimaryContainer = Color(0xFFE3F2FD)
val OnPrimary = Color.White
val OnPrimaryContainer = Color(0xFF001D36)

// Secondary colors - 使用iOS版本的辅助色
val SecondaryLight = Color(0xFFfbcd08)  // iOS版本的themeSecondary: #fbcd08
val SecondaryDark = Color(0xFF002447)   // iOS版本的themePrimary: #002447 (深色模式使用)
val SecondaryContainer = Color(0xFFFFF8E1)
val OnSecondary = Color.Black
val OnSecondaryContainer = Color(0xFF1D1B00)

// Error colors
val ErrorLight = Color(0xFFFF3B30)     // iOS红色
val ErrorDark = Color(0xFFFF453A)      // iOS深色模式红色
val ErrorContainer = Color(0xFFFFDAD6)
val OnError = Color.White
val OnErrorContainer = Color(0xFF410002)

// Warning colors
val WarningLight = Color(0xFFFF9500)   // iOS橙色
val WarningDark = Color(0xFFFF9F0A)    // iOS深色模式橙色

// Background colors - 匹配iOS版本的背景色
val BackgroundLight = Color(0xFFFFFFFF)  // 纯白背景
val BackgroundDark = Color(0xFF000000)   // 纯黑背景
val SurfaceLight = Color(0xFFF6F7F8)     // iOS版本的secondaryBackgroundColor浅色
val SurfaceDark = Color(0xFF1A1A1A)      // iOS版本的secondaryBackgroundColor深色

// Surface variant colors
val SurfaceVariantLight = Color(0xFFF2F2F7)  // iOS浅灰色
val SurfaceVariantDark = Color(0xFF1C1C1E)   // iOS深色模式灰色
val OnSurfaceVariantLight = Color(0xFF3C3C43)
val OnSurfaceVariantDark = Color(0xFFEBEBF5)

// Outline colors
val OutlineLight = Color(0xFFC6C6C8)
val OutlineDark = Color(0xFF38383A)

// Additional colors for CabyCare specific use cases
val HealthyGreen = Color(0xFF34C759)
val WarningOrange = Color(0xFFFF9500)
val CriticalRed = Color(0xFFFF3B30)
val InfoBlue = Color(0xFF007AFF)

// Video player colors
val VideoControlsBackground = Color(0x80000000)
val VideoProgressBackground = Color(0x40FFFFFF)
val VideoProgressForeground = Color(0xFFFFFFFF)

// Card colors
val CardBackgroundLight = Color.White
val CardBackgroundDark = Color(0xFF2C2C2E)
val CardBorderLight = Color(0xFFE5E5EA)
val CardBorderDark = Color(0xFF38383A)
