package com.cabycare.android.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.cabycare.android.ui.auth.AuthViewModel
import com.cabycare.android.ui.auth.LoginScreen
import com.cabycare.android.ui.main.MainScreen
import com.cabycare.android.ui.splash.SplashScreen
import kotlinx.coroutines.delay

/**
 * CabyCare应用程序的根Composable
 * 负责管理应用程序的整体导航和状态
 */
@Composable
fun CabyCareApp(
    modifier: Modifier = Modifier,
    authViewModel: AuthViewModel = hiltViewModel()
) {
    val authState by authViewModel.authState.collectAsState()
    var isLaunching by remember { mutableStateOf(true) }
    
    // 启动画面延迟
    LaunchedEffect(Unit) {
        delay(2000) // 2秒启动画面
        isLaunching = false
    }
    
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        when {
            isLaunching -> {
                SplashScreen()
            }
            authState.isLoading -> {
                CircularProgressIndicator(
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.padding(16.dp)
                )
            }
            authState.isAuthenticated -> {
                MainScreen()
            }
            else -> {
                LoginScreen(
                    onLoginSuccess = {
                        authViewModel.refreshAuthState()
                    }
                )
            }
        }
    }
}
