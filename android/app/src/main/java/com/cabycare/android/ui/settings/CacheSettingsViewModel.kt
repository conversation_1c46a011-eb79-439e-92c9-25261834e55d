package com.cabycare.android.ui.settings

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cabycare.android.data.cache.CacheInfo
import com.cabycare.android.data.cache.VideoCache
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import android.util.Log
import javax.inject.Inject

/**
 * 缓存设置ViewModel
 * 管理视频缓存的状态和操作
 */
@HiltViewModel
class CacheSettingsViewModel @Inject constructor(
    private val videoCache: VideoCache
) : ViewModel() {
    
    companion object {
        private const val TAG = "CacheSettingsViewModel"
    }
    
    private val _cacheInfo = MutableStateFlow(CacheInfo.empty())
    val cacheInfo: StateFlow<CacheInfo> = _cacheInfo.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _operationMessage = MutableStateFlow<String?>(null)
    val operationMessage: StateFlow<String?> = _operationMessage.asStateFlow()
    
    init {
        refreshCacheInfo()
    }
    
    /**
     * 刷新缓存信息
     */
    fun refreshCacheInfo() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val info = videoCache.getCacheInfo()
                _cacheInfo.value = info
                Log.d(TAG, "📊 缓存信息更新: ${info.getFormattedSize()}, ${info.getFormattedUtilization()}")
            } catch (e: Exception) {
                Log.e(TAG, "获取缓存信息失败", e)
                _operationMessage.value = "获取缓存信息失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 清除所有缓存
     */
    fun clearAllCache() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                videoCache.clearCache()
                _operationMessage.value = "✅ 缓存已清理"
                refreshCacheInfo()
                Log.d(TAG, "🗑️ 所有缓存已清理")
            } catch (e: Exception) {
                Log.e(TAG, "清理缓存失败", e)
                _operationMessage.value = "清理缓存失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 清除操作消息
     */
    fun clearMessage() {
        _operationMessage.value = null
    }
}
