package com.cabycare.android.ui.video

import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.os.Bundle
import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import com.cabycare.android.ui.theme.CabyCareTheme
import dagger.hilt.android.AndroidEntryPoint

/**
 * 全屏视频播放Activity
 * 专门用于全屏视频播放体验
 */
@AndroidEntryPoint
class VideoPlayerActivity : ComponentActivity() {
    
    companion object {
        private const val EXTRA_STREAM_URL = "stream_url"
        private const val EXTRA_IS_LIVE = "is_live"
        private const val EXTRA_DEVICE_ID = "device_id"
        
        fun createIntent(
            context: Context,
            streamUrl: String,
            isLive: Boolean = false,
            deviceId: String? = null
        ): Intent {
            return Intent(context, VideoPlayerActivity::class.java).apply {
                putExtra(EXTRA_STREAM_URL, streamUrl)
                putExtra(EXTRA_IS_LIVE, isLive)
                putExtra(EXTRA_DEVICE_ID, deviceId)
            }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 设置全屏模式
        setupFullscreen()
        
        // 强制横屏
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        
        val streamUrl = intent.getStringExtra(EXTRA_STREAM_URL) ?: ""
        val isLive = intent.getBooleanExtra(EXTRA_IS_LIVE, false)
        val deviceId = intent.getStringExtra(EXTRA_DEVICE_ID)
        
        setContent {
            CabyCareTheme {
                FullscreenVideoPlayer(
                    streamUrl = streamUrl,
                    isLive = isLive,
                    deviceId = deviceId,
                    onBackPressed = { finish() },
                    modifier = Modifier.fillMaxSize()
                )
            }
        }
    }
    
    /**
     * 设置全屏模式
     */
    private fun setupFullscreen() {
        // 隐藏状态栏和导航栏
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
        windowInsetsController.let { controller ->
            controller.hide(WindowInsetsCompat.Type.systemBars())
            controller.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        }
        
        // 保持屏幕常亮
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 恢复竖屏
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
    }
}

/**
 * 全屏视频播放器组件
 */
@Composable
fun FullscreenVideoPlayer(
    streamUrl: String,
    isLive: Boolean,
    deviceId: String?,
    onBackPressed: () -> Unit,
    modifier: Modifier = Modifier
) {
    var showBackButton by remember { mutableStateOf(true) }
    
    // 自动隐藏返回按钮
    LaunchedEffect(showBackButton) {
        if (showBackButton) {
            kotlinx.coroutines.delay(3000)
            showBackButton = false
        }
    }
    
    VideoPlayerView(
        streamUrl = streamUrl,
        isLive = isLive,
        onVideoClick = {
            showBackButton = !showBackButton
        },
        modifier = modifier
    )
    
    // 返回按钮覆盖层
    if (showBackButton) {
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            IconButton(
                onClick = onBackPressed,
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .padding(16.dp)
                    .background(
                        Color.Black.copy(alpha = 0.5f),
                        CircleShape
                    )
            ) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "返回",
                    tint = Color.White
                )
            }
        }
    }
}
