package com.cabycare.android.ui.animals

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.cabycare.android.data.model.*
import java.util.*

/**
 * 添加/编辑猫咪对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddEditCatDialog(
    cat: CatProfile? = null,
    onDismiss: () -> Unit,
    onSave: (CatProfile) -> Unit
) {
    val isEditMode = cat != null
    
    // 表单状态
    var name by remember { mutableStateOf(cat?.name ?: "") }
    var age by remember { mutableStateOf(cat?.age ?: "") }
    var weight by remember { mutableStateOf(cat?.weight ?: "") }
    var selectedGender by remember { mutableStateOf(cat?.genderEnum ?: CatGender.MALE) }
    var selectedType by remember { mutableStateOf(cat?.typeEnum ?: CatType.OTHER) }
    var selectedHealthStatus by remember { mutableStateOf(cat?.healthStatusEnum ?: CatHealthStatus.HEALTHY) }
    var selectedActivityLevel by remember { mutableStateOf(cat?.activityLevelEnum ?: CatActivityLevel.NORMAL) }
    var neuteredStatus by remember { mutableStateOf(cat?.neuteredStatus ?: "") }
    
    // 验证状态
    val isFormValid = name.isNotBlank() && age.isNotBlank() && weight.isNotBlank()
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
                    .verticalScroll(rememberScrollState())
            ) {
                // 标题
                Text(
                    text = if (isEditMode) "编辑猫咪信息" else "添加新猫咪",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 24.dp)
                )
                
                // 基本信息
                Text(
                    text = "基本信息",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    modifier = Modifier.padding(bottom = 12.dp)
                )
                
                // 姓名
                OutlinedTextField(
                    value = name,
                    onValueChange = { name = it },
                    label = { Text("姓名") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 12.dp),
                    singleLine = true
                )
                
                // 年龄和体重
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 12.dp),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedTextField(
                        value = age,
                        onValueChange = { age = it },
                        label = { Text("年龄") },
                        modifier = Modifier.weight(1f),
                        singleLine = true
                    )
                    
                    OutlinedTextField(
                        value = weight,
                        onValueChange = { weight = it },
                        label = { Text("体重(kg)") },
                        modifier = Modifier.weight(1f),
                        singleLine = true
                    )
                }
                
                // 性别选择
                Text(
                    text = "性别",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectableGroup()
                        .padding(bottom = 16.dp)
                ) {
                    CatGender.values().forEach { gender ->
                        Row(
                            modifier = Modifier
                                .selectable(
                                    selected = selectedGender == gender,
                                    onClick = { selectedGender = gender },
                                    role = Role.RadioButton
                                )
                                .padding(end = 16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = selectedGender == gender,
                                onClick = null
                            )
                            Text(
                                text = gender.displayName,
                                modifier = Modifier.padding(start = 8.dp)
                            )
                        }
                    }
                }
                
                // 猫咪类型
                Text(
                    text = "类型",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                var expandedType by remember { mutableStateOf(false) }
                ExposedDropdownMenuBox(
                    expanded = expandedType,
                    onExpandedChange = { expandedType = !expandedType },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp)
                ) {
                    OutlinedTextField(
                        value = selectedType.displayName,
                        onValueChange = {},
                        readOnly = true,
                        label = { Text("选择类型") },
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expandedType) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .menuAnchor()
                    )
                    
                    ExposedDropdownMenu(
                        expanded = expandedType,
                        onDismissRequest = { expandedType = false }
                    ) {
                        CatType.values().forEach { type ->
                            DropdownMenuItem(
                                text = { Text(type.displayName) },
                                onClick = {
                                    selectedType = type
                                    expandedType = false
                                }
                            )
                        }
                    }
                }
                
                // 健康状态
                Text(
                    text = "健康状态",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                var expandedHealth by remember { mutableStateOf(false) }
                ExposedDropdownMenuBox(
                    expanded = expandedHealth,
                    onExpandedChange = { expandedHealth = !expandedHealth },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp)
                ) {
                    OutlinedTextField(
                        value = selectedHealthStatus.displayName,
                        onValueChange = {},
                        readOnly = true,
                        label = { Text("健康状态") },
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expandedHealth) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .menuAnchor()
                    )
                    
                    ExposedDropdownMenu(
                        expanded = expandedHealth,
                        onDismissRequest = { expandedHealth = false }
                    ) {
                        CatHealthStatus.values().forEach { status ->
                            DropdownMenuItem(
                                text = { Text(status.displayName) },
                                onClick = {
                                    selectedHealthStatus = status
                                    expandedHealth = false
                                }
                            )
                        }
                    }
                }
                
                // 活跃度
                Text(
                    text = "活跃度",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                var expandedActivity by remember { mutableStateOf(false) }
                ExposedDropdownMenuBox(
                    expanded = expandedActivity,
                    onExpandedChange = { expandedActivity = !expandedActivity },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp)
                ) {
                    OutlinedTextField(
                        value = selectedActivityLevel.displayName,
                        onValueChange = {},
                        readOnly = true,
                        label = { Text("活跃度") },
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expandedActivity) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .menuAnchor()
                    )
                    
                    ExposedDropdownMenu(
                        expanded = expandedActivity,
                        onDismissRequest = { expandedActivity = false }
                    ) {
                        CatActivityLevel.values().forEach { level ->
                            DropdownMenuItem(
                                text = { Text(level.displayName) },
                                onClick = {
                                    selectedActivityLevel = level
                                    expandedActivity = false
                                }
                            )
                        }
                    }
                }
                
                // 绝育状态（可选）
                OutlinedTextField(
                    value = neuteredStatus,
                    onValueChange = { neuteredStatus = it },
                    label = { Text("绝育状态（可选）") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 24.dp),
                    singleLine = true
                )
                
                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("取消")
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Button(
                        onClick = {
                            val newCat = CatProfile(
                                id = cat?.id ?: UUID.randomUUID().toString(),
                                name = name,
                                age = age,
                                gender = selectedGender.value,
                                weight = weight,
                                healthStatus = selectedHealthStatus.displayName,
                                activityLevel = selectedActivityLevel.displayName,
                                type = selectedType.displayName,
                                neuteredStatus = neuteredStatus.ifEmpty { null }
                            )
                            onSave(newCat)
                        },
                        enabled = isFormValid
                    ) {
                        Text(if (isEditMode) "保存" else "添加")
                    }
                }
            }
        }
    }
}
