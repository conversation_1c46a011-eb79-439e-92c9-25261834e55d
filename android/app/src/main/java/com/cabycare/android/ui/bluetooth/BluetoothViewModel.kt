package com.cabycare.android.ui.bluetooth

import android.bluetooth.BluetoothDevice
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cabycare.android.bluetooth.*
import com.cabycare.android.data.repository.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 蓝牙配置ViewModel
 * 协调BluetoothManager和UI的交互
 */
@HiltViewModel
class BluetoothViewModel @Inject constructor(
    private val bluetoothManager: BluetoothManager,
    private val userRepository: UserRepository
) : ViewModel() {

    // 暴露蓝牙管理器的状态流
    val bluetoothState: StateFlow<BluetoothState> = bluetoothManager.bluetoothState
    val connectionStatus: StateFlow<ConnectionStatus> = bluetoothManager.connectionStatus
    val discoveredDevices: StateFlow<List<BluetoothDevice>> = bluetoothManager.discoveredDevices
    val isScanning: StateFlow<Boolean> = bluetoothManager.isScanning
    val connectedDevice: StateFlow<BluetoothDevice?> = bluetoothManager.connectedDevice
    val errorMessage: StateFlow<String?> = bluetoothManager.errorMessage
    val wifiSendStatus: StateFlow<SendStatus> = bluetoothManager.wifiSendStatus
    val userIdSendStatus: StateFlow<SendStatus> = bluetoothManager.userIdSendStatus
    val activationStatus: StateFlow<ActivationStatus> = bluetoothManager.activationStatus
    val bluetoothStatusMessage: StateFlow<String> = bluetoothManager.bluetoothStatusMessage

    /**
     * 请求开始扫描（如果需要）
     */
    fun requestScanningIfNeeded() {
        bluetoothManager.startScanning()
    }

    /**
     * 开始扫描设备
     */
    fun startScanning() {
        bluetoothManager.startScanning()
    }

    /**
     * 停止扫描设备
     */
    fun stopScanning() {
        bluetoothManager.stopScanning()
    }

    /**
     * 连接到设备
     */
    fun connectToDevice(device: BluetoothDevice) {
        bluetoothManager.connectToDevice(device)
    }

    /**
     * 断开设备连接
     */
    fun disconnectDevice() {
        bluetoothManager.disconnectDevice()
    }

    /**
     * 发送WiFi配置
     */
    fun sendWiFiConfig(ssid: String, password: String) {
        bluetoothManager.sendWiFiConfig(ssid, password)
        
        // WiFi配置发送成功后，自动发送用户ID
        viewModelScope.launch {
            // 监听WiFi发送状态
            bluetoothManager.wifiSendStatus.collect { status ->
                if (status == SendStatus.SUCCESS) {
                    // WiFi配置发送成功，发送用户ID
                    sendCurrentUserId()
                    return@collect
                }
            }
        }
    }

    /**
     * 发送当前用户ID
     */
    private suspend fun sendCurrentUserId() {
        try {
            val userId = userRepository.getUserId().first()
            if (!userId.isNullOrEmpty()) {
                bluetoothManager.sendUserId(userId)
            } else {
                // 处理没有用户ID的情况
                bluetoothManager.clearError()
                // 可以设置一个错误消息或处理逻辑
            }
        } catch (e: Exception) {
            // 处理获取用户ID失败的情况
            bluetoothManager.clearError()
        }
    }

    /**
     * 激活设备
     */
    fun activateDevice() {
        viewModelScope.launch {
            bluetoothManager.activateDevice()
        }
    }

    /**
     * 清除错误消息
     */
    fun clearError() {
        bluetoothManager.clearError()
    }

    /**
     * 重置发送状态
     */
    fun resetSendStatus() {
        // 这里可以添加重置发送状态的逻辑
        // 由于状态是在BluetoothManager中管理的，可能需要添加相应的方法
        clearError()
    }

    override fun onCleared() {
        super.onCleared()
        // 清理资源，断开连接，停止扫描
        bluetoothManager.stopScanning()
        bluetoothManager.disconnectDevice()
    }
} 