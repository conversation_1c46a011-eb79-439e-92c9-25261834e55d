package com.cabycare.android.data.repository

import android.util.Log
import com.cabycare.android.data.network.NetworkResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import retrofit2.HttpException
import java.io.IOException
import java.net.SocketTimeoutException

/**
 * 基础Repository类
 * 提供通用的网络请求处理逻辑
 */
open class BaseRepository {
    
    companion object {
        private const val TAG = "BaseRepository"
    }
    
    /**
     * 安全的API调用
     * 统一处理网络请求的异常和结果
     */
    protected suspend fun <T> safeApiCall(
        apiCall: suspend () -> T
    ): NetworkResult<T> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "执行API调用")
            val result = apiCall()
            Log.d(TAG, "API调用成功")
            NetworkResult.Success(result)
        } catch (e: HttpException) {
            Log.e(TAG, "HTTP错误: ${e.code()} - ${e.message()}", e)
            when (e.code()) {
                401 -> NetworkResult.Error(UnauthorizedException("认证失败，请重新登录"))
                403 -> NetworkResult.Error(ForbiddenException("权限不足"))
                404 -> NetworkResult.Error(NotFoundException("请求的资源不存在"))
                422 -> NetworkResult.Error(ValidationException("请求参数验证失败"))
                429 -> NetworkResult.Error(RateLimitException("请求频率过高，请稍后重试"))
                in 500..599 -> NetworkResult.Error(ServerException("服务器错误，请稍后重试"))
                else -> NetworkResult.Error(UnknownException("网络请求失败: ${e.message()}"))
            }
        } catch (e: SocketTimeoutException) {
            Log.e(TAG, "请求超时", e)
            NetworkResult.Error(TimeoutException("请求超时，请检查网络连接"))
        } catch (e: IOException) {
            Log.e(TAG, "网络连接错误", e)
            NetworkResult.Error(NetworkException("网络连接失败，请检查网络设置"))
        } catch (e: Exception) {
            Log.e(TAG, "未知错误", e)
            NetworkResult.Error(UnknownException("发生未知错误: ${e.message}"))
        }
    }
    
    /**
     * 处理分页数据
     */
    protected fun <T> handlePaginatedResult(
        result: NetworkResult<T>
    ): NetworkResult<T> {
        return when (result) {
            is NetworkResult.Success -> {
                Log.d(TAG, "分页数据获取成功")
                result
            }
            is NetworkResult.Error -> {
                Log.e(TAG, "分页数据获取失败: ${result.exception.message}")
                result
            }
            is NetworkResult.Loading -> result
        }
    }
    
    /**
     * 处理列表数据
     */
    protected fun <T> handleListResult(
        result: NetworkResult<List<T>>
    ): NetworkResult<List<T>> {
        return when (result) {
            is NetworkResult.Success -> {
                Log.d(TAG, "列表数据获取成功，共${result.data.size}条记录")
                result
            }
            is NetworkResult.Error -> {
                Log.e(TAG, "列表数据获取失败: ${result.exception.message}")
                result
            }
            is NetworkResult.Loading -> result
        }
    }
    
    /**
     * 处理单个对象数据
     */
    protected fun <T> handleSingleResult(
        result: NetworkResult<T>
    ): NetworkResult<T> {
        return when (result) {
            is NetworkResult.Success -> {
                Log.d(TAG, "单个对象数据获取成功")
                result
            }
            is NetworkResult.Error -> {
                Log.e(TAG, "单个对象数据获取失败: ${result.exception.message}")
                result
            }
            is NetworkResult.Loading -> result
        }
    }
}

// MARK: - 自定义异常类

/**
 * API异常基类
 */
abstract class ApiException(message: String) : Exception(message)

/**
 * 认证异常
 */
class UnauthorizedException(message: String) : ApiException(message)

/**
 * 权限异常
 */
class ForbiddenException(message: String) : ApiException(message)

/**
 * 资源不存在异常
 */
class NotFoundException(message: String) : ApiException(message)

/**
 * 参数验证异常
 */
class ValidationException(message: String) : ApiException(message)

/**
 * 频率限制异常
 */
class RateLimitException(message: String) : ApiException(message)

/**
 * 服务器异常
 */
class ServerException(message: String) : ApiException(message)

/**
 * 网络连接异常
 */
class NetworkException(message: String) : ApiException(message)

/**
 * 请求超时异常
 */
class TimeoutException(message: String) : ApiException(message)

/**
 * 未知异常
 */
class UnknownException(message: String) : ApiException(message)
