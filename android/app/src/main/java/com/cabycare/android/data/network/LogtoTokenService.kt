package com.cabycare.android.data.network

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import retrofit2.http.*

/**
 * Logto Token刷新服务
 * 用于直接调用Logto的token端点进行令牌刷新
 */
interface LogtoTokenService {
    
    /**
     * 刷新访问令牌
     * 使用refresh_token获取新的access_token
     */
    @FormUrlEncoded
    @POST("oidc/token")
    suspend fun refreshToken(
        @Field("grant_type") grantType: String = "refresh_token",
        @Field("refresh_token") refreshToken: String,
        @Field("client_id") clientId: String,
        @Field("scope") scope: String? = null
    ): LogtoTokenResponse
    
    /**
     * 使用授权码获取令牌
     * 首次登录时使用
     */
    @FormUrlEncoded
    @POST("oidc/token")
    suspend fun getTokenWithAuthCode(
        @Field("grant_type") grantType: String = "authorization_code",
        @Field("code") code: String,
        @Field("redirect_uri") redirectUri: String,
        @Field("client_id") clientId: String,
        @Field("code_verifier") codeVerifier: String? = null
    ): LogtoTokenResponse
}

/**
 * Logto Token响应模型
 * 对应OpenID Connect Core 1.0标准的token响应
 */
@Serializable
data class LogtoTokenResponse(
    @SerialName("access_token")
    val accessToken: String,
    
    @SerialName("token_type")
    val tokenType: String = "Bearer",
    
    @SerialName("expires_in")
    val expiresIn: Long,
    
    @SerialName("refresh_token")
    val refreshToken: String? = null,
    
    @SerialName("id_token")
    val idToken: String? = null,
    
    @SerialName("scope")
    val scope: String? = null
)

/**
 * Logto错误响应模型
 */
@Serializable
data class LogtoErrorResponse(
    val error: String,
    @SerialName("error_description")
    val errorDescription: String? = null,
    @SerialName("error_uri")
    val errorUri: String? = null
)
