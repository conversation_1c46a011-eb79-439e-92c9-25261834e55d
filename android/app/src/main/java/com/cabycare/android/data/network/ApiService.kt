package com.cabycare.android.data.network

import com.cabycare.android.data.model.*
import retrofit2.http.*

/**
 * API服务接口
 * 定义所有网络请求的接口
 */
interface ApiService {

    // MARK: - 认证相关API

    /**
     * OAuth回调处理
     */
    @GET("api/callback")
    suspend fun handleOAuthCallback(@Query("code") code: String, @Query("state") state: String): OAuthCallbackResponse

    // MARK: - 用户相关API

    /**
     * 获取用户信息
     */
    @GET("api/user/info")
    suspend fun getUserProfile(): UserInfoResponse

    /**
     * 更新用户信息
     */
    @PUT("api/user/profile")
    suspend fun updateUserProfile(@Body user: User): ApiResponse<User>

    // MARK: - 猫咪相关API

    /**
     * 获取猫咪列表
     */
    @GET("api/cats")
    suspend fun getCats(): List<CatAPIResponse>

    /**
     * 获取猫咪详情
     */
    @GET("api/cats/{catId}")
    suspend fun getCatDetail(@Path("catId") catId: String): CatAPIResponse

    /**
     * 创建猫咪档案
     */
    @POST("api/cats")
    suspend fun createCat(@Body cat: CatProfile): CreateCatAPIResponse

    /**
     * 更新猫咪档案
     */
    @PUT("api/cats/{catId}")
    suspend fun updateCat(
        @Path("catId") catId: String,
        @Body cat: CatProfile
    ): UpdateCatAPIResponse

    /**
     * 删除猫咪档案
     */
    @DELETE("api/cats/{catId}")
    suspend fun deleteCat(@Path("catId") catId: String): ApiResponse<Unit>

    // MARK: - 设备相关API

    /**
     * 获取可访问设备列表
     */
    @GET("api/devices/accessible")
    suspend fun getAccessibleDevices(@Query("user_id") userId: String): AccessibleDevicesResponse

    /**
     * 获取设备详情
     */
    @GET("api/devices/{deviceId}")
    suspend fun getDeviceDetail(@Path("deviceId") deviceId: String): ApiResponse<Device>

    /**
     * 更新设备信息
     */
    @PUT("api/devices/{deviceId}")
    suspend fun updateDevice(
        @Path("deviceId") deviceId: String,
        @Body device: Device
    ): ApiResponse<Device>

    /**
     * 获取设备状态
     */
    @GET("api/devices/{deviceId}/status")
    suspend fun getDeviceStatus(@Path("deviceId") deviceId: String): DeviceStatusResponse

    /**
     * 获取设备OTA状态
     */
    @GET("api/devices/{deviceId}/ota-status")
    suspend fun getDeviceOTAStatus(@Path("deviceId") deviceId: String): DeviceOTAStatusResponse

    /**
     * 触发设备OTA更新
     */
    @POST("api/devices/{deviceId}/ota-update")
    suspend fun triggerOTAUpdate(@Path("deviceId") deviceId: String): ApiResponse<Unit>

    // MARK: - 视频相关API

    /**
     * 获取视频列表（与iOS一致）
     * 返回直接数组格式，匹配API文档 /api/records/videos/list
     */
    @GET("api/records/videos/list")
    suspend fun getVideoList(
        @Query("path") path: String, // 格式: device{deviceId}
        @Query("start") start: String? = null,
        @Query("end") end: String? = null
    ): VideoListResponse

    /**
     * 获取视频播放列表（HLS m3u8文件）
     * 匹配API文档 /api/records/videos/get
     * 返回HLS播放列表，内容类型为 application/vnd.apple.mpegurl
     */
    @GET("api/records/videos/get")
    suspend fun getVideoPlaylist(
        @Query("path") path: String, // 设备路径，格式: device{deviceId}
        @Query("start") start: String, // ISO 8601格式时间，如: 2024-01-15T08:00:00Z
        @Query("duration") duration: String? = null // 视频持续时间（秒），可选
    ): String

    // MARK: - 家庭组相关API

    /**
     * 获取用户的家庭组列表
     */
    @GET("api/family-groups")
    suspend fun getFamilyGroups(@Query("user_id") userId: String): FamilyGroupsResponse

    /**
     * 创建家庭组
     */
    @POST("api/family-groups")
    suspend fun createFamilyGroup(
        @Query("user_id") userId: String,
        @Body request: CreateFamilyGroupRequest
    ): CreateFamilyGroupResponse

    /**
     * 获取家庭组详情
     */
    @GET("api/family-groups/{groupId}")
    suspend fun getFamilyGroupDetail(
        @Path("groupId") groupId: String,
        @Query("user_id") userId: String
    ): FamilyGroupDetailResponse

    // MARK: - 通知相关API

    /**
     * 获取通知设置
     */
    @GET("api/notifications/settings")
    suspend fun getNotificationSettings(@Query("user_id") userId: String): NotificationSettingsResponse

    /**
     * 更新通知设置
     */
    @PUT("api/notifications/settings")
    suspend fun updateNotificationSettings(
        @Query("user_id") userId: String,
        @Body settings: NotificationSettings
    ): ApiResponse<Unit>

    // MARK: - 客户端注册API

    /**
     * 注册客户端设备
     */
    @POST("api/clients/register")
    suspend fun registerClient(@Body request: ClientRegistrationRequest): ClientRegistrationResponse
}