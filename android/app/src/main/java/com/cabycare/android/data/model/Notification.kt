package com.cabycare.android.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * 通知类型枚举
 */
enum class NotificationType(val value: String, val displayName: String) {
    DEVICE_OFFLINE("device_offline", "设备离线"),
    DEVICE_ONLINE("device_online", "设备上线"),
    CAT_HEALTH_ALERT("cat_health_alert", "猫咪健康警报"),
    FAMILY_GROUP_INVITATION("family_group_invitation", "家庭组邀请"),
    FIRMWARE_UPDATE("firmware_update", "固件更新"),
    SYSTEM_MAINTENANCE("system_maintenance", "系统维护"),
    OTHER("other", "其他");
    
    companion object {
        fun fromValue(value: String): NotificationType {
            return values().find { it.value == value } ?: OTHER
        }
    }
}

/**
 * 通知优先级枚举
 */
enum class NotificationPriority(val value: Int, val displayName: String) {
    LOW(1, "低"),
    NORMAL(2, "普通"),
    HIGH(3, "高"),
    URGENT(4, "紧急");
    
    companion object {
        fun fromValue(value: Int): NotificationPriority {
            return values().find { it.value == value } ?: NORMAL
        }
    }
}

/**
 * 通知状态枚举
 */
enum class NotificationStatus(val value: Int, val displayName: String) {
    UNREAD(0, "未读"),
    READ(1, "已读"),
    ARCHIVED(2, "已归档");
    
    companion object {
        fun fromValue(value: Int): NotificationStatus {
            return values().find { it.value == value } ?: UNREAD
        }
    }
}

/**
 * 通知模型
 */
@Serializable
data class NotificationModel(
    val id: String,
    val title: String,
    val content: String,
    val type: String,
    val priority: Int = NotificationPriority.NORMAL.value,
    val status: Int = NotificationStatus.UNREAD.value,
    @SerialName("user_id")
    val userId: String,
    @SerialName("device_id")
    val deviceId: String? = null,
    @SerialName("cat_id")
    val catId: String? = null,
    @SerialName("group_id")
    val groupId: String? = null,
    val metadata: Map<String, String>? = null,
    @SerialName("created_at")
    val createdAt: String,
    @SerialName("updated_at")
    val updatedAt: String,
    @SerialName("read_at")
    val readAt: String? = null
) {
    /**
     * 获取通知类型枚举
     */
    val typeEnum: NotificationType
        get() = NotificationType.fromValue(type)
    
    /**
     * 获取优先级枚举
     */
    val priorityEnum: NotificationPriority
        get() = NotificationPriority.fromValue(priority)
    
    /**
     * 获取状态枚举
     */
    val statusEnum: NotificationStatus
        get() = NotificationStatus.fromValue(status)
    
    /**
     * 是否未读
     */
    val isUnread: Boolean
        get() = statusEnum == NotificationStatus.UNREAD
    
    /**
     * 是否为高优先级
     */
    val isHighPriority: Boolean
        get() = priorityEnum == NotificationPriority.HIGH || priorityEnum == NotificationPriority.URGENT
    
    /**
     * 获取通知图标
     */
    val iconName: String
        get() = when (typeEnum) {
            NotificationType.DEVICE_OFFLINE -> "wifi_off"
            NotificationType.DEVICE_ONLINE -> "wifi"
            NotificationType.CAT_HEALTH_ALERT -> "pets"
            NotificationType.FAMILY_GROUP_INVITATION -> "group_add"
            NotificationType.FIRMWARE_UPDATE -> "system_update"
            NotificationType.SYSTEM_MAINTENANCE -> "build"
            NotificationType.OTHER -> "notifications"
        }
    
    /**
     * 获取格式化的创建时间
     */
    fun getFormattedCreatedAt(): String {
        // TODO: 实现时间格式化逻辑
        return createdAt
    }
    
    /**
     * 获取相对时间
     */
    fun getRelativeTime(): String {
        // TODO: 实现相对时间计算逻辑
        return "刚刚"
    }
}

/**
 * 通知列表响应模型
 */
@Serializable
data class NotificationsResponse(
    val code: Int,
    val status: String,
    val message: String,
    val data: NotificationListData? = null
) {
    @Serializable
    data class NotificationListData(
        val notifications: List<NotificationModel>,
        val total: Int,
        val page: Int,
        val pageSize: Int,
        val hasMore: Boolean
    )
}

/**
 * 标记通知已读请求模型
 */
@Serializable
data class MarkNotificationReadRequest(
    @SerialName("notification_ids")
    val notificationIds: List<String>
)

/**
 * 推送通知设置模型
 */
@Serializable
data class PushNotificationSettings(
    @SerialName("user_id")
    val userId: String,
    @SerialName("device_token")
    val deviceToken: String,
    @SerialName("platform")
    val platform: String, // "android" 或 "ios"
    @SerialName("enabled_types")
    val enabledTypes: List<String> = emptyList(),
    @SerialName("quiet_hours_start")
    val quietHoursStart: String? = null, // 免打扰开始时间，格式: "22:00"
    @SerialName("quiet_hours_end")
    val quietHoursEnd: String? = null, // 免打扰结束时间，格式: "08:00"
    @SerialName("created_at")
    val createdAt: String,
    @SerialName("updated_at")
    val updatedAt: String
)

/**
 * 通知设置模型
 * 对应Swift版本的NotificationSettings
 */
@Serializable
data class NotificationSettings(
    @SerialName("daily_notifications_enabled") val dailyNotificationsEnabled: Boolean = true,
    @SerialName("stats_notifications_enabled") val statsNotificationsEnabled: Boolean = true,
    @SerialName("quiet_time_start") val quietTimeStart: String? = null, // 格式: "22:00"
    @SerialName("quiet_time_end") val quietTimeEnd: String? = null, // 格式: "08:00"
    @SerialName("push_token") val pushToken: String? = null
)

/**
 * 通知设置响应模型
 */
@Serializable
data class NotificationSettingsResponse(
    val status: String,
    val message: String,
    val data: NotificationSettings? = null
) {
    /**
     * 请求是否成功
     */
    val isSuccess: Boolean
        get() = status == "success"
}

/**
 * 客户端注册请求模型
 * 对应Swift版本的ClientRegistrationRequest
 */
@Serializable
data class ClientRegistrationRequest(
    @SerialName("user_id") val userId: String,
    @SerialName("client_id") val clientId: String,
    @SerialName("client_type") val clientType: String = "android",
    val name: String,
    val model: String,
    @SerialName("os_version") val osVersion: String,
    @SerialName("app_version") val appVersion: String
)

/**
 * 客户端注册响应模型
 */
@Serializable
data class ClientRegistrationResponse(
    val status: String,
    val message: String,
    val data: ClientInfo? = null
) {
    /**
     * 请求是否成功
     */
    val isSuccess: Boolean
        get() = status == "success"
}

/**
 * 客户端信息模型
 */
@Serializable
data class ClientInfo(
    @SerialName("client_id") val clientId: String,
    @SerialName("user_id") val userId: String,
    @SerialName("client_type") val clientType: String,
    val name: String,
    val model: String,
    @SerialName("os_version") val osVersion: String,
    @SerialName("app_version") val appVersion: String,
    @SerialName("last_active") val lastActive: String,
    @SerialName("created_at") val createdAt: String
)
