package com.cabycare.android.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * 猫咪性别枚举
 */
enum class CatGender(val value: Int) {
    MALE(0),
    FEMALE(1);
    
    companion object {
        fun fromValue(value: Int): CatGender {
            return values().find { it.value == value } ?: MALE
        }
    }
    
    val displayName: String
        get() = when (this) {
            MALE -> "公猫"
            FEMALE -> "母猫"
        }
}

/**
 * 猫咪类型枚举
 */
enum class CatType(val displayName: String) {
    WHITE("小白"),
    BLACK("小黑"),
    CALICO("三花"),
    TABBY("虎斑"),
    ORANGE("橘猫"),
    OTHER("其他");
    
    companion object {
        fun fromDisplayName(name: String): CatType {
            return values().find { it.displayName == name } ?: OTHER
        }
    }
}

/**
 * 猫咪健康状态枚举
 */
enum class CatHealthStatus(val displayName: String, val colorName: String) {
    HEALTHY("健康", "green"),
    NEEDS_ATTENTION("需要关注", "orange"),
    SICK("生病", "red");
    
    companion object {
        fun fromDisplayName(name: String): CatHealthStatus {
            return values().find { it.displayName == name } ?: HEALTHY
        }
    }
}

/**
 * 猫咪活动水平枚举
 */
enum class CatActivityLevel(val displayName: String) {
    LOW("低"),
    NORMAL("正常"),
    HIGH("高"),
    VERY_HIGH("很高");
    
    companion object {
        fun fromDisplayName(name: String): CatActivityLevel {
            return values().find { it.displayName == name } ?: NORMAL
        }
    }
}

/**
 * 疫苗接种记录
 */
@Serializable
data class Vaccination(
    val id: String,
    val name: String,
    val date: String,
    val nextDueDate: String? = null,
    val veterinarian: String? = null,
    val notes: String? = null
)

/**
 * 药物记录
 */
@Serializable
data class Medication(
    val id: String,
    val name: String,
    val dosage: String,
    val frequency: String,
    val startDate: String,
    val endDate: String? = null,
    val notes: String? = null
)

/**
 * 猫咪档案模型
 * 对应Swift版本的CatProfile模型
 */
@Serializable
data class CatProfile(
    val id: String,
    val name: String,
    val age: String,
    val gender: Int, // 0: 公猫, 1: 母猫
    val weight: String,
    val healthStatus: String = CatHealthStatus.HEALTHY.displayName,
    val activityLevel: String = CatActivityLevel.NORMAL.displayName,
    val type: String = CatType.OTHER.displayName,
    val neuteredStatus: String? = null, // 绝育状态
    @SerialName("avatar_url")
    val avatarUrl: String? = null,
    val birthDate: String? = null,
    val lastCheckupDate: String? = null,
    val vaccinations: List<Vaccination> = emptyList(),
    val medications: List<Medication> = emptyList(),
    val dietaryRestrictions: List<String> = emptyList()
) {
    /**
     * 获取性别枚举
     */
    val genderEnum: CatGender
        get() = CatGender.fromValue(gender)
    
    /**
     * 获取健康状态枚举
     */
    val healthStatusEnum: CatHealthStatus
        get() = CatHealthStatus.fromDisplayName(healthStatus)
    
    /**
     * 获取活动水平枚举
     */
    val activityLevelEnum: CatActivityLevel
        get() = CatActivityLevel.fromDisplayName(activityLevel)
    
    /**
     * 获取猫咪类型枚举
     */
    val typeEnum: CatType
        get() = CatType.fromDisplayName(type)
    
    /**
     * 是否需要关注
     */
    val needsAttention: Boolean
        get() = healthStatusEnum != CatHealthStatus.HEALTHY
}

/**
 * 猫咪API响应模型
 * 对应Swift版本的CatAPIResponse模型
 */
@Serializable
data class CatAPIResponse(
    @SerialName("cat_id")
    val catId: String,
    @SerialName("user_id")
    val userId: String,
    val name: String,
    val birthday: String? = null,
    val gender: Int,
    val color: String? = null,
    val weight: Double,
    val status: Int,
    @SerialName("avatar_url")
    val avatarUrl: String? = null,
    @SerialName("created_at")
    val createdAt: String,
    @SerialName("updated_at")
    val updatedAt: String
) {
    /**
     * 转换为CatProfile模型
     */
    fun toCatProfile(): CatProfile {
        return CatProfile(
            id = catId,
            name = name,
            age = calculateAge(birthday),
            gender = gender,
            weight = "${weight}kg",
            healthStatus = CatHealthStatus.HEALTHY.displayName,
            activityLevel = CatActivityLevel.NORMAL.displayName,
            type = CatType.WHITE.displayName,
            neuteredStatus = null,
            avatarUrl = avatarUrl,
            birthDate = birthday,
            lastCheckupDate = null,
            vaccinations = emptyList(),
            medications = emptyList(),
            dietaryRestrictions = emptyList()
        )
    }
    
    private fun calculateAge(birthday: String?): String {
        // TODO: 实现年龄计算逻辑
        return birthday?.let { "根据生日计算" } ?: "未知"
    }
}

/**
 * 创建猫咪API响应
 * 对应Swift版本的CreateCatAPIResponse
 */
@Serializable
data class CreateCatAPIResponse(
    val status: String,
    val message: String,
    val data: CatAPIResponse
) {
    /**
     * 请求是否成功
     */
    val isSuccess: Boolean
        get() = status == "success"
}

/**
 * 更新猫咪API响应
 * 对应Swift版本的UpdateCatAPIResponse
 */
@Serializable
data class UpdateCatAPIResponse(
    @SerialName("cat_id") val catId: String,
    val name: String,
    val status: Int,
    @SerialName("updated_at") val updatedAt: String
) {
    /**
     * 请求是否成功
     */
    val isSuccess: Boolean
        get() = status == 1
}
