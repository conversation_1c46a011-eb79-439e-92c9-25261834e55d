package com.cabycare.android.ui.settings

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Storage
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.cabycare.android.data.cache.CacheInfo

/**
 * 缓存设置页面
 * 显示缓存状态和提供缓存管理功能
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CacheSettingsScreen(
    onBackClick: () -> Unit = {},
    viewModel: CacheSettingsViewModel = hiltViewModel()
) {
    val cacheInfo by viewModel.cacheInfo.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val operationMessage by viewModel.operationMessage.collectAsState()
    
    var showClearDialog by remember { mutableStateOf(false) }
    
    // 显示操作结果消息
    LaunchedEffect(operationMessage) {
        operationMessage?.let {
            // 自动清除消息
            kotlinx.coroutines.delay(3000)
            viewModel.clearMessage()
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("缓存设置") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.Storage, contentDescription = "返回")
                    }
                },
                actions = {
                    IconButton(
                        onClick = { viewModel.refreshCacheInfo() },
                        enabled = !isLoading
                    ) {
                        Icon(Icons.Default.Refresh, contentDescription = "刷新")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 缓存状态卡片
            CacheStatusCard(
                cacheInfo = cacheInfo,
                isLoading = isLoading
            )
            
            // 缓存管理操作
            CacheManagementCard(
                onClearCache = { showClearDialog = true },
                isLoading = isLoading
            )
            
            // 缓存说明
            CacheExplanationCard()
            
            // 操作消息显示
            operationMessage?.let { message ->
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = if (message.startsWith("✅")) {
                            MaterialTheme.colorScheme.primaryContainer
                        } else {
                            MaterialTheme.colorScheme.errorContainer
                        }
                    )
                ) {
                    Text(
                        text = message,
                        modifier = Modifier.padding(16.dp),
                        color = if (message.startsWith("✅")) {
                            MaterialTheme.colorScheme.onPrimaryContainer
                        } else {
                            MaterialTheme.colorScheme.onErrorContainer
                        }
                    )
                }
            }
        }
    }
    
    // 清除缓存确认对话框
    if (showClearDialog) {
        AlertDialog(
            onDismissRequest = { showClearDialog = false },
            title = { Text("清除缓存") },
            text = { 
                Text("确定要清除所有视频缓存吗？这将删除已下载的视频内容，下次播放时需要重新缓冲。") 
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        viewModel.clearAllCache()
                        showClearDialog = false
                    }
                ) {
                    Text("确定")
                }
            },
            dismissButton = {
                TextButton(onClick = { showClearDialog = false }) {
                    Text("取消")
                }
            }
        )
    }
}

/**
 * 缓存状态卡片
 */
@Composable
private fun CacheStatusCard(
    cacheInfo: CacheInfo,
    isLoading: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.Storage,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "缓存状态",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            if (isLoading) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("获取缓存信息中...")
                }
            } else {
                // 缓存大小
                InfoRow(
                    label = "已使用空间",
                    value = cacheInfo.getFormattedSize()
                )
                
                // 利用率
                InfoRow(
                    label = "使用率",
                    value = cacheInfo.getFormattedUtilization()
                )
                
                // 缓存项数量
                InfoRow(
                    label = "缓存视频数",
                    value = "${cacheInfo.cachedItemsCount} 个"
                )
                
                // 缓存利用率进度条
                Spacer(modifier = Modifier.height(8.dp))
                LinearProgressIndicator(
                    progress = cacheInfo.utilizationPercent / 100f,
                    modifier = Modifier.fillMaxWidth(),
                    color = when {
                        cacheInfo.utilizationPercent < 50 -> MaterialTheme.colorScheme.primary
                        cacheInfo.utilizationPercent < 80 -> MaterialTheme.colorScheme.tertiary
                        else -> MaterialTheme.colorScheme.error
                    }
                )
            }
        }
    }
}

/**
 * 缓存管理卡片
 */
@Composable
private fun CacheManagementCard(
    onClearCache: () -> Unit,
    isLoading: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "缓存管理",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Button(
                onClick = onClearCache,
                enabled = !isLoading,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer,
                    contentColor = MaterialTheme.colorScheme.onErrorContainer
                )
            ) {
                Icon(Icons.Default.Delete, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text("清除所有缓存")
            }
        }
    }
}

/**
 * 缓存说明卡片
 */
@Composable
private fun CacheExplanationCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "关于视频缓存",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = "• 自动缓存：视频播放时会自动缓存到本地，提升后续观看体验",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "• 智能管理：当缓存空间不足时，会自动删除最久未使用的视频",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "• 离线播放：已缓存的视频在网络不佳时仍可正常播放",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 信息行组件
 */
@Composable
private fun InfoRow(
    label: String,
    value: String
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )
    }
}
