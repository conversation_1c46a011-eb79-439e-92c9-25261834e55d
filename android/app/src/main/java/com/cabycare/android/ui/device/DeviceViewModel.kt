package com.cabycare.android.ui.device

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import com.cabycare.android.data.model.*
import com.cabycare.android.data.network.NetworkResult
import com.cabycare.android.data.repository.DeviceRepository
import com.cabycare.android.data.repository.UserRepository
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import kotlinx.coroutines.Job
import android.util.Log
import javax.inject.Inject

/**
 * 设备管理UI状态
 */
data class DeviceUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val devices: List<AccessibleDeviceWithStatus> = emptyList(), // 使用带实时状态的设备
    val showAddDeviceDialog: Boolean = false,
    val showSettingsDialog: Boolean = false,
    val selectedDevice: AccessibleDeviceWithStatus? = null, // 修改为带状态的设备
    val isSubmitting: Boolean = false,
    val otaStatus: Map<String, String> = emptyMap(), // deviceId -> status
    val isRefreshing: Boolean = false, // 是否正在刷新
    val lastRefreshTime: Long = 0L, // 最后刷新时间
    val onlineDeviceCount: Int = 0 // 在线设备数量
)

/**
 * 设备管理ViewModel
 */
@HiltViewModel
class DeviceViewModel @Inject constructor(
    private val deviceRepository: DeviceRepository,
    private val userRepository: UserRepository
) : ViewModel() {
    
    companion object {
        private const val TAG = "DeviceViewModel"
        private const val REFRESH_INTERVAL = 30000L // 30秒刷新一次
        private const val FAST_REFRESH_INTERVAL = 10000L // 快速刷新间隔（10秒）
    }
    
    private val _uiState = MutableStateFlow(DeviceUiState())
    val uiState: StateFlow<DeviceUiState> = _uiState.asStateFlow()
    
    private var refreshJob: Job? = null
    private var isAutoRefreshEnabled = true
    
    init {
        // 启动时自动加载设备
        loadDevicesWithRealTimeStatus()
        startAutoRefresh()
    }
    
    override fun onCleared() {
        super.onCleared()
        stopAutoRefresh()
    }
    
    /**
     * 加载设备列表（使用实时状态API）
     */
    fun loadDevicesWithRealTimeStatus() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            try {
                // 获取当前用户ID
                val userId = userRepository.getUserId().first()
                if (userId.isNullOrEmpty()) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "用户未登录"
                    )
                    return@launch
                }

                Log.d(TAG, "🔄 开始加载设备列表及实时状态")
                
                // 使用新的API获取设备及其实时状态
                when (val result = deviceRepository.getAccessibleDevicesWithStatus(userId)) {
                    is NetworkResult.Success -> {
                        val devicesWithStatus = result.data
                        val onlineCount = devicesWithStatus.count { it.isOnline }
                        
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            devices = sortDevices(devicesWithStatus), // 应用排序
                            onlineDeviceCount = onlineCount,
                            lastRefreshTime = System.currentTimeMillis(),
                            error = null
                        )

                        Log.d(TAG, "✅ 设备列表加载成功: 总数=${devicesWithStatus.size}, 在线=$onlineCount")

                        // 加载每个设备的OTA状态
                        loadOTAStatuses(devicesWithStatus.map { it.device })
                    }
                    is NetworkResult.Error -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = result.exception.message ?: "加载设备列表失败"
                        )
                        Log.e(TAG, "❌ 加载设备列表失败: ${result.exception.message}")
                    }
                    else -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = "未知错误"
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "获取用户信息失败: ${e.message}"
                )
                Log.e(TAG, "❌ 加载设备时发生异常", e)
            }
        }
    }
    
    /**
     * 刷新设备状态（仅刷新在线状态，不重新获取设备列表）
     */
    fun refreshDeviceStatus() {
        viewModelScope.launch {
            if (_uiState.value.isRefreshing) {
                Log.d(TAG, "⚠️ 正在刷新中，跳过重复请求")
                return@launch
            }
            
            _uiState.value = _uiState.value.copy(isRefreshing = true)
            
            try {
                val currentDevices = _uiState.value.devices
                if (currentDevices.isEmpty()) {
                    Log.d(TAG, "📱 设备列表为空，执行完整加载")
                    loadDevicesWithRealTimeStatus()
                    return@launch
                }
                
                Log.d(TAG, "🔄 刷新 ${currentDevices.size} 台设备的状态")
                
                val deviceIds = currentDevices.map { it.deviceId }
                when (val result = deviceRepository.getDevicesStatus(deviceIds)) {
                    is NetworkResult.Success -> {
                        val statusMap = result.data
                        
                        // 更新设备状态
                        val updatedDevices = currentDevices.map { deviceWithStatus ->
                            val newStatus = statusMap[deviceWithStatus.deviceId]
                            if (newStatus != null) {
                                deviceWithStatus.copy(
                                    realTimeStatus = newStatus,
                                    isOnline = newStatus.online ?: false,
                                    lastHeartbeat = newStatus.lastSeen
                                )
                            } else {
                                // 如果无法获取新状态，标记为离线
                                deviceWithStatus.copy(
                                    isOnline = false,
                                    lastHeartbeat = null
                                )
                            }
                        }
                        
                        val onlineCount = updatedDevices.count { it.isOnline }
                        
                        _uiState.value = _uiState.value.copy(
                            devices = sortDevices(updatedDevices), // 应用排序
                            onlineDeviceCount = onlineCount,
                            lastRefreshTime = System.currentTimeMillis(),
                            isRefreshing = false
                        )
                        
                        Log.d(TAG, "✅ 设备状态刷新完成: 在线设备 $onlineCount/${updatedDevices.size}")
                    }
                    is NetworkResult.Error -> {
                        _uiState.value = _uiState.value.copy(
                            isRefreshing = false,
                            error = "刷新设备状态失败: ${result.exception.message}"
                        )
                        Log.e(TAG, "❌ 刷新设备状态失败: ${result.exception.message}")
                    }
                    else -> {
                        _uiState.value = _uiState.value.copy(
                            isRefreshing = false,
                            error = "刷新失败：未知错误"
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isRefreshing = false,
                    error = "刷新异常: ${e.message}"
                )
                Log.e(TAG, "❌ 刷新设备状态时发生异常", e)
            }
        }
    }
    
    /**
     * 启动自动刷新
     */
    private fun startAutoRefresh() {
        if (!isAutoRefreshEnabled) return
        
        refreshJob = viewModelScope.launch {
            while (isAutoRefreshEnabled) {
                delay(REFRESH_INTERVAL)
                
                if (isAutoRefreshEnabled && !_uiState.value.isLoading) {
                    Log.d(TAG, "🔄 执行自动刷新")
                    refreshDeviceStatus()
                }
            }
        }
        
        Log.d(TAG, "⏰ 自动刷新已启动，间隔: ${REFRESH_INTERVAL / 1000}秒")
    }
    
    /**
     * 停止自动刷新
     */
    private fun stopAutoRefresh() {
        isAutoRefreshEnabled = false
        refreshJob?.cancel()
        refreshJob = null
        Log.d(TAG, "⏰ 自动刷新已停止")
    }
    
    /**
     * 启用快速刷新模式（用于重要操作后）
     */
    fun enableFastRefresh() {
        viewModelScope.launch {
            Log.d(TAG, "⚡ 启用快速刷新模式")
            
            // 立即刷新一次
            refreshDeviceStatus()
            
            // 然后进行3次快速刷新
            repeat(3) {
                delay(FAST_REFRESH_INTERVAL)
                if (!_uiState.value.isLoading) {
                    refreshDeviceStatus()
                }
            }
            
            Log.d(TAG, "⚡ 快速刷新模式结束")
        }
    }
    
    /**
     * 加载设备OTA状态
     */
    private fun loadOTAStatuses(devices: List<AccessibleDevice>) {
        viewModelScope.launch {
            val otaStatuses = mutableMapOf<String, String>()
            
            devices.forEach { device ->
                when (val result = deviceRepository.getDeviceOTAStatus(device.deviceId)) {
                    is NetworkResult.Success -> {
                        otaStatuses[device.deviceId] = result.data.status
                    }
                    else -> {
                        otaStatuses[device.deviceId] = "unknown"
                    }
                }
            }
            
            _uiState.value = _uiState.value.copy(otaStatus = otaStatuses)
        }
    }
    
    /**
     * 刷新设备列表（完整刷新）
     */
    fun refreshDevices() {
        loadDevicesWithRealTimeStatus()
    }
    
    /**
     * 手动刷新（用户主动触发）
     */
    fun manualRefresh() {
        Log.d(TAG, "👤 用户手动刷新")
        refreshDeviceStatus()
    }
    
    /**
     * 更新设备信息
     */
    fun updateDevice(device: AccessibleDeviceWithStatus) {
        viewModelScope.launch {
            // 将AccessibleDeviceWithStatus转换为Device进行更新
            val deviceToUpdate = device.device.toDevice()
            when (val result = deviceRepository.updateDevice(device.deviceId, deviceToUpdate)) {
                is NetworkResult.Success -> {
                    // 更新成功后，启用快速刷新获取最新状态
                    enableFastRefresh()
                }
                is NetworkResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        error = result.exception.message ?: "更新设备信息失败"
                    )
                }
                else -> {
                    _uiState.value = _uiState.value.copy(error = "未知错误")
                }
            }
        }
    }
    
    /**
     * 触发OTA更新
     */
    fun triggerOTAUpdate(deviceId: String) {
        viewModelScope.launch {
            when (val result = deviceRepository.triggerOTAUpdate(deviceId)) {
                is NetworkResult.Success -> {
                    // 更新OTA状态
                    val updatedOTAStatus = _uiState.value.otaStatus.toMutableMap()
                    updatedOTAStatus[deviceId] = "updating"
                    _uiState.value = _uiState.value.copy(otaStatus = updatedOTAStatus)
                    
                    // 启用快速刷新监控OTA进度
                    enableFastRefresh()
                }
                is NetworkResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        error = result.exception.message ?: "触发OTA更新失败"
                    )
                }
                else -> {
                    _uiState.value = _uiState.value.copy(error = "未知错误")
                }
            }
        }
    }
    
    /**
     * 显示添加设备对话框
     */
    fun showAddDeviceDialog() {
        _uiState.value = _uiState.value.copy(showAddDeviceDialog = true)
    }
    
    /**
     * 隐藏添加设备对话框
     */
    fun hideAddDeviceDialog() {
        _uiState.value = _uiState.value.copy(
            showAddDeviceDialog = false,
            isSubmitting = false
        )
    }
    
    /**
     * 添加设备
     */
    fun addDevice(deviceInfo: DeviceInfo) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isSubmitting = true)
            
            // 实际项目中应该调用设备注册API
            // 这里只是模拟
            kotlinx.coroutines.delay(2000)
            
            _uiState.value = _uiState.value.copy(
                isSubmitting = false,
                showAddDeviceDialog = false
            )
            
            // 设备添加后，重新加载设备列表
            loadDevicesWithRealTimeStatus()
        }
    }
    
    /**
     * 显示设备设置对话框
     */
    fun showSettingsDialog(device: AccessibleDeviceWithStatus) {
        _uiState.value = _uiState.value.copy(
            selectedDevice = device,
            showSettingsDialog = true
        )
    }
    
    /**
     * 隐藏设备设置对话框
     */
    fun hideSettingsDialog() {
        _uiState.value = _uiState.value.copy(
            selectedDevice = null,
            showSettingsDialog = false
        )
    }
    
    /**
     * 删除设备
     */
    fun deleteDevice(deviceId: String) {
        viewModelScope.launch {
            when (val result = deviceRepository.deleteDevice(deviceId)) {
                is NetworkResult.Success -> {
                    // 删除成功后，重新加载设备列表
                    loadDevicesWithRealTimeStatus()
                }
                is NetworkResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        error = result.exception.message ?: "删除设备失败"
                    )
                }
                else -> {
                    _uiState.value = _uiState.value.copy(error = "删除失败：未知错误")
                }
            }
        }
    }
    
    /**
     * 清除错误信息
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    /**
     * 对设备列表进行排序
     * 排序规则：在线设备优先 > 未知状态 > 离线设备，相同状态下按设备名称排序
     * 参考iOS版本的排序逻辑
     */
    private fun sortDevices(devices: List<AccessibleDeviceWithStatus>): List<AccessibleDeviceWithStatus> {
        return devices.sortedWith { device1, device2 ->
            // 定义优先级：在线(2) > 未知(1) > 离线(0)
            val priority1 = when {
                device1.isOnline -> 2
                device1.hasRealTimeData -> 1
                else -> 0
            }
            val priority2 = when {
                device2.isOnline -> 2
                device2.hasRealTimeData -> 1
                else -> 0
            }
            
            // 首先按优先级排序
            if (priority1 != priority2) {
                priority2 - priority1 // 降序排列，优先级高的在前
            } else {
                // 如果优先级相同，按设备名称排序
                device1.displayName.compareTo(device2.displayName, ignoreCase = true)
            }
        }
    }
}
