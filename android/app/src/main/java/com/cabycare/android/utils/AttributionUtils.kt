package com.cabycare.android.utils

import android.content.Context
import android.os.Build
import androidx.annotation.RequiresApi

/**
 * Attribution utilities for handling AppOps attribution tags
 */
object AttributionUtils {
    
    // Attribution tags defined in manifest
    const val TAG_VIDEO_STREAMING = "video_streaming"
    const val TAG_NETWORK_ACCESS = "network_access"
    const val TAG_MEDIA_PLAYBACK = "media_playback"
    const val TAG_DEVICE_MONITORING = "device_monitoring"
    
    /**
     * Create an attribution context for the given tag
     */
    @RequiresApi(Build.VERSION_CODES.S)
    fun createAttributionContext(context: Context, attributionTag: String): Context {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            context.createAttributionContext(attributionTag)
        } else {
            context
        }
    }
    
    /**
     * Get attribution context for video streaming operations
     */
    fun getVideoStreamingContext(context: Context): Context {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            createAttributionContext(context, TAG_VIDEO_STREAMING)
        } else {
            context
        }
    }
    
    /**
     * Get attribution context for network access operations
     */
    fun getNetworkAccessContext(context: Context): Context {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            createAttributionContext(context, TAG_NETWORK_ACCESS)
        } else {
            context
        }
    }
    
    /**
     * Get attribution context for media playback operations
     */
    fun getMediaPlaybackContext(context: Context): Context {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            createAttributionContext(context, TAG_MEDIA_PLAYBACK)
        } else {
            context
        }
    }
    
    /**
     * Get attribution context for device monitoring operations
     */
    fun getDeviceMonitoringContext(context: Context): Context {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            createAttributionContext(context, TAG_DEVICE_MONITORING)
        } else {
            context
        }
    }
} 