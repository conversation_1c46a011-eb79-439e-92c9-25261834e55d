package com.cabycare.android.data.model

/**
 * 设备状态信息数据类
 * 用于展示设备的实时状态信息，与DeviceStatus枚举区分
 */
data class DeviceStatusInfo(
    val deviceId: String,
    val name: String,
    val isOnline: Boolean,
    val statusSource: String, // "实时数据" 或 "缓存数据"
    val hasRealTimeData: Boolean,
    val batteryLevel: Int? = null, // 电池电量百分比
    val temperature: Double? = null, // 温度
    val humidity: Double? = null, // 湿度
    val lastUpdated: String? = null // 最后更新时间
) {
    companion object {
        /**
         * 从设备响应创建设备状态信息
         */
        fun fromDeviceResponse(device: DeviceStatusResponse): DeviceStatusInfo {
            return DeviceStatusInfo(
                deviceId = device.deviceId,
                name = device.name,
                isOnline = device.online ?: false,
                statusSource = if (device.online != null) "实时数据" else "缓存数据",
                hasRealTimeData = device.online != null,
                batteryLevel = device.batteryLevel,
                temperature = device.temperature,
                humidity = device.humidity,
                lastUpdated = device.lastSeen
            )
        }
        
        /**
         * 从可访问设备创建设备状态信息
         */
        fun fromDeviceResponse(device: AccessibleDevice): DeviceStatusInfo {
            return DeviceStatusInfo(
                deviceId = device.deviceId,
                name = device.name,
                isOnline = device.isOnline,
                statusSource = "缓存数据", // AccessibleDevice通常来自缓存
                hasRealTimeData = false,
                batteryLevel = null, // AccessibleDevice中没有这些数据
                temperature = null,
                humidity = null,
                lastUpdated = device.lastHeartbeat
            )
        }
    }
} 