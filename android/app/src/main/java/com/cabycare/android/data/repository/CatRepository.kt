package com.cabycare.android.data.repository

import android.util.Log
import com.cabycare.android.data.model.*
import com.cabycare.android.data.network.ApiService
import com.cabycare.android.data.network.NetworkResult
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 猫咪数据仓库
 * 负责管理猫咪相关的数据操作
 */
@Singleton
class CatRepository @Inject constructor(
    private val apiService: ApiService
) : BaseRepository() {

    companion object {
        private const val TAG = "CatRepository"
    }
    
    /**
     * 获取猫咪列表
     */
    suspend fun getCats(): NetworkResult<List<CatProfile>> {
        Log.d(TAG, "获取猫咪列表")
        return handleListResult(
            safeApiCall {
                val apiCats = apiService.getCats()
                apiCats.map { mapApiResponseToCatProfile(it) }
            }
        )
    }
    
    /**
     * 获取猫咪详情
     */
    suspend fun getCatDetail(catId: String): NetworkResult<CatProfile> {
        Log.d(TAG, "获取猫咪详情: $catId")
        return handleSingleResult(
            safeApiCall {
                val apiCat = apiService.getCatDetail(catId)
                mapApiResponseToCatProfile(apiCat)
            }
        )
    }
    
    /**
     * 创建猫咪档案
     */
    suspend fun createCat(cat: CatProfile): NetworkResult<CatProfile> {
        Log.d(TAG, "创建猫咪档案: ${cat.name}")
        return handleSingleResult(
            safeApiCall {
                val request = mapCatProfileToCreateRequest(cat)
                val response = apiService.createCat(request)
                if (response.isSuccess) {
                    mapApiResponseToCatProfile(response.data)
                } else {
                    throw RuntimeException(response.message)
                }
            }
        )
    }

    /**
     * 更新猫咪档案
     */
    suspend fun updateCat(catId: String, cat: CatProfile): NetworkResult<CatProfile> {
        Log.d(TAG, "更新猫咪档案: $catId")
        return handleSingleResult(
            safeApiCall {
                val request = mapCatProfileToUpdateRequest(cat)
                val response = apiService.updateCat(catId, request)
                if (response.isSuccess) {
                    // 重新获取完整信息
                    val fullCat = apiService.getCatDetail(catId)
                    mapApiResponseToCatProfile(fullCat)
                } else {
                    throw Exception("更新失败")
                }
            }
        )
    }

    /**
     * 删除猫咪档案
     */
    suspend fun deleteCat(catId: String): NetworkResult<Unit> {
        Log.d(TAG, "删除猫咪档案: $catId")
        return handleSingleResult(
            safeApiCall {
                val response = apiService.deleteCat(catId)
                Unit
            }
        )
    }

    // TODO: 实现其他猫咪相关API调用

    /**
     * 将API响应转换为本地模型
     */
    private fun mapApiResponseToCatProfile(apiResponse: CatAPIResponse): CatProfile {
        return CatProfile(
            id = apiResponse.catId,
            name = apiResponse.name,
            age = calculateAge(apiResponse.birthday),
            gender = apiResponse.gender,
            weight = if (apiResponse.weight > 0) "${apiResponse.weight}kg" else "未知",
            healthStatus = CatHealthStatus.HEALTHY.displayName,
            activityLevel = CatActivityLevel.NORMAL.displayName,
            type = CatType.WHITE.displayName,
            neuteredStatus = null,
            avatarUrl = apiResponse.avatarUrl,
            birthDate = apiResponse.birthday,
            lastCheckupDate = null,
            vaccinations = emptyList(),
            medications = emptyList(),
            dietaryRestrictions = emptyList()
        )
    }

    /**
     * 计算年龄
     */
    private fun calculateAge(birthday: String?): String {
        if (birthday.isNullOrEmpty()) return "未知"

        return try {
            // 这里应该实现实际的年龄计算逻辑
            // 暂时返回固定值
            "2岁"
        } catch (e: Exception) {
            Log.w(TAG, "计算年龄失败: $birthday", e)
            "未知"
        }
    }

    /**
     * 解析日期
     */
    private fun parseDate(dateString: String?): java.util.Date? {
        if (dateString.isNullOrEmpty()) return null

        return try {
            // 这里应该实现实际的日期解析逻辑
            // 暂时返回null
            null
        } catch (e: Exception) {
            Log.w(TAG, "解析日期失败: $dateString", e)
            null
        }
    }

    /**
     * 将本地模型转换为创建请求
     */
    private fun mapCatProfileToCreateRequest(catProfile: CatProfile): CatProfile {
        // 暂时直接返回CatProfile，等API更新后再修改
        return catProfile
    }

    /**
     * 将本地模型转换为更新请求
     */
    private fun mapCatProfileToUpdateRequest(catProfile: CatProfile): CatProfile {
        // 暂时直接返回CatProfile，等API更新后再修改
        return catProfile
    }
}
