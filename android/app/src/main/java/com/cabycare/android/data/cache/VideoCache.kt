package com.cabycare.android.data.cache

import android.content.Context
import android.util.Log
import androidx.media3.database.StandaloneDatabaseProvider
import androidx.media3.datasource.cache.LeastRecentlyUsedCacheEvictor
import androidx.media3.datasource.cache.SimpleCache
import dagger.hilt.android.qualifiers.ApplicationContext
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 视频缓存管理器
 * 使用ExoPlayer的SimpleCache来自动处理HLS流的缓存
 */
@Singleton
class VideoCache @Inject constructor(
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val TAG = "VideoCache"
        
        // 缓存设置
        private const val CACHE_SIZE_BYTES = 100L * 1024 * 1024 // 100MB
        private const val CACHE_FOLDER_NAME = "video_cache"
    }
    
    private var _cache: SimpleCache? = null
    
    /**
     * 获取或创建缓存实例
     */
    val cache: SimpleCache
        get() {
            if (_cache == null) {
                initializeCache()
            }
            return _cache!!
        }
    
    /**
     * 初始化缓存
     */
    private fun initializeCache() {
        try {
            val cacheDir = File(context.cacheDir, CACHE_FOLDER_NAME)
            val databaseProvider = StandaloneDatabaseProvider(context)
            val cacheEvictor = LeastRecentlyUsedCacheEvictor(CACHE_SIZE_BYTES)
            
            _cache = SimpleCache(cacheDir, cacheEvictor, databaseProvider)
            
            Log.d(TAG, "✅ 视频缓存初始化成功")
            Log.d(TAG, "📁 缓存目录: ${cacheDir.absolutePath}")
            Log.d(TAG, "💾 最大缓存大小: ${CACHE_SIZE_BYTES / 1024 / 1024}MB")
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ 初始化视频缓存失败", e)
            throw e
        }
    }
    
    /**
     * 获取缓存状态信息
     */
    fun getCacheInfo(): CacheInfo {
        return try {
            val cacheSize = cache.cacheSpace
            val keys = cache.keys
            
            CacheInfo(
                totalSizeBytes = cacheSize,
                totalSizeMB = cacheSize / 1024 / 1024,
                maxSizeBytes = CACHE_SIZE_BYTES,
                maxSizeMB = CACHE_SIZE_BYTES / 1024 / 1024,
                cachedItemsCount = keys.size,
                utilizationPercent = (cacheSize * 100 / CACHE_SIZE_BYTES).toInt()
            )
        } catch (e: Exception) {
            Log.e(TAG, "获取缓存信息失败", e)
            CacheInfo.empty()
        }
    }
    
    /**
     * 清除所有缓存
     */
    fun clearCache() {
        try {
            cache.keys.forEach { key ->
                cache.removeResource(key)
            }
            Log.d(TAG, "🗑️ 缓存已清理")
        } catch (e: Exception) {
            Log.e(TAG, "清理缓存失败", e)
        }
    }
    
    /**
     * 清除特定视频的缓存
     */
    fun clearCacheForUrl(url: String) {
        try {
            cache.removeResource(url)
            Log.d(TAG, "🗑️ 已清理URL缓存: $url")
        } catch (e: Exception) {
            Log.e(TAG, "清理特定URL缓存失败", e)
        }
    }
    
    /**
     * 检查某个URL是否已缓存
     */
    fun isCached(url: String): Boolean {
        return try {
            cache.isCached(url, 0, 1)
        } catch (e: Exception) {
            Log.e(TAG, "检查缓存状态失败", e)
            false
        }
    }
    
    /**
     * 释放缓存资源
     */
    fun release() {
        try {
            _cache?.release()
            _cache = null
            Log.d(TAG, "🔄 缓存资源已释放")
        } catch (e: Exception) {
            Log.e(TAG, "释放缓存资源失败", e)
        }
    }
}

/**
 * 缓存信息数据类
 */
data class CacheInfo(
    val totalSizeBytes: Long,
    val totalSizeMB: Long,
    val maxSizeBytes: Long,
    val maxSizeMB: Long,
    val cachedItemsCount: Int,
    val utilizationPercent: Int
) {
    companion object {
        fun empty() = CacheInfo(0, 0, 0, 0, 0, 0)
    }
    
    fun getFormattedSize(): String = "${totalSizeMB}MB / ${maxSizeMB}MB"
    fun getFormattedUtilization(): String = "$utilizationPercent%"
}
