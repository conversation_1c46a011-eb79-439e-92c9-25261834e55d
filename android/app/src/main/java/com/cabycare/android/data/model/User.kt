package com.cabycare.android.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.util.Date

/**
 * 用户数据模型
 * 对应Swift版本的User模型
 */
@Serializable
data class User(
    @SerialName("user_id")
    val userId: String,
    val username: String,
    val email: String,
    val phone: String,
    val nickname: String,
    val status: Int,
    @SerialName("created_at")
    val createdAt: String, // 使用String类型，后续转换为Date
    @SerialName("updated_at")
    val updatedAt: String
) {
    /**
     * 用户是否活跃
     */
    val isActive: Boolean
        get() = status == 1
        
    /**
     * 显示名称（优先使用nickname，否则使用username）
     */
    val displayName: String
        get() = nickname.ifEmpty { username }
}

/**
 * OAuth回调响应模型
 * 对应Swift版本的OAuthCallbackResponse
 */
@Serializable
data class OAuthCallbackResponse(
    val status: String,
    val message: String,
    @SerialName("user_id") val userId: String
) {
    /**
     * 请求是否成功
     */
    val isSuccess: Boolean
        get() = status == "success"
}

/**
 * 用户信息API响应模型
 * 对应 /api/user/info 的直接响应格式
 */
@Serializable
data class UserInfoResponse(
    val email: String,
    @SerialName("logto_id") val logtoId: String,
    @SerialName("user_id") val userId: String,
    val username: String,
    val message: String,
    val nickname: String? = null,
    @SerialName("logto_user_info") val logtoUserInfo: LogtoUserInfo? = null
)

/**
 * Logto用户信息
 */
@Serializable
data class LogtoUserInfo(
    val sub: String,
    val name: String? = null,
    val email: String? = null,
    val picture: String? = null,
    @SerialName("email_verified") val emailVerified: Boolean? = null,
    val locale: String? = null,
    @SerialName("updated_at") val updatedAt: Long? = null
)

/**
 * 完整的用户认证信息
 * 包含所有需要持久化存储的用户数据
 */
@Serializable
data class CompleteUserInfo(
    // 基本用户信息
    val userId: String? = null,           // 后端数据库中的用户ID
    val logtoId: String? = null,          // Logto认证服务的用户ID
    val email: String? = null,            // 用户邮箱
    val username: String? = null,         // 用户名
    val nickname: String? = null,         // 昵称
    val phone: String? = null,            // 电话号码

    // 认证令牌信息
    val accessToken: String? = null,      // 访问令牌
    val refreshToken: String? = null,     // 刷新令牌
    val idToken: String? = null,          // ID令牌
    val tokenType: String? = "Bearer",    // 令牌类型
    val expiresIn: Long? = null,          // 令牌过期时间（秒）
    val scope: String? = null,            // 令牌作用域
    val tokenIssuedAt: Long? = null,      // 令牌签发时间戳

    // Logto用户详细信息
    val logtoUserInfo: LogtoUserInfo? = null,

    // 用户状态
    val status: Int? = null,              // 用户状态
    val isAuthenticated: Boolean = false, // 是否已认证
    val lastLoginTime: Long? = null,      // 最后登录时间戳

    // 用户偏好设置
    val themeMode: String? = "system",    // 主题模式: "light", "dark", "system"
    val language: String? = "zh",         // 语言设置: "zh", "en"
    val notificationEnabled: Boolean = true, // 通知开关
    val videoQuality: String? = "medium", // 视频质量: "low", "medium", "high"
    val autoPlay: Boolean = true,         // 自动播放

    // 时间戳
    val createdAt: String? = null,        // 创建时间
    val updatedAt: String? = null         // 更新时间
) {
    /**
     * 检查访问令牌是否过期
     */
    fun isAccessTokenExpired(): Boolean {
        if (tokenIssuedAt == null || expiresIn == null) return true
        val currentTime = System.currentTimeMillis() / 1000
        return currentTime >= (tokenIssuedAt + expiresIn)
    }

    /**
     * 检查访问令牌是否即将过期（5分钟内）
     */
    fun isAccessTokenExpiringSoon(): Boolean {
        if (tokenIssuedAt == null || expiresIn == null) return true
        val currentTime = System.currentTimeMillis() / 1000
        val expirationTime = tokenIssuedAt + expiresIn
        return currentTime >= (expirationTime - 300) // 5分钟 = 300秒
    }

    /**
     * 获取显示名称
     */
    fun getDisplayName(): String {
        return nickname?.takeIf { it.isNotEmpty() }
            ?: username?.takeIf { it.isNotEmpty() }
            ?: email?.takeIf { it.isNotEmpty() }
            ?: "用户"
    }

    /**
     * 检查是否有有效的刷新令牌
     */
    fun hasValidRefreshToken(): Boolean {
        return !refreshToken.isNullOrEmpty()
    }

    /**
     * 检查是否有有效的访问令牌
     */
    fun hasValidAccessToken(): Boolean {
        return !accessToken.isNullOrEmpty() && !isAccessTokenExpired()
    }

    /**
     * 创建一个清除认证信息的副本
     */
    fun clearAuthInfo(): CompleteUserInfo {
        return this.copy(
            accessToken = null,
            refreshToken = null,
            idToken = null,
            tokenType = "Bearer",
            expiresIn = null,
            tokenIssuedAt = null,
            isAuthenticated = false,
            lastLoginTime = null
        )
    }

    /**
     * 更新令牌信息
     */
    fun updateTokens(
        newAccessToken: String,
        newRefreshToken: String? = null,
        newExpiresIn: Long? = null,
        newIdToken: String? = null
    ): CompleteUserInfo {
        return this.copy(
            accessToken = newAccessToken,
            refreshToken = newRefreshToken ?: this.refreshToken,
            idToken = newIdToken ?: this.idToken,
            expiresIn = newExpiresIn,
            tokenIssuedAt = System.currentTimeMillis() / 1000,
            isAuthenticated = true,
            lastLoginTime = System.currentTimeMillis()
        )
    }
}


