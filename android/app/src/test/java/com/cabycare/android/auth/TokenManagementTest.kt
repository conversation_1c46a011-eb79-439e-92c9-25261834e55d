package com.cabycare.android.auth

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import com.cabycare.android.data.auth.LogtoManager
import com.cabycare.android.data.local.UserPreferences
import com.cabycare.android.data.model.CompleteUserInfo
import com.cabycare.android.data.model.LogtoUserInfo
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * Token管理测试
 * 测试新的token管理逻辑，包括完整用户信息存储和token刷新
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [28])
class TokenManagementTest {

    private lateinit var context: Context
    private lateinit var userPreferences: UserPreferences

    @Before
    fun setup() {
        context = ApplicationProvider.getApplicationContext()
        userPreferences = UserPreferences(context)
    }

    @Test
    fun `test complete user info storage and retrieval`() = runTest {
        // 创建测试用户信息
        val testUserInfo = CompleteUserInfo(
            logtoId = "test_logto_id",
            email = "<EMAIL>",
            username = "testuser",
            nickname = "Test User",
            accessToken = "test_access_token",
            refreshToken = "test_refresh_token",
            idToken = "test_id_token",
            tokenType = "Bearer",
            expiresIn = 3600,
            scope = "openid profile email offline_access",
            tokenIssuedAt = System.currentTimeMillis() / 1000,
            logtoUserInfo = LogtoUserInfo(
                sub = "test_logto_id",
                name = "Test User",
                email = "<EMAIL>",
                emailVerified = true
            ),
            isAuthenticated = true,
            lastLoginTime = System.currentTimeMillis(),
            themeMode = "dark",
            language = "zh",
            notificationEnabled = true,
            videoQuality = "high",
            autoPlay = false
        )

        // 保存用户信息
        userPreferences.saveCompleteUserInfo(testUserInfo)

        // 获取用户信息
        val retrievedUserInfo = userPreferences.getCompleteUserInfoSync()

        // 验证
        assertNotNull(retrievedUserInfo)
        assertEquals(testUserInfo.logtoId, retrievedUserInfo.logtoId)
        assertEquals(testUserInfo.email, retrievedUserInfo.email)
        assertEquals(testUserInfo.accessToken, retrievedUserInfo.accessToken)
        assertEquals(testUserInfo.refreshToken, retrievedUserInfo.refreshToken)
        assertEquals(testUserInfo.themeMode, retrievedUserInfo.themeMode)
        assertEquals(testUserInfo.language, retrievedUserInfo.language)
        assertTrue(retrievedUserInfo.isAuthenticated)
    }

    @Test
    fun `test token expiration check`() = runTest {
        val currentTime = System.currentTimeMillis() / 1000

        // 测试过期的token
        val expiredUserInfo = CompleteUserInfo(
            accessToken = "expired_token",
            tokenIssuedAt = currentTime - 7200, // 2小时前
            expiresIn = 3600 // 1小时有效期
        )
        assertTrue(expiredUserInfo.isAccessTokenExpired())
        assertTrue(expiredUserInfo.isAccessTokenExpiringSoon())

        // 测试即将过期的token
        val expiringSoonUserInfo = CompleteUserInfo(
            accessToken = "expiring_soon_token",
            tokenIssuedAt = currentTime - 3300, // 55分钟前
            expiresIn = 3600 // 1小时有效期
        )
        assertFalse(expiringSoonUserInfo.isAccessTokenExpired())
        assertTrue(expiringSoonUserInfo.isAccessTokenExpiringSoon())

        // 测试有效的token
        val validUserInfo = CompleteUserInfo(
            accessToken = "valid_token",
            tokenIssuedAt = currentTime - 1800, // 30分钟前
            expiresIn = 3600 // 1小时有效期
        )
        assertFalse(validUserInfo.isAccessTokenExpired())
        assertFalse(validUserInfo.isAccessTokenExpiringSoon())
    }

    @Test
    fun `test token update`() = runTest {
        // 创建初始用户信息
        val initialUserInfo = CompleteUserInfo(
            logtoId = "test_logto_id",
            email = "<EMAIL>",
            accessToken = "old_access_token",
            refreshToken = "old_refresh_token",
            isAuthenticated = true
        )

        userPreferences.saveCompleteUserInfo(initialUserInfo)

        // 更新token
        userPreferences.updateTokenInfo(
            accessToken = "new_access_token",
            refreshToken = "new_refresh_token",
            expiresIn = 7200,
            idToken = "new_id_token"
        )

        // 验证更新
        val updatedUserInfo = userPreferences.getCompleteUserInfoSync()
        assertNotNull(updatedUserInfo)
        assertEquals("new_access_token", updatedUserInfo.accessToken)
        assertEquals("new_refresh_token", updatedUserInfo.refreshToken)
        assertEquals("new_id_token", updatedUserInfo.idToken)
        assertEquals(7200L, updatedUserInfo.expiresIn)
        assertTrue(updatedUserInfo.isAuthenticated)
        
        // 验证其他信息保持不变
        assertEquals(initialUserInfo.logtoId, updatedUserInfo.logtoId)
        assertEquals(initialUserInfo.email, updatedUserInfo.email)
    }

    @Test
    fun `test clear auth info only`() = runTest {
        // 创建包含用户偏好的用户信息
        val userInfoWithPreferences = CompleteUserInfo(
            logtoId = "test_logto_id",
            email = "<EMAIL>",
            accessToken = "access_token",
            refreshToken = "refresh_token",
            isAuthenticated = true,
            themeMode = "dark",
            language = "en",
            notificationEnabled = false,
            videoQuality = "high"
        )

        userPreferences.saveCompleteUserInfo(userInfoWithPreferences)

        // 清除认证信息但保留偏好设置
        userPreferences.clearAuthInfoOnly()

        // 验证
        val clearedUserInfo = userPreferences.getCompleteUserInfoSync()
        assertNotNull(clearedUserInfo)
        
        // 认证信息应该被清除
        assertEquals(null, clearedUserInfo.accessToken)
        assertEquals(null, clearedUserInfo.refreshToken)
        assertFalse(clearedUserInfo.isAuthenticated)
        
        // 用户偏好应该保留
        assertEquals("dark", clearedUserInfo.themeMode)
        assertEquals("en", clearedUserInfo.language)
        assertFalse(clearedUserInfo.notificationEnabled)
        assertEquals("high", clearedUserInfo.videoQuality)
    }

    @Test
    fun `test has valid tokens`() = runTest {
        val currentTime = System.currentTimeMillis() / 1000

        // 测试有有效refresh token的用户
        val userWithValidRefreshToken = CompleteUserInfo(
            refreshToken = "valid_refresh_token"
        )
        assertTrue(userWithValidRefreshToken.hasValidRefreshToken())

        // 测试有有效access token的用户
        val userWithValidAccessToken = CompleteUserInfo(
            accessToken = "valid_access_token",
            tokenIssuedAt = currentTime - 1800, // 30分钟前
            expiresIn = 3600 // 1小时有效期
        )
        assertTrue(userWithValidAccessToken.hasValidAccessToken())

        // 测试没有token的用户
        val userWithoutTokens = CompleteUserInfo()
        assertFalse(userWithoutTokens.hasValidRefreshToken())
        assertFalse(userWithoutTokens.hasValidAccessToken())
    }
}
