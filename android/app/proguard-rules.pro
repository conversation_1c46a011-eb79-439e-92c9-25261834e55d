# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# ExoPlayer ProGuard rules
-keep class com.google.android.exoplayer2.** { *; }
-keep class androidx.media3.** { *; }
-dontwarn com.google.android.exoplayer2.**
-dontwarn androidx.media3.**

# MediaCodec and codec related rules
-keep class android.media.** { *; }
-keep class com.android.media.** { *; }
-dontwarn android.media.**
-dontwarn com.android.media.**

# Keep codec2 related classes
-keep class ** extends com.android.media.** { *; }
-keepclassmembers class ** {
    native <methods>;
}

# Prevent optimization of video related classes
-keep class * extends android.view.Surface
-keep class * extends android.view.SurfaceView
-keep class * extends android.view.TextureView

# HLS and streaming related
-keep class ** implements androidx.media3.exoplayer.hls.** { *; }
-keep class ** implements androidx.media3.datasource.** { *; } 