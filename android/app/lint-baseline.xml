<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.7.2" type="baseline" client="gradle" dependencies="false" name="AGP (8.7.2)" variant="all" version="8.7.2">

    <issue
        id="ObsoleteLintCustomCheck"
        message="Library lint checks out of date;&#xA;these checks **will be skipped**!&#xA;&#xA;Lint found an issue registry (`androidx.navigation.common.lint.NavigationCommonIssueRegistry`)&#xA;which was compiled against an older version of lint&#xA;than this one. This is usually fine, but not in this&#xA;case; some basic verification shows that the lint&#xA;check jar references (for example) the following API&#xA;which is no longer valid in this version of lint:&#xA;org.jetbrains.kotlin.analysis.api.lifetime.KaDefaultLifetimeTokenProvider#Companion&#xA;(Referenced from androidx/navigation/lint/UtilKt.class)&#xA;&#xA;Therefore, this lint check library is **not** included&#xA;in analysis. This affects the following lint checks:&#xA;`EmptyNavDeepLink`,`WrongStartDestinationType`,`MissingSerializableAnnotation`,`MissingKeepAnnotation`&#xA;&#xA;Recompile the checks against the latest version, or if&#xA;this is a check bundled with a third-party library, see&#xA;if there is a more recent version available.&#xA;&#xA;Version of Lint API this lint check is using is 14.&#xA;The Lint API version currently running is 16 (8.7+).">
        <location
            file="build/intermediates/lint-cache/updateLintBaselineDebug/migrated-jars/androidx.navigation.common.lint.NavigationCommonIssueRegistry-5daa12651be0734e..jar"/>
    </issue>

    <issue
        id="ObsoleteLintCustomCheck"
        message="Library lint checks out of date;&#xA;these checks **will be skipped**!&#xA;&#xA;Lint found an issue registry (`androidx.navigation.compose.lint.NavigationComposeIssueRegistry`)&#xA;which was compiled against an older version of lint&#xA;than this one. This is usually fine, but not in this&#xA;case; some basic verification shows that the lint&#xA;check jar references (for example) the following API&#xA;which is no longer valid in this version of lint:&#xA;org.jetbrains.kotlin.analysis.api.lifetime.KaDefaultLifetimeTokenProvider#Companion&#xA;(Referenced from androidx/navigation/lint/UtilKt.class)&#xA;&#xA;Therefore, this lint check library is **not** included&#xA;in analysis. This affects the following lint checks:&#xA;`ComposableDestinationInComposeScope`&#xA;`ComposableNavGraphInComposeScope`&#xA;`UnrememberedGetBackStackEntry`&#xA;`WrongStartDestinationType`&#xA;`MissingKeepAnnotation`&#xA;`MissingSerializableAnnotation`&#xA;&#xA;Recompile the checks against the latest version, or if&#xA;this is a check bundled with a third-party library, see&#xA;if there is a more recent version available.&#xA;&#xA;Version of Lint API this lint check is using is 14.&#xA;The Lint API version currently running is 16 (8.7+).">
        <location
            file="build/intermediates/lint-cache/updateLintBaselineDebug/migrated-jars/androidx.navigation.compose.lint.NavigationComposeIssueRegistry-598b224f0ce2f9f2..jar"/>
    </issue>

    <issue
        id="ObsoleteLintCustomCheck"
        message="Library lint checks out of date;&#xA;these checks **will be skipped**!&#xA;&#xA;Lint found an issue registry (`androidx.navigation.runtime.lint.NavigationRuntimeIssueRegistry`)&#xA;which was compiled against an older version of lint&#xA;than this one. This is usually fine, but not in this&#xA;case; some basic verification shows that the lint&#xA;check jar references (for example) the following API&#xA;which is no longer valid in this version of lint:&#xA;org.jetbrains.kotlin.analysis.api.lifetime.KaDefaultLifetimeTokenProvider#Companion&#xA;(Referenced from androidx/navigation/lint/UtilKt.class)&#xA;&#xA;Therefore, this lint check library is **not** included&#xA;in analysis. This affects the following lint checks:&#xA;`DeepLinkInActivityDestination`,`WrongStartDestinationType`,`WrongNavigateRouteType`,`MissingKeepAnnotation`,`MissingSerializableAnnotation`&#xA;&#xA;Recompile the checks against the latest version, or if&#xA;this is a check bundled with a third-party library, see&#xA;if there is a more recent version available.&#xA;&#xA;Version of Lint API this lint check is using is 14.&#xA;The Lint API version currently running is 16 (8.7+).">
        <location
            file="build/intermediates/lint-cache/updateLintBaselineDebug/migrated-jars/androidx.navigation.runtime.lint.NavigationRuntimeIssueRegistry-a8fea42aedb9bdb3..jar"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="                Log.d(TAG, &quot;发现设备: ${device.name ?: &quot;Unknown&quot;} (${device.address})&quot;)"
        errorLine2="                                    ~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/bluetooth/BluetoothManager.kt"
            line="204"
            column="37"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="                            text = device.name ?: &quot;未知设备&quot;,"
        errorLine2="                                   ~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/bluetooth/BluetoothScreen.kt"
            line="288"
            column="36"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="                        text = device.name ?: &quot;未知设备&quot;,"
        errorLine2="                               ~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/bluetooth/BluetoothScreen.kt"
            line="560"
            column="32"/>
    </issue>

    <issue
        id="MissingLeanbackLauncher"
        message="Expecting an activity to have `android.intent.category.LEANBACK_LAUNCHER` intent filter"
        errorLine1="&lt;manifest xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2=" ~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="2"
            column="2"/>
    </issue>

    <issue
        id="ScopedStorage"
        message="READ_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to query or interact with MediaStore or media files on the shared storage, you should instead use one or more new storage permissions: `READ_MEDIA_IMAGES`, `READ_MEDIA_VIDEO` or `READ_MEDIA_AUDIO`."
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.READ_EXTERNAL_STORAGE&quot; />"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="30"
            column="36"/>
    </issue>

    <issue
        id="ScopedStorage"
        message="WRITE_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to write to shared storage, use the `MediaStore.createWriteRequest` intent."
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.WRITE_EXTERNAL_STORAGE&quot; />"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="31"
            column="36"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="                String.format(&quot;%02d:%02d:%02d&quot;, hours, minutes, seconds)"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/data/model/Video.kt"
            line="86"
            column="17"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="                String.format(&quot;%02d:%02d&quot;, minutes, seconds)"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/data/model/Video.kt"
            line="88"
            column="17"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="            String.format(&quot;%02d:%02d:%02d&quot;, hours, minutes, seconds)"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/data/model/Video.kt"
            line="213"
            column="13"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="            String.format(&quot;%02d:%02d&quot;, minutes, seconds)"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/data/model/Video.kt"
            line="215"
            column="13"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="        String.format(&quot;%02d:%02d:%02d&quot;, hours, minutes, seconds)"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/VideoPlayerView.kt"
            line="323"
            column="9"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="        String.format(&quot;%02d:%02d&quot;, minutes, seconds)"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/VideoPlayerView.kt"
            line="325"
            column="9"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="                hours > 0 -> String.format(&quot;%dh %dm&quot;, hours, minutes)"
        errorLine2="                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/data/model/VideoSegment.kt"
            line="170"
            column="30"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="                minutes > 0 -> String.format(&quot;%dm %ds&quot;, minutes, seconds)"
        errorLine2="                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/data/model/VideoSegment.kt"
            line="171"
            column="32"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="                else -> String.format(&quot;%ds&quot;, seconds)"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/data/model/VideoSegment.kt"
            line="172"
            column="25"/>
    </issue>

    <issue
        id="RedundantLabel"
        message="Redundant label can be removed"
        errorLine1="            android:label=&quot;@string/app_name&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="68"
            column="13"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        message="A newer version of com.android.application than 8.7.2 is available: 8.11.1. (There is also a newer version of 8.7.𝑥 available, if upgrading to 8.11.1 is difficult: 8.7.3)"
        errorLine1="agp = &quot;8.7.2&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        message="A newer version of com.android.application than 8.7.2 is available: 8.11.1. (There is also a newer version of 8.7.𝑥 available, if upgrading to 8.11.1 is difficult: 8.7.3)"
        errorLine1="agp = &quot;8.7.2&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        message="A newer version of com.android.application than 8.7.2 is available: 8.11.1. (There is also a newer version of 8.7.𝑥 available, if upgrading to 8.11.1 is difficult: 8.7.3)"
        errorLine1="agp = &quot;8.7.2&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.android.tools:desugar_jdk_libs than 2.0.4 is available: 2.1.5"
        errorLine1="    coreLibraryDesugaring(&quot;com.android.tools:desugar_jdk_libs:2.0.4&quot;)"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="139"
            column="27"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.core:core-ktx than 1.15.0 is available: 1.16.0"
        errorLine1="coreKtx = &quot;1.15.0&quot;"
        errorLine2="          ~~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="4"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.core:core-ktx than 1.15.0 is available: 1.16.0"
        errorLine1="coreKtx = &quot;1.15.0&quot;"
        errorLine2="          ~~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="4"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.core:core-ktx than 1.15.0 is available: 1.16.0"
        errorLine1="coreKtx = &quot;1.15.0&quot;"
        errorLine2="          ~~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="4"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.8.7 is available: 2.9.1"
        errorLine1="lifecycleRuntimeKtx = &quot;2.8.7&quot;"
        errorLine2="                      ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="8"
            column="23"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.8.7 is available: 2.9.1"
        errorLine1="lifecycleRuntimeKtx = &quot;2.8.7&quot;"
        errorLine2="                      ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="8"
            column="23"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.8.7 is available: 2.9.1"
        errorLine1="lifecycleRuntimeKtx = &quot;2.8.7&quot;"
        errorLine2="                      ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="8"
            column="23"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-process than 2.8.7 is available: 2.9.1"
        errorLine1="lifecycleProcess = &quot;2.8.7&quot;"
        errorLine2="                   ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="9"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-process than 2.8.7 is available: 2.9.1"
        errorLine1="lifecycleProcess = &quot;2.8.7&quot;"
        errorLine2="                   ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="9"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-process than 2.8.7 is available: 2.9.1"
        errorLine1="lifecycleProcess = &quot;2.8.7&quot;"
        errorLine2="                   ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="9"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.activity:activity-compose than 1.9.3 is available: 1.10.1"
        errorLine1="activityCompose = &quot;1.9.3&quot;"
        errorLine2="                  ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="10"
            column="19"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.activity:activity-compose than 1.9.3 is available: 1.10.1"
        errorLine1="activityCompose = &quot;1.9.3&quot;"
        errorLine2="                  ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="10"
            column="19"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.activity:activity-compose than 1.9.3 is available: 1.10.1"
        errorLine1="activityCompose = &quot;1.9.3&quot;"
        errorLine2="                  ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="10"
            column="19"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.compose:compose-bom than 2024.10.00 is available: 2025.06.01"
        errorLine1="composeBom = &quot;2024.10.00&quot;"
        errorLine2="             ~~~~~~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="11"
            column="14"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.compose:compose-bom than 2024.10.00 is available: 2025.06.01"
        errorLine1="composeBom = &quot;2024.10.00&quot;"
        errorLine2="             ~~~~~~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="11"
            column="14"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.compose:compose-bom than 2024.10.00 is available: 2025.06.01"
        errorLine1="composeBom = &quot;2024.10.00&quot;"
        errorLine2="             ~~~~~~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="11"
            column="14"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.navigation:navigation-compose than 2.8.4 is available: 2.9.1"
        errorLine1="composeNavigation = &quot;2.8.4&quot;"
        errorLine2="                    ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="12"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.navigation:navigation-compose than 2.8.4 is available: 2.9.1"
        errorLine1="composeNavigation = &quot;2.8.4&quot;"
        errorLine2="                    ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="12"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.navigation:navigation-compose than 2.8.4 is available: 2.9.1"
        errorLine1="composeNavigation = &quot;2.8.4&quot;"
        errorLine2="                    ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="12"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.room:room-compiler than 2.6.1 is available: 2.7.2"
        errorLine1="room = &quot;2.6.1&quot;"
        errorLine2="       ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="19"
            column="8"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.room:room-compiler than 2.6.1 is available: 2.7.2"
        errorLine1="room = &quot;2.6.1&quot;"
        errorLine2="       ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="19"
            column="8"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.room:room-compiler than 2.6.1 is available: 2.7.2"
        errorLine1="room = &quot;2.6.1&quot;"
        errorLine2="       ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="19"
            column="8"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.room:room-ktx than 2.6.1 is available: 2.7.2"
        errorLine1="room = &quot;2.6.1&quot;"
        errorLine2="       ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="19"
            column="8"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.room:room-ktx than 2.6.1 is available: 2.7.2"
        errorLine1="room = &quot;2.6.1&quot;"
        errorLine2="       ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="19"
            column="8"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.room:room-ktx than 2.6.1 is available: 2.7.2"
        errorLine1="room = &quot;2.6.1&quot;"
        errorLine2="       ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="19"
            column="8"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.room:room-runtime than 2.6.1 is available: 2.7.2"
        errorLine1="room = &quot;2.6.1&quot;"
        errorLine2="       ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="19"
            column="8"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.room:room-runtime than 2.6.1 is available: 2.7.2"
        errorLine1="room = &quot;2.6.1&quot;"
        errorLine2="       ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="19"
            column="8"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.room:room-runtime than 2.6.1 is available: 2.7.2"
        errorLine1="room = &quot;2.6.1&quot;"
        errorLine2="       ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="19"
            column="8"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.datastore:datastore-preferences than 1.1.1 is available: 1.1.7"
        errorLine1="datastore = &quot;1.1.1&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="20"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.datastore:datastore-preferences than 1.1.1 is available: 1.1.7"
        errorLine1="datastore = &quot;1.1.1&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="20"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.datastore:datastore-preferences than 1.1.1 is available: 1.1.7"
        errorLine1="datastore = &quot;1.1.1&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="20"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.media3:media3-exoplayer than 1.5.0 is available: 1.7.1"
        errorLine1="exoplayer = &quot;1.5.0&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="22"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.media3:media3-exoplayer than 1.5.0 is available: 1.7.1"
        errorLine1="exoplayer = &quot;1.5.0&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="22"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.media3:media3-exoplayer than 1.5.0 is available: 1.7.1"
        errorLine1="exoplayer = &quot;1.5.0&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="22"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.media3:media3-exoplayer-hls than 1.5.0 is available: 1.7.1"
        errorLine1="exoplayer = &quot;1.5.0&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="22"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.media3:media3-exoplayer-hls than 1.5.0 is available: 1.7.1"
        errorLine1="exoplayer = &quot;1.5.0&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="22"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.media3:media3-exoplayer-hls than 1.5.0 is available: 1.7.1"
        errorLine1="exoplayer = &quot;1.5.0&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="22"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.media3:media3-ui than 1.5.0 is available: 1.7.1"
        errorLine1="exoplayer = &quot;1.5.0&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="22"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.media3:media3-ui than 1.5.0 is available: 1.7.1"
        errorLine1="exoplayer = &quot;1.5.0&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="22"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.media3:media3-ui than 1.5.0 is available: 1.7.1"
        errorLine1="exoplayer = &quot;1.5.0&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="22"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.compose.material3:material3 than 1.3.1 is available: 1.3.2"
        errorLine1="material3 = &quot;1.3.1&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="25"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.compose.material3:material3 than 1.3.1 is available: 1.3.2"
        errorLine1="material3 = &quot;1.3.1&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="25"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.compose.material3:material3 than 1.3.1 is available: 1.3.2"
        errorLine1="material3 = &quot;1.3.1&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="25"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.work:work-runtime-ktx than 2.10.0 is available: 2.10.2"
        errorLine1="workManager = &quot;2.10.0&quot;"
        errorLine2="              ~~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="26"
            column="15"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.work:work-runtime-ktx than 2.10.0 is available: 2.10.2"
        errorLine1="workManager = &quot;2.10.0&quot;"
        errorLine2="              ~~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="26"
            column="15"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.work:work-runtime-ktx than 2.10.0 is available: 2.10.2"
        errorLine1="workManager = &quot;2.10.0&quot;"
        errorLine2="              ~~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="26"
            column="15"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.camera:camera-camera2 than 1.4.0 is available: 1.4.2"
        errorLine1="camerax = &quot;1.4.0&quot;"
        errorLine2="          ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="27"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.camera:camera-camera2 than 1.4.0 is available: 1.4.2"
        errorLine1="camerax = &quot;1.4.0&quot;"
        errorLine2="          ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="27"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.camera:camera-camera2 than 1.4.0 is available: 1.4.2"
        errorLine1="camerax = &quot;1.4.0&quot;"
        errorLine2="          ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="27"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.camera:camera-core than 1.4.0 is available: 1.4.2"
        errorLine1="camerax = &quot;1.4.0&quot;"
        errorLine2="          ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="27"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.camera:camera-core than 1.4.0 is available: 1.4.2"
        errorLine1="camerax = &quot;1.4.0&quot;"
        errorLine2="          ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="27"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.camera:camera-core than 1.4.0 is available: 1.4.2"
        errorLine1="camerax = &quot;1.4.0&quot;"
        errorLine2="          ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="27"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.camera:camera-lifecycle than 1.4.0 is available: 1.4.2"
        errorLine1="camerax = &quot;1.4.0&quot;"
        errorLine2="          ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="27"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.camera:camera-lifecycle than 1.4.0 is available: 1.4.2"
        errorLine1="camerax = &quot;1.4.0&quot;"
        errorLine2="          ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="27"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.camera:camera-lifecycle than 1.4.0 is available: 1.4.2"
        errorLine1="camerax = &quot;1.4.0&quot;"
        errorLine2="          ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="27"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.camera:camera-view than 1.4.0 is available: 1.4.2"
        errorLine1="camerax = &quot;1.4.0&quot;"
        errorLine2="          ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="27"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.camera:camera-view than 1.4.0 is available: 1.4.2"
        errorLine1="camerax = &quot;1.4.0&quot;"
        errorLine2="          ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="27"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.camera:camera-view than 1.4.0 is available: 1.4.2"
        errorLine1="camerax = &quot;1.4.0&quot;"
        errorLine2="          ~~~~~~~">
        <location
            file="$HOME/github/CabyCare/android/gradle/libs.versions.toml"
            line="27"
            column="11"/>
    </issue>

    <issue
        id="LockedOrientationActivity"
        message="Expecting `android:screenOrientation=&quot;unspecified&quot;` or `&quot;fullSensor&quot;` for this activity so the user can use the application in any orientation and provide a great experience on Chrome OS devices"
        errorLine1="            android:screenOrientation=&quot;portrait&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="70"
            column="13"/>
    </issue>

    <issue
        id="SourceLockedOrientationActivity"
        message="You should not lock orientation of your activities, so that you can support a good user experience for any device or orientation"
        errorLine1="        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/VideoPlayerActivity.kt"
            line="102"
            column="9"/>
    </issue>

    <issue
        id="SourceLockedOrientationActivity"
        message="You should not lock orientation of your activities, so that you can support a good user experience for any device or orientation"
        errorLine1="        activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/VideoPlayerView.kt"
            line="340"
            column="9"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="            setUserAgent(&quot;CabyCare-Android/1.0&quot;)"
        errorLine2="            ~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="154"
            column="13"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="                setDefaultRequestProperties(mapOf("
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="158"
            column="17"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="            setConnectTimeoutMs(15000) // 15秒连接超时（增加超时时间）"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="167"
            column="13"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="            setReadTimeoutMs(30000)    // 30秒读取超时（增加读取超时）"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="168"
            column="13"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="            setAllowCrossProtocolRedirects(true)"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="171"
            column="13"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="            setKeepPostFor302Redirects(true)"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="174"
            column="13"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="            val renderersFactory = DefaultRenderersFactory(attributionContext).apply {"
        errorLine2="                ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="185"
            column="17"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="            val renderersFactory = DefaultRenderersFactory(attributionContext).apply {"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="185"
            column="36"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="                setExtensionRendererMode(DefaultRenderersFactory.EXTENSION_RENDERER_MODE_PREFER)"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="187"
            column="17"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="                setExtensionRendererMode(DefaultRenderersFactory.EXTENSION_RENDERER_MODE_PREFER)"
        errorLine2="                                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="187"
            column="66"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="                setEnableDecoderFallback(true)"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="189"
            column="17"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="                setEnableAudioFloatOutput(false)"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="191"
            column="17"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="            val mediaSourceFactory = DefaultMediaSourceFactory(httpDataSourceFactory)"
        errorLine2="                                     ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="195"
            column="38"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="            val loadControl = DefaultLoadControl.Builder()"
        errorLine2="                ~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="198"
            column="17"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="            val loadControl = DefaultLoadControl.Builder()"
        errorLine2="                                                 ~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="198"
            column="50"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="                .setAllocator(DefaultAllocator(true, 64))  // 使用64KB块大小的分配器（更大的缓冲块）"
        errorLine2="                 ~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="199"
            column="18"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="                .setAllocator(DefaultAllocator(true, 64))  // 使用64KB块大小的分配器（更大的缓冲块）"
        errorLine2="                              ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="199"
            column="31"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="                .setBufferDurationsMs("
        errorLine2="                 ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="200"
            column="18"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="                .setTargetBufferBytes(50 * 1024 * 1024)  // 设置50MB目标缓冲字节数"
        errorLine2="                 ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="206"
            column="18"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="                .setPrioritizeTimeOverSizeThresholds(true)  // 优先考虑时间而不是大小"
        errorLine2="                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="207"
            column="18"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="                .setBackBuffer(30000, true)  // 保留30秒的后台缓冲"
        errorLine2="                 ~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="208"
            column="18"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="                .build()"
        errorLine2="                 ~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="209"
            column="18"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="                .setRenderersFactory(renderersFactory)  // 使用自定义渲染器工厂"
        errorLine2="                 ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="213"
            column="18"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="                .setLoadControl(loadControl)  // 应用自定义缓冲控制"
        errorLine2="                 ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="215"
            column="18"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="                val hlsMediaSource = HlsMediaSource.Factory(httpDataSourceFactory)"
        errorLine2="                    ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="295"
            column="21"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="                val hlsMediaSource = HlsMediaSource.Factory(httpDataSourceFactory)"
        errorLine2="                                                    ~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="295"
            column="53"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="                    .setAllowChunklessPreparation(true)  // 允许无chunk准备，更快开始播放"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="296"
            column="22"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="                    .setLoadErrorHandlingPolicy(DefaultLoadErrorHandlingPolicy(3)) // 设置加载错误处理策略"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="297"
            column="22"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="                    .setLoadErrorHandlingPolicy(DefaultLoadErrorHandlingPolicy(3)) // 设置加载错误处理策略"
        errorLine2="                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="297"
            column="49"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="                    .createMediaSource(mediaItem)"
        errorLine2="                     ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="298"
            column="22"/>
    </issue>

    <issue
        id="UnsafeOptInUsageError"
        message="This declaration is opt-in and its usage should be marked with `@androidx.media3.common.util.UnstableApi` or `@OptIn(markerClass = androidx.media3.common.util.UnstableApi.class)`"
        errorLine1="                exoPlayer.setMediaSource(hlsMediaSource)"
        errorLine2="                          ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="301"
            column="27"/>
    </issue>

    <issue
        id="ModifierParameter"
        message="Modifier parameter should be the first optional parameter"
        errorLine1="    modifier: Modifier = Modifier"
        errorLine2="    ~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/HLSVideoPlayer.kt"
            line="429"
            column="5"/>
    </issue>

    <issue
        id="ModifierParameter"
        message="Modifier parameter should be the first optional parameter"
        errorLine1="    modifier: Modifier = Modifier"
        errorLine2="    ~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/video/VideoPlayerView.kt"
            line="39"
            column="5"/>
    </issue>

    <issue
        id="PermissionImpliesUnsupportedHardware"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.bluetooth&quot; required=&quot;false&quot;>` tag"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.BLUETOOTH&quot; />"
        errorLine2="     ~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="13"
            column="6"/>
    </issue>

    <issue
        id="PermissionImpliesUnsupportedHardware"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.bluetooth&quot; required=&quot;false&quot;>` tag"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.BLUETOOTH_ADMIN&quot; />"
        errorLine2="     ~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="14"
            column="6"/>
    </issue>

    <issue
        id="PermissionImpliesUnsupportedHardware"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.location.gps&quot; required=&quot;false&quot;>` tag"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.ACCESS_FINE_LOCATION&quot; />"
        errorLine2="     ~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="17"
            column="6"/>
    </issue>

    <issue
        id="DiscouragedApi"
        message="Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed."
        errorLine1="            android:screenOrientation=&quot;portrait&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="70"
            column="13"/>
    </issue>

    <issue
        id="DiscouragedApi"
        message="Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed."
        errorLine1="            android:screenOrientation=&quot;landscape&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="91"
            column="13"/>
    </issue>

    <issue
        id="AutoboxingStateCreation"
        message="Prefer `mutableFloatStateOf` instead of `mutableStateOf`"
        errorLine1="    var motionSensitivity by remember { mutableStateOf(0.5f) }"
        errorLine2="                                        ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/cabycare/android/ui/device/DeviceSettingsDialog.kt"
            line="35"
            column="41"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.primary_dark` appears to be unused"
        errorLine1="    &lt;color name=&quot;primary_dark&quot;>#0A84FF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="5"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.primary_container` appears to be unused"
        errorLine1="    &lt;color name=&quot;primary_container&quot;>#E3F2FD&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="6"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.on_primary_container` appears to be unused"
        errorLine1="    &lt;color name=&quot;on_primary_container&quot;>#001D36&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="7"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.secondary_light` appears to be unused"
        errorLine1="    &lt;color name=&quot;secondary_light&quot;>#34C759&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="10"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.secondary_dark` appears to be unused"
        errorLine1="    &lt;color name=&quot;secondary_dark&quot;>#30D158&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="11"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.secondary_container` appears to be unused"
        errorLine1="    &lt;color name=&quot;secondary_container&quot;>#E8F5E8&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="12"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.on_secondary_container` appears to be unused"
        errorLine1="    &lt;color name=&quot;on_secondary_container&quot;>#002106&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="13"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.error_light` appears to be unused"
        errorLine1="    &lt;color name=&quot;error_light&quot;>#FF3B30&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="16"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.error_dark` appears to be unused"
        errorLine1="    &lt;color name=&quot;error_dark&quot;>#FF453A&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="17"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.error_container` appears to be unused"
        errorLine1="    &lt;color name=&quot;error_container&quot;>#FFDAD6&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="18"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.on_error_container` appears to be unused"
        errorLine1="    &lt;color name=&quot;on_error_container&quot;>#410002&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="19"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.warning_light` appears to be unused"
        errorLine1="    &lt;color name=&quot;warning_light&quot;>#FF9500&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="22"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.warning_dark` appears to be unused"
        errorLine1="    &lt;color name=&quot;warning_dark&quot;>#FF9F0A&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="23"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.background_light` appears to be unused"
        errorLine1="    &lt;color name=&quot;background_light&quot;>#FFFBFE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="26"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.background_dark` appears to be unused"
        errorLine1="    &lt;color name=&quot;background_dark&quot;>#1C1B1F&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="27"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.surface_light` appears to be unused"
        errorLine1="    &lt;color name=&quot;surface_light&quot;>#FFFBFE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="28"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.surface_dark` appears to be unused"
        errorLine1="    &lt;color name=&quot;surface_dark&quot;>#1C1B1F&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="29"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.surface_variant_light` appears to be unused"
        errorLine1="    &lt;color name=&quot;surface_variant_light&quot;>#F2F2F7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="32"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.surface_variant_dark` appears to be unused"
        errorLine1="    &lt;color name=&quot;surface_variant_dark&quot;>#1C1C1E&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="33"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.on_surface_variant_light` appears to be unused"
        errorLine1="    &lt;color name=&quot;on_surface_variant_light&quot;>#3C3C43&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="34"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.on_surface_variant_dark` appears to be unused"
        errorLine1="    &lt;color name=&quot;on_surface_variant_dark&quot;>#EBEBF5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="35"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.outline_light` appears to be unused"
        errorLine1="    &lt;color name=&quot;outline_light&quot;>#C6C6C8&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="38"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.outline_dark` appears to be unused"
        errorLine1="    &lt;color name=&quot;outline_dark&quot;>#38383A&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="39"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.white` appears to be unused"
        errorLine1="    &lt;color name=&quot;white&quot;>#FFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="42"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.healthy_green` appears to be unused"
        errorLine1="    &lt;color name=&quot;healthy_green&quot;>#34C759&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="46"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.warning_orange` appears to be unused"
        errorLine1="    &lt;color name=&quot;warning_orange&quot;>#FF9500&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="47"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.critical_red` appears to be unused"
        errorLine1="    &lt;color name=&quot;critical_red&quot;>#FF3B30&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="48"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.info_blue` appears to be unused"
        errorLine1="    &lt;color name=&quot;info_blue&quot;>#007AFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="49"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.video_controls_background` appears to be unused"
        errorLine1="    &lt;color name=&quot;video_controls_background&quot;>#80000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="52"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.video_progress_background` appears to be unused"
        errorLine1="    &lt;color name=&quot;video_progress_background&quot;>#40FFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="53"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.video_progress_foreground` appears to be unused"
        errorLine1="    &lt;color name=&quot;video_progress_foreground&quot;>#FFFFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="54"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.card_background_light` appears to be unused"
        errorLine1="    &lt;color name=&quot;card_background_light&quot;>#FFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="57"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.card_background_dark` appears to be unused"
        errorLine1="    &lt;color name=&quot;card_background_dark&quot;>#2C2C2E&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="58"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.card_border_light` appears to be unused"
        errorLine1="    &lt;color name=&quot;card_border_light&quot;>#E5E5EA&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="59"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.card_border_dark` appears to be unused"
        errorLine1="    &lt;color name=&quot;card_border_dark&quot;>#38383A&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="60"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.login_background` appears to be unused"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="src/main/res/drawable/login_background.xml"
            line="3"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.attribution_video_streaming` appears to be unused"
        errorLine1="    &lt;string name=&quot;attribution_video_streaming&quot;>Video Streaming&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="6"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.attribution_network_access` appears to be unused"
        errorLine1="    &lt;string name=&quot;attribution_network_access&quot;>Network Access&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="7"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.attribution_media_playback` appears to be unused"
        errorLine1="    &lt;string name=&quot;attribution_media_playback&quot;>Media Playback&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="8"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.attribution_device_monitoring` appears to be unused"
        errorLine1="    &lt;string name=&quot;attribution_device_monitoring&quot;>Device Monitoring&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="9"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.ok` appears to be unused"
        errorLine1="    &lt;string name=&quot;ok&quot;>确定&lt;/string>"
        errorLine2="            ~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="12"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.cancel` appears to be unused"
        errorLine1="    &lt;string name=&quot;cancel&quot;>取消&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="13"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.save` appears to be unused"
        errorLine1="    &lt;string name=&quot;save&quot;>保存&lt;/string>"
        errorLine2="            ~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="14"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.delete` appears to be unused"
        errorLine1="    &lt;string name=&quot;delete&quot;>删除&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="15"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.edit` appears to be unused"
        errorLine1="    &lt;string name=&quot;edit&quot;>编辑&lt;/string>"
        errorLine2="            ~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="16"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.add` appears to be unused"
        errorLine1="    &lt;string name=&quot;add&quot;>添加&lt;/string>"
        errorLine2="            ~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="17"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.retry` appears to be unused"
        errorLine1="    &lt;string name=&quot;retry&quot;>重试&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="18"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.loading` appears to be unused"
        errorLine1="    &lt;string name=&quot;loading&quot;>加载中...&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="19"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.error` appears to be unused"
        errorLine1="    &lt;string name=&quot;error&quot;>错误&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="20"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.success` appears to be unused"
        errorLine1="    &lt;string name=&quot;success&quot;>成功&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="21"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.nav_home` appears to be unused"
        errorLine1="    &lt;string name=&quot;nav_home&quot;>首页&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="24"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.nav_care` appears to be unused"
        errorLine1="    &lt;string name=&quot;nav_care&quot;>关爱&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="25"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.nav_animals` appears to be unused"
        errorLine1="    &lt;string name=&quot;nav_animals&quot;>宠物&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="26"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.nav_device` appears to be unused"
        errorLine1="    &lt;string name=&quot;nav_device&quot;>设备&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="27"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.home_title` appears to be unused"
        errorLine1="    &lt;string name=&quot;home_title&quot;>CabyCare&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="30"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.home_welcome` appears to be unused"
        errorLine1="    &lt;string name=&quot;home_welcome&quot;>欢迎使用CabyCare&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="31"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.home_add_first_pet` appears to be unused"
        errorLine1="    &lt;string name=&quot;home_add_first_pet&quot;>添加您的第一只宠物开始使用&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="32"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.home_needs_attention` appears to be unused"
        errorLine1="    &lt;string name=&quot;home_needs_attention&quot;>需要关注&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="33"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.home_today_activity` appears to be unused"
        errorLine1="    &lt;string name=&quot;home_today_activity&quot;>今日活动&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="34"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.home_health_score` appears to be unused"
        errorLine1="    &lt;string name=&quot;home_health_score&quot;>健康评分&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="35"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.home_activity_level` appears to be unused"
        errorLine1="    &lt;string name=&quot;home_activity_level&quot;>活跃度&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="36"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.care_title` appears to be unused"
        errorLine1="    &lt;string name=&quot;care_title&quot;>实时关爱&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="39"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.care_live_streaming` appears to be unused"
        errorLine1="    &lt;string name=&quot;care_live_streaming&quot;>直播中&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="40"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.care_no_video` appears to be unused"
        errorLine1="    &lt;string name=&quot;care_no_video&quot;>暂无视频流&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="41"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.care_recent_activity` appears to be unused"
        errorLine1="    &lt;string name=&quot;care_recent_activity&quot;>最近活动&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="42"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.care_quick_actions` appears to be unused"
        errorLine1="    &lt;string name=&quot;care_quick_actions&quot;>快速操作&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="43"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.care_take_snapshot` appears to be unused"
        errorLine1="    &lt;string name=&quot;care_take_snapshot&quot;>截图&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="44"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.care_toggle_recording` appears to be unused"
        errorLine1="    &lt;string name=&quot;care_toggle_recording&quot;>录制&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="45"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.care_send_notification` appears to be unused"
        errorLine1="    &lt;string name=&quot;care_send_notification&quot;>通知&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="46"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.animals_title` appears to be unused"
        errorLine1="    &lt;string name=&quot;animals_title&quot;>我的宠物&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="49"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.animals_add_pet` appears to be unused"
        errorLine1="    &lt;string name=&quot;animals_add_pet&quot;>添加宠物&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="50"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.animals_no_pets` appears to be unused"
        errorLine1="    &lt;string name=&quot;animals_no_pets&quot;>还没有宠物&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="51"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.animals_add_first_pet` appears to be unused"
        errorLine1="    &lt;string name=&quot;animals_add_first_pet&quot;>添加您的第一只宠物开始使用CabyCare&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="52"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.animals_edit_pet` appears to be unused"
        errorLine1="    &lt;string name=&quot;animals_edit_pet&quot;>编辑猫咪信息&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="53"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.animals_add_new_pet` appears to be unused"
        errorLine1="    &lt;string name=&quot;animals_add_new_pet&quot;>添加新猫咪&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="54"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.device_title` appears to be unused"
        errorLine1="    &lt;string name=&quot;device_title&quot;>设备管理&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="57"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.device_overview` appears to be unused"
        errorLine1="    &lt;string name=&quot;device_overview&quot;>设备概览&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="58"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.device_total` appears to be unused"
        errorLine1="    &lt;string name=&quot;device_total&quot;>总设备&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="59"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.device_online` appears to be unused"
        errorLine1="    &lt;string name=&quot;device_online&quot;>在线&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="60"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.device_offline` appears to be unused"
        errorLine1="    &lt;string name=&quot;device_offline&quot;>离线&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="61"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.device_add_new` appears to be unused"
        errorLine1="    &lt;string name=&quot;device_add_new&quot;>添加新设备&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="62"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.device_no_devices` appears to be unused"
        errorLine1="    &lt;string name=&quot;device_no_devices&quot;>还没有设备&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="63"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.device_add_first` appears to be unused"
        errorLine1="    &lt;string name=&quot;device_add_first&quot;>添加您的第一个CabyCare设备&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="64"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.device_settings` appears to be unused"
        errorLine1="    &lt;string name=&quot;device_settings&quot;>设备设置&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="65"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.device_ota_update` appears to be unused"
        errorLine1="    &lt;string name=&quot;device_ota_update&quot;>OTA升级&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="66"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.login_welcome` appears to be unused"
        errorLine1="    &lt;string name=&quot;login_welcome&quot;>欢迎使用CabyCare&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="69"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.login_subtitle` appears to be unused"
        errorLine1="    &lt;string name=&quot;login_subtitle&quot;>请登录您的账户以继续使用&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="70"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.login_button` appears to be unused"
        errorLine1="    &lt;string name=&quot;login_button&quot;>登录&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="71"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.login_first_time` appears to be unused"
        errorLine1="    &lt;string name=&quot;login_first_time&quot;>首次使用？系统将自动为您创建账户&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="72"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.login_terms` appears to be unused"
        errorLine1="    &lt;string name=&quot;login_terms&quot;>登录即表示您同意我们的服务条款和隐私政策&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="73"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.video_play` appears to be unused"
        errorLine1="    &lt;string name=&quot;video_play&quot;>播放&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="76"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.video_pause` appears to be unused"
        errorLine1="    &lt;string name=&quot;video_pause&quot;>暂停&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="77"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.video_fullscreen` appears to be unused"
        errorLine1="    &lt;string name=&quot;video_fullscreen&quot;>全屏&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="78"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.video_exit_fullscreen` appears to be unused"
        errorLine1="    &lt;string name=&quot;video_exit_fullscreen&quot;>退出全屏&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="79"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.video_live` appears to be unused"
        errorLine1="    &lt;string name=&quot;video_live&quot;>直播&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="80"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.error_network` appears to be unused"
        errorLine1="    &lt;string name=&quot;error_network&quot;>网络连接失败&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="83"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.error_load_failed` appears to be unused"
        errorLine1="    &lt;string name=&quot;error_load_failed&quot;>加载失败&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="84"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.error_save_failed` appears to be unused"
        errorLine1="    &lt;string name=&quot;error_save_failed&quot;>保存失败&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="85"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.error_delete_failed` appears to be unused"
        errorLine1="    &lt;string name=&quot;error_delete_failed&quot;>删除失败&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="86"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.error_unknown` appears to be unused"
        errorLine1="    &lt;string name=&quot;error_unknown&quot;>未知错误&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="87"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.health_healthy` appears to be unused"
        errorLine1="    &lt;string name=&quot;health_healthy&quot;>健康&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="90"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.health_needs_attention` appears to be unused"
        errorLine1="    &lt;string name=&quot;health_needs_attention&quot;>需要关注&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="91"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.health_sick` appears to be unused"
        errorLine1="    &lt;string name=&quot;health_sick&quot;>生病&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="92"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.cat_name` appears to be unused"
        errorLine1="    &lt;string name=&quot;cat_name&quot;>姓名&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="95"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.cat_age` appears to be unused"
        errorLine1="    &lt;string name=&quot;cat_age&quot;>年龄&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="96"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.cat_weight` appears to be unused"
        errorLine1="    &lt;string name=&quot;cat_weight&quot;>体重&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="97"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.cat_gender` appears to be unused"
        errorLine1="    &lt;string name=&quot;cat_gender&quot;>性别&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="98"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.cat_type` appears to be unused"
        errorLine1="    &lt;string name=&quot;cat_type&quot;>类型&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="99"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.cat_health_status` appears to be unused"
        errorLine1="    &lt;string name=&quot;cat_health_status&quot;>健康状态&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="100"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.cat_activity_level` appears to be unused"
        errorLine1="    &lt;string name=&quot;cat_activity_level&quot;>活跃度&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="101"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.cat_male` appears to be unused"
        errorLine1="    &lt;string name=&quot;cat_male&quot;>公猫&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="102"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.cat_female` appears to be unused"
        errorLine1="    &lt;string name=&quot;cat_female&quot;>母猫&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="103"
            column="13"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;loading&quot;>加载中...&lt;/string>"
        errorLine2="                           ~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="19"
            column="28"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/app_icon.png` in densityless folder">
        <location
            file="src/main/res/drawable/app_icon.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/login_coconut.jpg` in densityless folder">
        <location
            file="src/main/res/drawable/login_coconut.jpg"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/login_ebony.jpg` in densityless folder">
        <location
            file="src/main/res/drawable/login_ebony.jpg"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    coreLibraryDesugaring(&quot;com.android.tools:desugar_jdk_libs:2.0.4&quot;)"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="139"
            column="27"/>
    </issue>

</issues>
