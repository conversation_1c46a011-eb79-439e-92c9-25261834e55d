# CabyCare Android

CabyCare Android版本 - 智能宠物关爱应用

## 项目概述

CabyCare是一款智能宠物关爱应用，帮助宠物主人更好地照顾他们的猫咪。本项目是基于Swift版本CabyCare的一比一Android实现。

## 主要功能

### 🏠 首页
- 宠物健康状态概览
- 今日活动统计
- 重要提醒和警报
- 健康评分展示

### ❤️ 实时关爱
- 实时视频监控
- HLS流媒体播放
- 设备状态监控
- 快速操作（截图、录制、通知）

### 🐱 宠物管理
- 宠物档案管理
- 健康状态跟踪
- 疫苗接种记录
- 用药记录管理

### 📱 设备管理
- 设备注册和配对
- 设备状态监控
- OTA固件升级
- 设备设置配置

## 技术架构

### 开发框架
- **UI框架**: Jetpack Compose
- **架构模式**: MVVM + Repository
- **依赖注入**: Hilt
- **网络请求**: Retrofit + OkHttp
- **数据序列化**: Kotlinx Serialization
- **本地存储**: DataStore + Room
- **视频播放**: ExoPlayer
- **图片加载**: Coil
- **异步处理**: Kotlin Coroutines + Flow

### 项目结构
```
app/src/main/java/com/cabycare/android/
├── data/                    # 数据层
│   ├── auth/               # 认证管理
│   ├── local/              # 本地存储
│   ├── model/              # 数据模型
│   ├── network/            # 网络层
│   └── repository/         # 数据仓库
├── di/                     # 依赖注入
├── service/                # 后台服务
└── ui/                     # UI层
    ├── animals/            # 宠物管理
    ├── auth/               # 认证界面
    ├── care/               # 关爱页面
    ├── device/             # 设备管理
    ├── home/               # 首页
    ├── main/               # 主界面
    ├── splash/             # 启动页
    ├── theme/              # 主题样式
    └── video/              # 视频播放
```

## 核心特性

### 🔐 用户认证
- OAuth2.0 + PKCE认证流程
- 自动令牌刷新
- 安全的令牌存储

### 📹 视频功能
- HLS流媒体播放
- 全屏播放支持
- 播放控制（播放/暂停/进度）
- 历史视频回放

### 🔔 通知系统
- 推送通知
- 本地通知
- 通知权限管理

### 💾 数据管理
- 离线数据缓存
- 数据同步
- 偏好设置存储

## 开发环境

### 要求
- Android Studio Hedgehog | 2023.1.1+
- Kotlin 1.9.0+
- Android Gradle Plugin 8.1.0+
- 最低SDK版本: 24 (Android 7.0)
- 目标SDK版本: 34 (Android 14)

### 构建和运行

#### 方式一：使用构建脚本（推荐）
```bash
# 进入android目录
cd android

# 运行构建脚本
./build.sh
```

#### 方式二：手动构建
1. 确保已安装Java 17
```bash
# macOS使用Homebrew安装
brew install openjdk@17

# 设置环境变量
export JAVA_HOME=/opt/homebrew/opt/openjdk@17/libexec/openjdk.jdk/Contents/Home
export PATH="/opt/homebrew/opt/openjdk@17/bin:$PATH"
```

2. 构建项目
```bash
./gradlew assembleDebug
```

#### 方式三：使用Android Studio
1. 使用Android Studio打开项目
2. 同步Gradle依赖
3. 连接Android设备或启动模拟器
4. 点击运行按钮

### 配置
在运行项目前，请确保配置以下内容：
- API基础URL（在`NetworkManager.kt`中）
- OAuth客户端ID（在`AuthManager.kt`中）
- 推送通知配置

## API集成

### 主要API端点
- `/api/user/profile` - 用户信息
- `/api/cats` - 猫咪管理
- `/api/devices` - 设备管理
- `/api/family-groups` - 家庭组
- `/api/notifications` - 通知
- `/api/devices/{id}/video/stream` - 视频流

### 认证
使用Bearer Token进行API认证，令牌自动添加到请求头中。

## 测试

### 单元测试
```bash
./gradlew test
```

### UI测试
```bash
./gradlew connectedAndroidTest
```

## 构建状态

✅ **项目构建成功！**

- 所有Kotlin代码编译通过
- Hilt依赖注入配置正确
- APK文件成功生成
- 项目结构完整

### 构建输出
- Debug APK: `app/build/outputs/apk/debug/app-debug.apk`
- APK大小: 约 15-20MB

## 构建发布版本

```bash
./gradlew assembleRelease
```

## 许可证

本项目仅供学习和参考使用。

## 联系方式

如有问题或建议，请联系开发团队。
