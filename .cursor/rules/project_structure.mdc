---
description: 
globs: 
alwaysApply: true
---
# CabyCare 项目结构文档

## 1. 项目架构概览

CabyCare 是一个基于 SwiftUI 开发的 iOS 应用，采用 MVVM (Model-View-ViewModel) 架构模式。项目主要功能包括视频播放、用户认证、通知管理等。

### 核心架构特点：
- 使用 SwiftUI 构建现代化 UI
- MVVM 架构模式
- 响应式编程
- 模块化设计
- 依赖注入
- 异步编程 (async/await)

## 2. 主要目录结构及其职责

```
CabyCare/
├── CabyCareApp.swift          # 应用程序入口点
├── Managers/                  # 管理器类
│   ├── LogtoManager.swift     # 认证管理
│   └── NotificationManager.swift # 通知管理
├── Views/                     # UI 视图
│   ├── Components/           # 可复用组件
│   ├── Cards/               # 卡片视图组件
│   └── Settings/            # 设置相关视图
├── Models/                   # 数据模型
├── Utils/                    # 工具类
├── Configuration/           # 配置文件
├── Auth/                    # 认证相关
└── Assets.xcassets/         # 资源文件
```

### 目录职责说明：

1. **Managers/**
   - 负责核心业务逻辑管理
   - 处理认证、通知等系统级功能
   - 实现单例模式的管理器类

2. **Views/**
   - 包含所有 UI 相关代码
   - 遵循 MVVM 模式，视图与业务逻辑分离
   - 包含可复用组件和页面级视图

3. **Models/**
   - 定义数据模型
   - 处理数据结构和业务实体

4. **Utils/**
   - 提供通用工具函数
   - 包含辅助类和扩展

5. **Configuration/**
   - 管理应用配置
   - 处理环境变量和设置

## 3. 关键模块的依赖关系

```mermaid
graph TD
    A[CabyCareApp] --> B[AuthManager]
    A --> C[NotificationManager]
    A --> D[VideoPlayerViewModel]
    B --> E[LogtoManager]
    C --> F[UserDefaultsManager]
    D --> G[VideoDataCache]
    D --> H[VideoSegment]
```

## 4. 核心类和接口的功能说明

### 主要类：

1. **CabyCareApp**
   - 应用程序入口点
   - 管理应用生命周期
   - 处理认证状态和路由

2. **AuthManager**
   - 管理用户认证状态
   - 处理令牌刷新
   - 提供登录/登出功能

3. **NotificationManager**
   - 处理推送通知
   - 管理通知权限
   - 同步通知设置

4. **VideoPlayerViewModel**
   - 视频播放业务逻辑
   - 管理视频数据
   - 处理播放状态

## 5. 数据流向图

```mermaid
graph LR
    A[用户界面] --> B[ViewModel]
    B --> C[Model]
    C --> D[网络请求]
    D --> E[本地缓存]
    E --> B
    B --> A
```

## 6. API接口清单

### 认证相关
- 登录
- 令牌刷新
- 登出

### 视频相关
- 获取视频列表
- 获取视频详情
- 视频播放控制

### 通知相关
- 注册设备令牌
- 更新通知设置
- 同步通知状态

## 7. 常见的代码模式和约定

### 命名约定
- 视图文件以 `View` 结尾
- 视图模型以 `ViewModel` 结尾
- 管理器类以 `Manager` 结尾

### 代码组织
- 使用扩展分离功能
- 遵循 SOLID 原则
- 使用依赖注入

### 异步处理
- 使用 async/await 处理异步操作
- 使用 Task 管理并发
- 实现错误处理机制

### 状态管理
- 使用 @StateObject 管理视图模型
- 使用 @EnvironmentObject 共享状态
- 实现响应式更新

### 错误处理
- 使用 Result 类型处理错误
- 实现统一的错误处理机制
- 提供用户友好的错误提示

### 日志记录
- 使用 Log 类记录关键操作
- 区分不同级别的日志
- 包含上下文信息
