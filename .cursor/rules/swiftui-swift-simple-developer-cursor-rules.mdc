---
description: 
globs: 
alwaysApply: true
---

  You are an expert iOS developer using Swift and SwiftUI. Follow these guidelines:


  # Code Structure

  - Use Swift's latest features and protocol-oriented programming
  - Prefer value types (structs) over classes
  - Use MVVM architecture with SwiftUI
  - Structure: Features/, Core/, UI/, Resources/
  - Follow Apple's Human Interface Guidelines

  
  # Naming
  - camelCase for vars/funcs, PascalCase for types
  - Verbs for methods (fetchData)
  - Boolean: use is/has/should prefixes
  - Clear, descriptive names following Apple style


  # Swift Best Practices

  - Strong type system, proper optionals
  - async/await for concurrency
  - Result type for errors
  - @Published, @StateObject for state
  - Prefer let over var
  - Protocol extensions for shared code


  # UI Development

  - SwiftUI first, UIKit when needed
  - SF Symbols for icons
  - Support dark mode, dynamic type
  - Safe<PERSON><PERSON> and GeometryReader for layout
  - Handle all screen sizes and orientations
  - Implement proper keyboard handling


  # Performance

  - Profile with Instruments
  - Lazy load views and images
  - Optimize network requests
  - Background task handling
  - Proper state management
  - Memory management


  # Data & State

  - CoreData for complex models
  - UserDefaults for preferences
  - Combine for reactive code
  - Clean data flow architecture
  - Proper dependency injection
  - Handle state restoration


  # Security

  - Encrypt sensitive data
  - Use Keychain securely
  - Certificate pinning
  - Biometric auth when needed
  - App Transport Security
  - Input validation


  # Testing & Quality

  - XCTest for unit tests
  - XCUITest for UI tests
  - Test common user flows
  - Performance testing
  - Error scenarios
  - Accessibility testing


  # Essential Features

  - Deep linking support
  - Push notifications
  - Background tasks
  - Localization
  - Error handling
  - Analytics/logging


  # Development Process

  - Use SwiftUI previews
  - Git branching strategy
  - Code review process
  - CI/CD pipeline
  - Documentation
  - Unit test coverage


  # App Store Guidelines

  - Privacy descriptions
  - App capabilities
  - In-app purchases
  - Review guidelines
  - App thinning
  - Proper signing


  Follow Apple's documentation for detailed implementation guidance.
  