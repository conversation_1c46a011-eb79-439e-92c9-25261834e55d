# CabyCare 维护脚本说明

这个目录包含了用于维护 CabyCare 项目的各种自动化脚本。

## 📜 脚本列表

### 1. `build_testflight.sh` - TestFlight 构建脚本
自动化构建和准备 TestFlight 发布的完整流程。

**使用方法：**
```bash
# 使用默认版本号和构建号
./scripts/build_testflight.sh

# 指定版本号
./scripts/build_testflight.sh 0.4.8

# 指定版本号和构建号
./scripts/build_testflight.sh 0.4.8 12
```

**功能：**
- 自动更新 Info.plist 中的版本号
- 清理构建缓存
- 构建 Archive
- 导出 IPA 文件
- 生成发布说明
- 提供下一步操作指导

**输出文件：**
- `build/CabyCare.xcarchive` - Archive 文件
- `build/CabyCare.ipa` - IPA 安装包
- `build/release_notes_[version].txt` - 发布说明

### 2. `check_health.sh` - 项目健康检查脚本
全面检查项目的代码质量、安全性和维护状态。

**使用方法：**
```bash
./scripts/check_health.sh
```

**检查项目：**
- 项目结构完整性
- Git 状态和同步
- 代码质量（TODO/FIXME、长函数）
- 依赖管理状态
- 文件大小和性能
- 安全性检查
- 测试覆盖率
- 文档完整性

**输出：**
- 详细的检查报告
- 项目健康度评分（0-100%）
- 改进建议

## 🚀 快速开始

### 首次使用设置
1. 确保脚本有执行权限：
   ```bash
   chmod +x scripts/*.sh
   ```

2. 检查项目健康状态：
   ```bash
   ./scripts/check_health.sh
   ```

3. 构建 TestFlight 版本：
   ```bash
   ./scripts/build_testflight.sh 0.4.8
   ```

### 日常维护流程

#### 每周维护（推荐周一执行）
```bash
# 1. 检查项目健康状态
./scripts/check_health.sh

# 2. 如果有问题，根据报告进行修复
# 3. 提交所有更改
git add .
git commit -m "Weekly maintenance: fix health check issues"

# 4. 推送到远程仓库
git push origin main
```

#### 发布准备
```bash
# 1. 确保所有更改已提交
git status

# 2. 运行健康检查
./scripts/check_health.sh

# 3. 如果健康检查通过，构建 TestFlight 版本
./scripts/build_testflight.sh 0.4.8

# 4. 创建版本标签
git tag -a v0.4.8 -m "Version 0.4.8 - 头像管理功能"
git push origin v0.4.8
```

## ⚙️ 配置说明

### build_testflight.sh 配置
在使用构建脚本前，需要修改以下配置：

1. **开发者团队 ID**：
   编辑 `scripts/build_testflight.sh`，将 `YOUR_TEAM_ID` 替换为你的实际团队 ID。

2. **ExportOptions.plist**：
   脚本会自动创建默认的导出配置，如需自定义，可以手动创建此文件。

### 环境要求
- macOS 系统
- Xcode 命令行工具
- Git
- 项目必须在 Git 仓库中

## 🔧 故障排除

### 常见问题

**Q: 构建脚本报错 "找不到开发者证书"**
A: 确保在 Xcode 中正确配置了开发者账户和证书，并将脚本中的 `YOUR_TEAM_ID` 替换为正确的团队 ID。

**Q: 健康检查显示很多警告**
A: 这是正常的，警告不会影响应用运行，但建议逐步改进。优先处理错误级别的问题。

**Q: IPA 文件过大**
A: 检查是否包含了不必要的资源文件，使用 Asset Catalog 管理图片资源，启用 App Thinning。

**Q: 脚本权限不足**
A: 运行 `chmod +x scripts/*.sh` 为脚本添加执行权限。

### 调试模式
如需调试脚本执行过程，可以使用：
```bash
bash -x ./scripts/build_testflight.sh
```

## 📊 最佳实践

### 1. 版本号管理
- 遵循语义化版本控制（Semantic Versioning）
- 主版本号：重大功能更新或不兼容的 API 更改
- 次版本号：新功能添加，向后兼容
- 修订版本号：bug 修复

### 2. 构建号管理
- 每次构建都应该有唯一的构建号
- 可以使用时间戳：`YYYYMMDDHHMM`
- 或者使用递增数字

### 3. 发布节奏
- TestFlight：每周 1-2 次
- App Store：每月一次主要更新
- 热修复：按需发布

### 4. 质量保证
- 每次发布前运行健康检查
- 保持项目健康度在 80% 以上
- 及时处理 TODO 和 FIXME
- 定期更新依赖库

## 📞 支持

如果在使用脚本过程中遇到问题：

1. 首先查看本文档的故障排除部分
2. 检查脚本输出的错误信息
3. 确保环境配置正确
4. 联系开发团队获取帮助

---

**记住**：自动化是提高开发效率的关键，但理解每个步骤的作用同样重要！ 