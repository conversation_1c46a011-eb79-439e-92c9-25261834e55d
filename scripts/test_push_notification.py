import jwt
import time
import json
import ssl
import socket
from h2.connection import H2Connection
from h2.events import (
    ResponseReceived, DataReceived, StreamEnded
)
from hyperframe.frame import SettingsFrame

# Apple开发者账号配置
TEAM_ID = "5WZYK34FW5"  # 你的Apple开发者团队ID
KEY_ID = "G46PP8JK59"    # 你的APNs密钥ID
BUNDLE_ID = "com.animsilicon.CabyCare"  # 修改为你实际的 Bundle ID
AUTH_KEY_PATH = "/home/<USER>/AuthKey_G46PP8JK59.p8"  # 你的APNs认证密钥文件路径

# 设备令牌 (从NotificationManager打印的token中获取)
DEVICE_TOKEN = "804d3ddcef43b62f1cc780a385ad699a8aa8c18d0a4666002316a15c066e6ad3e321bc8491cc43fc234e0ada2438014503d78a85aaa18e803732e5a521786e9116d3746e1ff865fd014e91f91196a13e"

def create_token():
    """创建JWT token用于APNs认证"""
    try:
        with open(AUTH_KEY_PATH, "r") as f:
            secret = f.read()

        time_now = time.time()
        token = jwt.encode(
            {
                "iss": TEAM_ID,
                "iat": time_now
            },
            secret,
            algorithm="ES256",
            headers={
                "kid": KEY_ID,
                "alg": "ES256"
            }
        )
        print("✅ Token created successfully")
        return token
    except Exception as e:
        print(f"❌ Error creating token: {str(e)}")
        raise

def send_push_notification(token, device_token, payload):
    """发送推送通知"""
    # APNs开发环境服务器
    host = "api.development.push.apple.com"
    port = 443

    path = f"/3/device/{device_token}"

    # 将payload转换为JSON字符串并编码为bytes
    json_payload = json.dumps(payload).encode('utf-8')

    # 设置请求头 - 包含content-length
    headers = [
        (b':method', b'POST'),
        (b':scheme', b'https'),
        (b':path', path.encode('utf-8')),
        (b':authority', host.encode('utf-8')),
        (b'authorization', f"bearer {token}".encode('utf-8')),
        (b'apns-topic', BUNDLE_ID.encode('utf-8')),
        (b'apns-push-type', b'alert'),
        (b'content-type', b'application/json'),
        (b'content-length', str(len(json_payload)).encode('utf-8'))
    ]

    print(f"\n📤 Sending notification to device:")
    print(f"Host: {host}")
    print(f"Path: {path}")
    print(f"Headers: {[(k.decode('utf-8', 'ignore'), v.decode('utf-8', 'ignore') if not k.startswith(b':') else v.decode('utf-8', 'ignore')) for k, v in headers]}")
    print(f"Payload: {json.dumps(payload, indent=2)}")

    # 创建TLS上下文
    context = ssl.create_default_context()
    context.set_alpn_protocols(['h2'])

    try:
        # 创建TCP套接字
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)  # 设置连接超时

        # 包装成TLS套接字
        tls_sock = context.wrap_socket(
            sock,
            server_hostname=host
        )

        # 连接到APNs服务器
        tls_sock.connect((host, port))

        # 确保协商的协议是HTTP/2
        negotiated_protocol = tls_sock.selected_alpn_protocol()
        if negotiated_protocol != 'h2':
            raise Exception(f"服务器不支持HTTP/2, 协商的协议: {negotiated_protocol}")

        # 创建H2Connection实例
        conn = H2Connection()
        conn.initiate_connection()

        # 发送连接前言
        tls_sock.sendall(conn.data_to_send())

        # 发送请求 - 使用正确的API
        stream_id = conn.get_next_available_stream_id()
        conn.send_headers(stream_id=stream_id, headers=headers)

        # 发送数据并标记为流结束
        conn.send_data(stream_id=stream_id, data=json_payload, end_stream=True)

        # 发送帧数据
        tls_sock.sendall(conn.data_to_send())

        # 处理响应
        response_data = b''
        status_code = None

        # 读取响应
        while True:
            data = tls_sock.recv(65536)
            if not data:
                break

            events = conn.receive_data(data)
            tls_sock.sendall(conn.data_to_send())

            for event in events:
                if isinstance(event, ResponseReceived):
                    # 获取状态码
                    for header, value in event.headers:
                        if header == b':status':
                            status_code = int(value.decode('utf-8'))
                            print(f"Received status code: {status_code}")

                elif isinstance(event, DataReceived):
                    response_data += event.data
                    print(f"Received data: {event.data}")

                elif isinstance(event, StreamEnded):
                    print("Stream ended")
                    # 如果流结束了，我们就可以退出外层循环
                    break

            # 如果已经收到了状态码和流结束事件，可以退出
            if status_code is not None and isinstance(event, StreamEnded):
                break

        print(f"\n📥 Response:")
        print(f"Status: {status_code}")

        if status_code == 200:
            print("✅ Notification sent successfully")
        else:
            try:
                if response_data:
                    response_text = response_data.decode('utf-8')
                    print(f"❌ Error: {response_text}")
                else:
                    print(f"❌ Error with status code: {status_code}")
            except Exception as e:
                print(f"❌ Error with status code: {status_code}")
                print(f"Raw response: {response_data}")
                print(f"Decode error: {str(e)}")

    except Exception as e:
        print(f"❌ Connection error: {str(e)}")
        raise
    finally:
        if 'tls_sock' in locals():
            tls_sock.close()

def create_test_notifications():
    """创建各种类型的测试通知"""
    return [
        # 日常类通知测试
        {
            "aps": {
                "alert": {
                    "title": "排泄提醒",
                    "body": "检测到猫咪排泄行为"
                },
                "sound": "default"
            },
            "type": "daily",
            "subtype": "excretion",
            "id": "excretion_001",
            "timestamp": int(time.time()),
            "metadata": {
                "catId": "cat_001",
                "videoId": "video_001",
                "videoTimestamp": int(time.time())
            }
        },
        # 异常类通知测试
        {
            "aps": {
                "alert": {
                    "title": "异常警告",
                    "body": "检测到异常动物出现"
                },
                "sound": "default"
            },
            "type": "alert",
            "subtype": "foreignAnimal",
            "id": "alert_001",
            "timestamp": int(time.time()),
            "metadata": {
                "catId": "cat_001",
                "videoId": "video_002",
                "videoTimestamp": int(time.time())
            }
        },
        # 统计类通知测试
        {
            "aps": {
                "alert": {
                    "title": "每日统计",
                    "body": "您的猫咪今天排泄了3次"
                },
                "sound": "default"
            },
            "type": "stats",
            "subtype": "dailyStats",
            "id": "stats_001",
            "timestamp": int(time.time()),
            "metadata": {
                "statsStartDate": int(time.time()) - 86400,
                "statsEndDate": int(time.time())
            }
        },
        # 健康类通知测试
        {
            "aps": {
                "alert": {
                    "title": "健康提醒",
                    "body": "检测到猫咪体重变化"
                },
                "sound": "default"
            },
            "type": "health",
            "subtype": "weightChange",
            "id": "health_001",
            "timestamp": int(time.time()),
            "metadata": {
                "catId": "cat_001",
                "healthMetric": "weight",
                "oldValue": "4.5",
                "newValue": "4.8"
            }
        },
        # 维护类通知测试
        {
            "aps": {
                "alert": {
                    "title": "系统维护",
                    "body": "检测到新的猫咪"
                },
                "sound": "default"
            },
            "type": "maintain",
            "subtype": "newCat",
            "id": "maintain_001",
            "timestamp": int(time.time()),
            "metadata": {
                "catId": "cat_002",
                "maintenanceType": "newCatDetected",
                "severity": 1
            }
        }
    ]

def main():
    try:
        # 创建认证token
        token = create_token()

        # 获取测试通知列表
        notifications = create_test_notifications()

        # 依次发送每个测试通知
        for notification in notifications:
            print(f"\n🔔 Testing notification type: {notification['type']}")
            print(f"Subtype: {notification['subtype']}")

            try:
                send_push_notification(token, DEVICE_TOKEN, notification)
                # 等待5秒再发送下一条通知
                time.sleep(5)
            except Exception as e:
                print(f"❌ Failed to send notification: {str(e)}")
                continue

        print("\n✅ All test notifications sent")

    except Exception as e:
        print(f"❌ Program failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()