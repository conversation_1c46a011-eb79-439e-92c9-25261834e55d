#!/bin/bash

# TestFlight 自动化构建脚本
# 使用方法: ./build_testflight.sh [version] [build]

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查参数
VERSION=${1:-"0.4.8"}
BUILD=${19:-$(date +%Y%m%d%H%M)}

log_info "开始构建 TestFlight 版本 $VERSION (Build $BUILD)"

# 检查工作目录
if [ ! -f "CabyCare.xcodeproj/project.pbxproj" ]; then
    log_error "请在项目根目录运行此脚本"
    exit 1
fi

# 创建构建目录
mkdir -p build
log_info "创建构建目录"

# 更新版本号
log_info "更新版本号..."
/usr/libexec/PlistBuddy -c "Set :CFBundleShortVersionString $VERSION" CabyCare/Info.plist
/usr/libexec/PlistBuddy -c "Set :CFBundleVersion $BUILD" CabyCare/Info.plist
log_success "版本号更新完成: $VERSION ($BUILD)"

# 清理构建缓存
log_info "清理构建缓存..."
xcodebuild clean -project CabyCare.xcodeproj -scheme CabyCare -configuration Release
log_success "构建缓存清理完成"

# 构建 Archive
log_info "开始构建 Archive..."

# 检查是否安装了 xcpretty
if command -v xcpretty >/dev/null 2>&1; then
    XCPRETTY_CMD="| xcpretty"
else
    XCPRETTY_CMD=""
    log_warning "xcpretty 未安装，将显示原始输出"
fi

eval "xcodebuild archive \
  -project CabyCare.xcodeproj \
  -scheme CabyCare \
  -archivePath ./build/CabyCare.xcarchive \
  -configuration Release \
  -destination \"generic/platform=iOS\" \
  CODE_SIGN_STYLE=Automatic \
  DEVELOPMENT_TEAM=\"YOUR_TEAM_ID\" \
  $XCPRETTY_CMD"

if [ $? -eq 0 ]; then
    log_success "Archive 构建完成"
else
    log_error "Archive 构建失败"
    exit 1
fi

# 检查 ExportOptions.plist 是否存在
if [ ! -f "ExportOptions.plist" ]; then
    log_warning "ExportOptions.plist 不存在，创建默认配置..."
    cat > ExportOptions.plist << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>method</key>
    <string>app-store</string>
    <key>uploadBitcode</key>
    <false/>
    <key>uploadSymbols</key>
    <true/>
    <key>compileBitcode</key>
    <false/>
</dict>
</plist>
EOF
fi

# 导出 IPA
log_info "导出 IPA..."
eval "xcodebuild -exportArchive \
  -archivePath ./build/CabyCare.xcarchive \
  -exportPath ./build \
  -exportOptionsPlist ExportOptions.plist \
  $XCPRETTY_CMD"

if [ $? -eq 0 ]; then
    log_success "IPA 导出完成"
    log_info "文件位置: ./build/CabyCare.ipa"
else
    log_error "IPA 导出失败"
    exit 1
fi

# 生成发布说明
log_info "生成发布说明..."
cat > "./build/release_notes_${VERSION}.txt" << EOF
## CabyCare ${VERSION} (Build ${BUILD})

### 本次更新内容：
$(git log --pretty=format:"- %s" $(git describe --tags --abbrev=0 2>/dev/null || echo "HEAD~10")..HEAD 2>/dev/null || echo "- 初始版本")

### 构建信息：
- 构建时间: $(date)
- Git 提交: $(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
- 构建环境: $(uname -s) $(uname -r)

### 测试重点：
- 头像上传和管理功能
- 猫咪档案编辑功能
- 网络请求稳定性
- UI 响应性能

### 反馈方式：
- TestFlight 内置反馈
- 邮件：<EMAIL>
EOF

log_success "发布说明已生成: ./build/release_notes_${VERSION}.txt"

# 显示文件大小
IPA_SIZE=$(du -h "./build/CabyCare.ipa" | cut -f1)
log_info "IPA 文件大小: $IPA_SIZE"

# 提示下一步操作
echo ""
log_success "🎉 TestFlight 构建完成！"
echo ""
log_info "下一步操作："
echo "1. 在 Xcode 中上传到 App Store Connect"
echo "2. 或使用 Application Loader 上传 IPA 文件"
echo "3. 在 App Store Connect 中配置 TestFlight"
echo "4. 邀请测试者并发送测试说明"
echo ""
log_info "构建文件位置:"
echo "- Archive: ./build/CabyCare.xcarchive"
echo "- IPA: ./build/CabyCare.ipa"
echo "- 发布说明: ./build/release_notes_${VERSION}.txt" 