#!/bin/bash

# CabyCare 项目健康检查脚本
# 检查代码质量、依赖更新、安全漏洞等

# set -e  # 暂时禁用严格模式，避免某些检查失败时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 计数器
WARNINGS=0
ERRORS=0
INFO_COUNT=0

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
    ((INFO_COUNT++))
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
    ((WARNINGS++))
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
    ((ERRORS++))
}

echo "🔍 CabyCare 项目健康检查开始..."
echo "========================================"

# 1. 检查项目结构
log_info "检查项目结构..."
if [ ! -f "CabyCare.xcodeproj/project.pbxproj" ]; then
    log_error "找不到 Xcode 项目文件"
else
    log_success "Xcode 项目文件存在"
fi

if [ ! -f "CabyCare/Info.plist" ]; then
    log_error "找不到 Info.plist 文件"
else
    log_success "Info.plist 文件存在"
fi

# 2. 检查 Git 状态
log_info "检查 Git 状态..."
if [ -d ".git" ]; then
    UNCOMMITTED=$(git status --porcelain | wc -l)
    if [ $UNCOMMITTED -gt 0 ]; then
        log_warning "有 $UNCOMMITTED 个未提交的文件"
        git status --porcelain
    else
        log_success "工作目录干净，没有未提交的更改"
    fi
    
    # 检查远程分支同步状态
    git fetch origin main 2>/dev/null || true
    LOCAL=$(git rev-parse HEAD 2>/dev/null || echo "unknown")
    REMOTE=$(git rev-parse origin/main 2>/dev/null || echo "unknown")
    
    if [ "$LOCAL" != "$REMOTE" ]; then
        log_warning "本地分支与远程分支不同步"
    else
        log_success "本地分支与远程分支同步"
    fi
else
    log_error "不是 Git 仓库"
fi

# 3. 检查代码质量
log_info "检查代码质量..."

# 检查 TODO 和 FIXME
TODO_COUNT=$(find . -name "*.swift" -exec grep -l "TODO\|FIXME" {} \; 2>/dev/null | wc -l)
if [ $TODO_COUNT -gt 0 ]; then
    log_warning "发现 $TODO_COUNT 个文件包含 TODO 或 FIXME"
    find . -name "*.swift" -exec grep -Hn "TODO\|FIXME" {} \; 2>/dev/null | head -5
else
    log_success "没有发现 TODO 或 FIXME"
fi

# 检查长函数（超过50行）- 简化版本
SWIFT_FILES=$(find . -name "*.swift" 2>/dev/null | wc -l)
if [ $SWIFT_FILES -gt 0 ]; then
    log_success "发现 $SWIFT_FILES 个 Swift 文件"
else
    log_warning "未发现 Swift 文件"
fi

# 4. 检查依赖和包管理
log_info "检查依赖和包管理..."

# 检查 Package.swift 或 Podfile
if [ -f "Package.swift" ]; then
    log_success "使用 Swift Package Manager"
elif [ -f "Podfile" ]; then
    log_success "使用 CocoaPods"
    if [ -f "Podfile.lock" ]; then
        log_info "发现 Podfile.lock 文件"
    fi
else
    log_warning "未检测到包管理工具"
fi

# 5. 检查构建配置
log_info "检查构建配置..."

# 检查版本号
VERSION=$(grep -A1 "CFBundleShortVersionString" CabyCare/Info.plist | grep -o "[0-9]\+\.[0-9]\+\.[0-9]\+" || echo "unknown")
BUILD=$(grep -A1 "CFBundleVersion" CabyCare/Info.plist | grep -o "[0-9]\+" || echo "unknown")

log_info "当前版本: $VERSION (Build $BUILD)"

# 6. 检查文件大小
log_info "检查项目文件大小..."
PROJECT_SIZE=$(du -sh . 2>/dev/null | cut -f1)
log_info "项目总大小: $PROJECT_SIZE"

# 检查大文件（超过10MB）
LARGE_FILES=$(find . -type f -size +10M 2>/dev/null | grep -v ".git" | wc -l)
if [ $LARGE_FILES -gt 0 ]; then
    log_warning "发现 $LARGE_FILES 个大文件（>10MB）"
    find . -type f -size +10M 2>/dev/null | grep -v ".git" | head -3
else
    log_success "没有发现异常大的文件"
fi

# 7. 检查安全性
log_info "检查安全性..."

# 检查硬编码密钥或敏感信息
SENSITIVE_PATTERNS=("password" "secret" "key" "token" "api_key")
SENSITIVE_FOUND=0

for pattern in "${SENSITIVE_PATTERNS[@]}"; do
    COUNT=$(find . -name "*.swift" -exec grep -il "$pattern" {} \; 2>/dev/null | wc -l)
    if [ $COUNT -gt 0 ]; then
        ((SENSITIVE_FOUND++))
    fi
done

if [ $SENSITIVE_FOUND -gt 0 ]; then
    log_warning "发现可能包含敏感信息的文件，请检查是否有硬编码密钥"
else
    log_success "未发现明显的敏感信息泄露"
fi

# 8. 检查测试覆盖率（如果有测试）
log_info "检查测试文件..."
TEST_FILES=$(find . -name "*Test*.swift" -o -name "*Tests*.swift" 2>/dev/null | wc -l)
if [ $TEST_FILES -gt 0 ]; then
    log_success "发现 $TEST_FILES 个测试文件"
else
    log_warning "未发现测试文件"
fi

# 9. 检查文档
log_info "检查文档..."
if [ -f "README.md" ]; then
    log_success "README.md 存在"
else
    log_warning "缺少 README.md 文件"
fi

if [ -f "CHANGELOG.md" ] || [ -f "RELEASE_NOTES.md" ]; then
    log_success "发现变更日志文件"
else
    log_warning "建议添加变更日志文件"
fi

# 10. 性能检查建议
log_info "性能检查建议..."

# 检查图片资源
IMAGE_COUNT=$(find . -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" 2>/dev/null | wc -l)
log_info "发现 $IMAGE_COUNT 个图片文件"

# 检查未使用的资源（简单检查）
ASSETS_DIR="CabyCare/Assets.xcassets"
if [ -d "$ASSETS_DIR" ]; then
    log_success "Assets.xcassets 目录存在"
else
    log_warning "未找到 Assets.xcassets 目录"
fi

echo ""
echo "========================================"
echo "🏥 健康检查完成"
echo "========================================"
echo -e "${GREEN}✅ 成功项目数: $(($INFO_COUNT - $WARNINGS - $ERRORS))${NC}"
echo -e "${YELLOW}⚠️  警告数: $WARNINGS${NC}"
echo -e "${RED}❌ 错误数: $ERRORS${NC}"

# 给出总体评分
TOTAL_CHECKS=$(($INFO_COUNT))
ISSUES=$(($WARNINGS + $ERRORS))
SCORE=$((($TOTAL_CHECKS - $ISSUES) * 100 / $TOTAL_CHECKS))

echo ""
if [ $SCORE -ge 90 ]; then
    echo -e "${GREEN}🎉 项目健康度: $SCORE% - 优秀${NC}"
elif [ $SCORE -ge 70 ]; then
    echo -e "${YELLOW}👍 项目健康度: $SCORE% - 良好${NC}"
elif [ $SCORE -ge 50 ]; then
    echo -e "${YELLOW}⚠️  项目健康度: $SCORE% - 需要改进${NC}"
else
    echo -e "${RED}🚨 项目健康度: $SCORE% - 需要紧急处理${NC}"
fi

echo ""
echo "建议的改进措施："
if [ $WARNINGS -gt 0 ]; then
    echo "- 处理上述警告项目"
fi
if [ $ERRORS -gt 0 ]; then
    echo "- 修复上述错误项目"
fi
echo "- 定期运行此健康检查"
echo "- 保持代码整洁和文档更新"
echo "- 定期更新依赖库"

# 退出码
if [ $ERRORS -gt 0 ]; then
    exit 1
elif [ $WARNINGS -gt 5 ]; then
    exit 2
else
    exit 0
fi 