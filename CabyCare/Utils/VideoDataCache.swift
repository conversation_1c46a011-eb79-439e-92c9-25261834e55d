import Foundation

actor VideoDataCache {
    static let shared = VideoDataCache()

    private let fileManager = FileManager.default
    private let cacheFileName = "video_data_cache.json"

    private var cacheURL: URL? {
        fileManager.urls(for: .cachesDirectory, in: .userDomainMask).first?
            .appendingPathComponent(cacheFileName)
    }

    struct CachedData: Codable {
        let dates: [Date]
        let segments: [VideoSegment]
        let lastSelectedDate: Date?
        let timestamp: Date
    }

    func loadCache() -> (dates: [Date], segments: [VideoSegment], lastSelectedDate: Date?)? {
        guard let url = cacheURL,
              let data = try? Data(contentsOf: url),
              let cachedData = try? JSONDecoder().decode(CachedData.self, from: data)
        else {
            return nil
        }

        // 检查缓存是否过期（24小时）
        if Date().timeIntervalSince(cachedData.timestamp) > 24 * 3600 {
            try? fileManager.removeItem(at: url)
            return nil
        }

        return (
            dates: cachedData.dates,
            segments: cachedData.segments,
            lastSelectedDate: cachedData.lastSelectedDate
        )
    }

    func updateCache(dates: [Date], segments: [VideoSegment], lastSelectedDate: Date?) {
        guard let url = cacheURL else { return }

        let cachedData = CachedData(
            dates: dates,
            segments: segments,
            lastSelectedDate: lastSelectedDate,
            timestamp: Date()
        )

        do {
            let data = try JSONEncoder().encode(cachedData)
            try data.write(to: url)
        } catch {
            print("❌ Failed to save cache: \(error)")
        }
    }

    func clearCache() {
        guard let url = cacheURL else { return }
        try? fileManager.removeItem(at: url)
    }
}