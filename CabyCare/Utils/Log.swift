import Foundation

/// 日志工具类
enum Log {
    /// 在控制台中打印信息日志
    static func info(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        let fileName = URL(fileURLWithPath: file).lastPathComponent
        print("ℹ️ INFO [\(fileName):\(line)] \(function): \(message)")
        
        // 写入到文件日志
        appendToLogFile("INFO", message)
    }
    
    /// 在控制台中打印调试日志，仅在DEBUG模式下显示
    static func debug(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        #if DEBUG
        let fileName = URL(fileURLWithPath: file).lastPathComponent
        print("🔍 DEBUG [\(fileName):\(line)] \(function): \(message)")
        
        // 写入到文件日志
        appendToLogFile("DEBUG", message)
        #endif
    }
    
    /// 在控制台中打印警告日志
    static func warning(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        let fileName = URL(fileURLWithPath: file).lastPathComponent
        print("⚠️ WARNING [\(fileName):\(line)] \(function): \(message)")
        
        // 写入到文件日志
        appendToLogFile("WARNING", message)
    }
    
    /// 在控制台中打印错误日志
    static func error(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        let fileName = URL(fileURLWithPath: file).lastPathComponent
        print("❌ ERROR [\(fileName):\(line)] \(function): \(message)")
        
        // 写入到文件日志
        appendToLogFile("ERROR", message)
    }
    
    /// 获取格式化的时间戳
    private static func formattedTimestamp() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS"
        return formatter.string(from: Date())
    }
    
    /// 将日志追加到文件中
    private static func appendToLogFile(_ level: String, _ message: String) {
        guard let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return
        }
        
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        let dateString = formatter.string(from: Date())
        
        let logFileURL = documentsDirectory.appendingPathComponent("CabyCare_\(dateString).log")
        
        let timestamp = formattedTimestamp()
        let logMessage = "[\(timestamp)] [\(level)] \(message)\n"
        
        do {
            if FileManager.default.fileExists(atPath: logFileURL.path) {
                let fileHandle = try FileHandle(forWritingTo: logFileURL)
                fileHandle.seekToEndOfFile()
                if let data = logMessage.data(using: .utf8) {
                    fileHandle.write(data)
                }
                fileHandle.closeFile()
            } else {
                try logMessage.write(to: logFileURL, atomically: true, encoding: .utf8)
            }
        } catch {
            print("❌ 无法写入日志文件: \(error.localizedDescription)")
        }
    }
    
    /// 导出日志文件
    static func exportLogFiles() -> [URL] {
        guard let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return []
        }
        
        do {
            let fileURLs = try FileManager.default.contentsOfDirectory(at: documentsDirectory, 
                                                                      includingPropertiesForKeys: nil)
            return fileURLs.filter { $0.pathExtension == "log" }
        } catch {
            print("❌ 无法获取日志文件列表: \(error.localizedDescription)")
            return []
        }
    }
    
    // 新增：认证状态监控专用方法
    static func authStatus(_ message: String, tokenInfo: (isValid: Bool, remainingTime: TimeInterval?, status: String)? = nil) {
        var logMessage = "🔐 AUTH: \(message)"
        
        if let info = tokenInfo {
            if let remainingTime = info.remainingTime {
                let minutes = Int(remainingTime / 60)
                let seconds = Int(remainingTime.truncatingRemainder(dividingBy: 60))
                logMessage += " | 状态: \(info.status) | 剩余: \(minutes)分\(seconds)秒"
            } else {
                logMessage += " | 状态: \(info.status)"
            }
        }
        
        print(logMessage)
    }
    
    // 新增：网络请求监控专用方法
    static func network(_ message: String, url: String? = nil, statusCode: Int? = nil, error: Error? = nil) {
        var logMessage = "🌐 NETWORK: \(message)"
        
        if let url = url {
            logMessage += " | URL: \(url)"
        }
        
        if let statusCode = statusCode {
            logMessage += " | Status: \(statusCode)"
        }
        
        if let error = error {
            logMessage += " | Error: \(error.localizedDescription)"
        }
        
        print(logMessage)
    }
    
    // 新增：令牌操作专用方法
    static func token(_ operation: String, success: Bool, details: String? = nil) {
        let emoji = success ? "✅" : "❌"
        var logMessage = "\(emoji) TOKEN \(operation.uppercased())"
        
        if let details = details {
            logMessage += " | \(details)"
        }
        
        print(logMessage)
    }
}
