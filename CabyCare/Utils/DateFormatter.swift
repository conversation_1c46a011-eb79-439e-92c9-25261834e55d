import Foundation

// 重命名为 DateFormatterUtil 以避免与系统 DateFormatter 冲突
struct DateFormatterUtil {
    private static let shared: Foundation.DateFormatter = {
        let formatter = Foundation.DateFormatter()
        formatter.timeZone = TimeZone(identifier: "UTC")!
        return formatter
    }()

    // 使用 nonisolated(unsafe) 来标记为并发安全的静态属性
    // ISO8601DateFormatter 虽然不是 Sendable，但在实际使用中是线程安全的
    nonisolated(unsafe) private static let iso8601Formatter: ISO8601DateFormatter = {
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime]
        formatter.timeZone = TimeZone(identifier: "UTC")!
        return formatter
    }()

    enum Format: String {
        case api = "yyyy-MM-dd"
        case iso8601 // 使用 ISO8601DateFormatter
        case time = "HH:mm:ss"
        case display = "MMM d, yyyy"
    }

    static func format(_ date: Date, as format: Format) -> String {
        switch format {
        case .iso8601:
            return iso8601Formatter.string(from: date)
        default:
            shared.dateFormat = format.rawValue
            return shared.string(from: date)
        }
    }

    // 使用指定时区格式化时间
    static func formatWithTimezone(_ date: Date, as format: Format, timezone: String) -> String {
        let formatter = Foundation.DateFormatter()
        formatter.dateFormat = format.rawValue
        formatter.timeZone = TimeZone(identifier: timezone) ?? TimeZone.current
        return formatter.string(from: date)
    }

    // 使用本地时区格式化时间
    static func formatWithLocalTimeZone(_ date: Date, as format: Format) -> String {
        let formatter = Foundation.DateFormatter()
        formatter.dateFormat = format.rawValue
        formatter.timeZone = TimeZone.current
        return formatter.string(from: date)
    }

    // 特别为时间显示提供的便捷方法
    static func formatTimeLocal(_ date: Date) -> String {
        return formatWithLocalTimeZone(date, as: .time)
    }

    // 使用设备时区格式化时间
    static func formatTimeWithDeviceTimezone(_ date: Date, timezone: String) -> String {
        return formatWithTimezone(date, as: .time, timezone: timezone)
    }

    static func date(from string: String, format: Format) -> Date? {
        switch format {
        case .iso8601:
            return iso8601Formatter.date(from: string)
        default:
            shared.dateFormat = format.rawValue
            return shared.date(from: string)
        }
    }
}