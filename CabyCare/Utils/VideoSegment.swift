import Foundation

struct VideoSegment: Codable, Identifiable {
    let start: Date
    let duration: TimeInterval
    private(set) var url: String
    let weight_litter: Double
    let weight_cat: Double
    let weight_waste: Double
    let animal_id: String?  // 添加猫咪ID字段
    var index: Int
    var deviceName: String?  // 添加设备名称字段

    var id: String {
        Configuration.DateFormat.formatISO8601(start)
    }

    var formattedDuration: String {
        let totalSeconds = Int(duration)
        let minutes = totalSeconds / 60
        let seconds = totalSeconds % 60
        
        // 使用本地化字符串，更清晰地表示时长
        if minutes > 0 {
            if seconds == 0 {
                return String(format: NSLocalizedString("video_duration_minutes_only", comment: "Duration in minutes only"), minutes)
            } else {
                return String(format: NSLocalizedString("video_duration_minutes_seconds", comment: "Duration in minutes and seconds"), minutes, seconds)
            }
        } else {
            return String(format: NSLocalizedString("video_duration_seconds_only", comment: "Duration in seconds only"), seconds)
        }
    }

    enum CodingKeys: String, CodingKey {
        case start, duration, url, weight_litter, weight_cat, weight_waste, animal_id
        // deviceName 不在 CodingKeys 中，因为它不是从API返回的，而是在本地添加的
    }

    init(start: Date, duration: TimeInterval, url: String,
          weightLitter: Double, weightCat: Double, weightWaste: Double,
          index: Int, deviceName: String? = nil, animalId: String? = nil) {
        self.start = start
        self.duration = duration
        self.url = url
        self.weight_litter = weightLitter
        self.weight_cat = weightCat
        self.weight_waste = weightWaste
        self.animal_id = animalId
        self.index = index
        self.deviceName = deviceName
    }
    
    // 添加支持Unix时间戳的初始化方法
    init(startTimestamp: Double, duration: TimeInterval, url: String,
         weightLitter: Double, weightCat: Double, weightWaste: Double,
         index: Int, deviceName: String? = nil, animalId: String? = nil) {
        // 将Unix时间戳转换为Date对象
        self.start = Date(timeIntervalSince1970: startTimestamp)
        self.duration = duration
        self.url = url
        self.weight_litter = weightLitter
        self.weight_cat = weightCat
        self.weight_waste = weightWaste
        self.animal_id = animalId
        self.index = index
        self.deviceName = deviceName
    }

    // 添加一个方法来更新设备名称
    mutating func updateDeviceName(_ name: String?) {
        deviceName = name
    }

    // 添加一个方法来更新 URL
    mutating func updateUrl(_ newUrl: String) {
        url = newUrl
    }

    // 用于错误处理和重试
    mutating func updateWithAuthToken() async {
        // 从MainActor隔离的AuthManager安全获取token
        let token = await MainActor.run { AuthManager.shared.accessToken }

        // 如果URL不包含token参数，且我们有访问令牌，可以在这里更新URL
        if let accessToken = token, !url.contains("token=") {
            // 根据实际API要求更新URL
            let separator = url.contains("?") ? "&" : "?"
            url += "\(separator)token=\(accessToken)"
        }
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        // 解码开始时间 - 尝试先作为数字（Unix时间戳）解码
        if let startTimestamp = try? container.decode(Double.self, forKey: .start) {
            // 如果成功，直接使用时间戳创建Date对象
            self.start = Date(timeIntervalSince1970: startTimestamp)
        } else {
            // 如果失败，尝试作为字符串解码
            let startValue = try container.decode(String.self, forKey: .start)
            
            // 尝试将字符串转换为时间戳
            if let unixTimestamp = Double(startValue) {
                // 如果是数字格式字符串，作为Unix时间戳处理
                self.start = Date(timeIntervalSince1970: unixTimestamp)
            } else {
                // 否则尝试作为ISO8601字符串处理
                guard let startDate = DateFormatterUtil.date(from: startValue, format: .iso8601) else {
                    throw DecodingError.dataCorrupted(
                        DecodingError.Context(
                            codingPath: container.codingPath,
                            debugDescription: "Invalid date format: \(startValue)"
                        )
                    )
                }
                self.start = startDate
            }
        }

        // 解码持续时间
        // 尝试先作为数字解码
        if let durationValue = try? container.decode(Double.self, forKey: .duration) {
            self.duration = durationValue
        } else {
            // 如果失败，尝试作为字符串解码
            let durationString = try container.decode(String.self, forKey: .duration)
            self.duration = Double(durationString) ?? 0
        }

        // 直接使用服务器返回的 URL
        self.url = try container.decode(String.self, forKey: .url)

        // 处理权重字段 - 支持数字和字符串格式
        self.weight_litter = try Self.decodeWeight(from: container, for: .weight_litter)
        self.weight_cat = try Self.decodeWeight(from: container, for: .weight_cat)
        self.weight_waste = try Self.decodeWeight(from: container, for: .weight_waste)

        // 解码 animal_id，可能为空
        self.animal_id = try container.decodeIfPresent(String.self, forKey: .animal_id)

        self.index = 0
        self.deviceName = nil  // 设备名称将在DeviceManager中设置
    }

    // 通用方法来解码权重字段，处理不同的数据类型
    private static func decodeWeight(from container: KeyedDecodingContainer<CodingKeys>, for key: CodingKeys) throws -> Double {
        // 首先尝试直接解码为Double
        if let numberValue = try? container.decodeIfPresent(Double.self, forKey: key) {
            return numberValue
        }

        // 如果失败，尝试解码为String并转换
        if let stringValue = try? container.decodeIfPresent(String.self, forKey: key),
           let numberFromString = Double(stringValue) {
            return numberFromString
        }

        // 如果两种方式都失败，返回默认值
        return key == .weight_litter ? -41.415 : (key == .weight_cat ? -4.1415 : -0.41415)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(Configuration.DateFormat.formatISO8601(start), forKey: .start)
        try container.encode(String(format: "%.3f", duration), forKey: .duration)
        try container.encode(url, forKey: .url)
        try container.encode(animal_id, forKey: .animal_id)
    }
}

// 添加调试扩展，用于打印解码JSON
extension Decodable {
    static func debugPrint(jsonData: Data) {
        do {
            let json = try JSONSerialization.jsonObject(with: jsonData, options: .allowFragments)
            if let prettyJsonData = try? JSONSerialization.data(withJSONObject: json, options: .prettyPrinted) {
                let jsonString = String(data: prettyJsonData, encoding: .utf8) ?? "无法显示JSON数据"
                print("📦 解析的JSON数据:\n\(jsonString)")
            }
        } catch {
            print("⚠️ 无法解析JSON数据: \(error)")
            if let dataString = String(data: jsonData, encoding: .utf8) {
                print("📄 原始数据字符串:\n\(dataString)")
            }
        }
    }
}
