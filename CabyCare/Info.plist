<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleIconName</key>
	<string>AppIcon</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>api.cabycare</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>api.cabycare</string>
			</array>
		</dict>
	</array>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>此应用需要使用蓝牙来连接和配置AbyBox设备</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>此应用需要使用蓝牙来连接和配置AbyBox设备</string>
	<key>UIAppFonts</key>
	<array>
		<string>Chillax-Regular.otf</string>
		<string>Chillax-Extralight.otf</string>
		<string>Chillax-Light.otf</string>
		<string>Chillax-Medium.otf</string>
		<string>Chillax-Semibold.otf</string>
		<string>Chillax-Bold.otf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
		<string>bluetooth-central</string>
		<string>fetch</string>
	</array>
</dict>
</plist>
