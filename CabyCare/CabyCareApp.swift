//
//  CabyCareApp.swift
//  CabyCareApp
//
//  Created by Homalozoa X on 1/24/25.
//

import SwiftUI

// 实现应用代理，支持方向控制
class AppDelegate: NSObject, UIApplicationDelegate, UISceneDelegate {
    var orientationLock: UIInterfaceOrientationMask = .portrait
    
    func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]? = nil
    ) -> <PERSON><PERSON> {
        // 清除用户认证信息，便于测试（可以在生产环境移除）
        if CommandLine.arguments.contains("--reset-auth") {
            UserDefaultsManager.shared.clearAuthCredentials()
        }

        // 应用启动日志
        Log.info("📱 应用启动")

        // 登录状态日志
        if AuthManager.shared.isAuthenticated {
            Log.info("🔐 应用启动时用户已登录")
            // 打印用户数据
            UserDefaultsManager.shared.printUserData()
        } else {
            Log.info("🔒 应用启动时用户未登录")
        }

        // 请求通知权限
        Task {
            await NotificationManager.shared.requestAuthorization()
        }
        
        // 初始化为竖屏
        OrientationManager.shared.lockPortrait()
        
        return true
    }
    
    // 支持屏幕方向控制
    func application(_ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {
        return OrientationManager.shared.orientation
    }

    func application(
        _ application: UIApplication,
        didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data
    ) {
        NotificationManager.shared.handleDeviceToken(deviceToken)
    }

    func application(
        _ application: UIApplication,
        didFailToRegisterForRemoteNotificationsWithError error: Error
    ) {
        NotificationManager.shared.handleRegistrationError(error)
    }
    
    // 添加清除凭证的简单方法
    private func clearAuthCredentials() {
        let keys = ["access_token", "refresh_token", "user_id", "logto_id", "id_token", "auth_state"]
        keys.forEach { UserDefaults.standard.removeObject(forKey: $0) }
        Log.info("🧹 已清除登录凭证，可以进行新的登录测试")
    }

    // AppDelegate中的打印用户数据方法
    private func printUserData() {
        let userData: [String: Any?] = [
            "access_token": AuthManager.shared.accessToken,
            "refresh_token": AuthManager.shared.refreshToken,
            "user_id": UserDefaults.standard.string(forKey: "user_id"),
            "logto_id": UserDefaults.standard.string(forKey: "logto_id"),
            "id_token": UserDefaults.standard.string(forKey: "id_token")
        ]

        do {
            try JSONSerialization.data(withJSONObject: userData, options: .prettyPrinted)
            // if let jsonString = String(data: jsonData, encoding: .utf8) {
            //     Log.info("🧑‍💻 当前用户数据: \(jsonString)")
            // }
        } catch {
            Log.error("无法序列化用户数据: \(error.localizedDescription)")
        }
    }
}

@main
struct CabyCareApp: App {
    @UIApplicationDelegateAdaptor private var appDelegate: AppDelegate
    @StateObject private var authManager = AuthManager.shared
    @StateObject private var tokenRefreshManager = TokenRefreshManager.shared
    @State private var showLoginAlert = false
    @State private var isLaunching = true // 添加启动状态
    @State private var authExpirationTask: Task<Void, Never>? // 添加认证过期验证任务
    @Environment(\.scenePhase) private var scenePhase // 监听应用生命周期

    var body: some Scene {
        WindowGroup {
            Group {
                if isLaunching {
                    LaunchScreen {
                        withAnimation {
                            isLaunching = false
                        }
                    }
                } else if authManager.isAuthenticated {
                    ContentView()
                        .alert("认证已过期", isPresented: $showLoginAlert) {
                            Button("重新登录") {
                                // 清除过期的认证数据并回到登录界面
                                authManager.logout()
                            }
                            Button("取消", role: .cancel) { }
                        } message: {
                            Text("您的登录凭证已过期，请重新登录以继续使用。")
                        }
                        .task {
                            // 用户认证后触发VideoPlayerViewModel数据初始化
                            await initializeVideoPlayerData()
                        }
                        .onReceive(NotificationCenter.default.publisher(for: .authenticationExpired)) { notification in
                            // 监听认证过期通知 - 处理静默重试完成后的通知
                            let reason = notification.userInfo?["reason"] as? String ?? "未知原因"
                            let userFriendlyReason = notification.userInfo?["userFriendlyReason"] as? String ?? "会话已过期，请重新登录"
                            let silentRetryCompleted = notification.userInfo?["silentRetryCompleted"] as? Bool ?? false
                            let retryCount = notification.userInfo?["retryCount"] as? Int ?? 0
                            
                            Log.warning("⚠️ 收到认证过期通知")
                            Log.warning("   - 技术原因: \(reason)")
                            Log.warning("   - 用户友好原因: \(userFriendlyReason)")
                            Log.warning("   - 静默重试已完成: \(silentRetryCompleted)")
                            Log.warning("   - 重试次数: \(retryCount)")
                            
                            handleAuthenticationExpired(
                                reason: reason,
                                userFriendlyReason: userFriendlyReason,
                                silentRetryCompleted: silentRetryCompleted
                            )
                        }
                        .onReceive(NotificationCenter.default.publisher(for: .authenticationStatusChanged)) { _ in
                            // 监听用户登录状态变化 - 登录成功后清除设备缓存
                            if authManager.isAuthenticated {
                                Log.info("✅ 用户登录成功，清除设备缓存以确保显示最新数据")
                                Task {
                                    await clearDeviceCacheOnLogin()
                                }
                            }
                        }
                } else {
                    LoginView()
                }
            }
            .task {
                // 应用启动时的初始化任务
                await performInitialSetup()
            }
            .onChange(of: scenePhase) { oldPhase, newPhase in
                // 监听应用生命周期变化
                handleScenePhaseChange(from: oldPhase, to: newPhase)
            }
            .onDisappear {
                // 清理认证过期验证任务
                authExpirationTask?.cancel()
                authExpirationTask = nil
            }
        }
    }
    
    // MARK: - 认证过期处理
    
    /// 处理认证过期通知 - 优化的静默重试处理
    private func handleAuthenticationExpired(
        reason: String,
        userFriendlyReason: String,
        silentRetryCompleted: Bool
    ) {
        // 取消之前的验证任务
        authExpirationTask?.cancel()
        
        // 如果静默重试已完成，直接处理认证过期
        if silentRetryCompleted {
            Log.info("🔄 静默重试已完成，直接处理认证过期")
            Log.info("🔍 技术原因: \(reason)")
            Log.info("🔍 用户友好原因: \(userFriendlyReason)")
            
            // 创建即时处理任务
            authExpirationTask = Task { @MainActor in
                // 再次确认认证状态
                let isStillAuthenticated = authManager.isAuthenticated
                let hasValidToken = authManager.accessToken != nil && !authManager.accessToken!.isEmpty
                
                Log.info("🔍 最终认证状态确认:")
                Log.info("  - 认证状态: \(isStillAuthenticated)")
                Log.info("  - 有效访问令牌: \(hasValidToken)")
                
                // 如果确实需要重新登录，显示友好的错误信息
                if !isStillAuthenticated && !hasValidToken {
                    Log.warning("⚠️ 确认认证已过期，显示重新登录弹窗")
                    Log.warning("⚠️ 显示给用户的原因: \(userFriendlyReason)")
                    
                    // 更新弹窗信息以显示用户友好的错误原因
                    // self.authExpiredMessage = userFriendlyReason // This line was removed from the new_code, so it's removed here.
                    self.showLoginAlert = true
                } else {
                    Log.info("✅ 认证状态正常，不显示过期弹窗")
                }
            }
        } else {
            // 如果静默重试未完成，使用传统的延迟验证方式
            Log.info("🔄 静默重试未完成，使用延迟验证方式")
            self.handleAuthenticationExpiredLegacy(reason: reason)
        }
    }
    
    /// 传统的延迟验证方式（保留向后兼容）
    private func handleAuthenticationExpiredLegacy(reason: String) {
        // 创建新的验证任务，延迟2秒后再检查认证状态
        authExpirationTask = Task { @MainActor in
            Log.info("🕐 延迟2秒后验证认证状态，以防令牌刷新正在进行...")
            Log.info("🔍 认证过期原因: \(reason)")
            
            // 延迟2秒，给令牌刷新足够的时间完成
            try? await Task.sleep(nanoseconds: 2_000_000_000)
            
            // 检查任务是否被取消
            guard !Task.isCancelled else {
                Log.debug("🚫 认证过期验证任务被取消")
                return
            }
            
            // 重新检查认证状态
            let isStillAuthenticated = authManager.isAuthenticated
            let hasValidToken = authManager.accessToken != nil && !authManager.accessToken!.isEmpty
            let hasRefreshToken = authManager.hasRefreshToken
            
            Log.info("🔍 延迟验证结果:")
            Log.info("  - 认证状态: \(isStillAuthenticated)")
            Log.info("  - 有效访问令牌: \(hasValidToken)")
            Log.info("  - 有刷新令牌: \(hasRefreshToken)")
            Log.info("  - 原始失败原因: \(reason)")
            
            // 只有在确实无法恢复认证状态时才显示弹窗
            if !isStillAuthenticated && !hasValidToken {
                if hasRefreshToken {
                    Log.info("🔄 检测到有刷新令牌但认证失败，尝试最后一次令牌刷新...")
                    
                    // 尝试最后一次令牌刷新
                    do {
                        _ = try await authManager.refreshToken()
                        Log.info("✅ 最后一次令牌刷新成功，不显示过期弹窗")
                        return
                    } catch {
                        Log.error("❌ 最后一次令牌刷新也失败: \(error.localizedDescription)")
                    }
                }
                
                Log.warning("⚠️ 确认认证已过期，显示重新登录弹窗")
                Log.warning("⚠️ 最终失败原因: \(reason)")
                showLoginAlert = true
            } else {
                Log.info("✅ 认证状态正常，不显示过期弹窗")
            }
        }
    }
    
    // MARK: - 初始化和设置
    
    private func performInitialSetup() async {
        Log.info("📱 应用启动 - 开始初始化设置")
        
        // 🔄 注释掉应用启动时的缓存清理，让设备管理器可以显示缓存的设备
        // 只在用户登录时清理缓存，确保用户登录后显示最新数据
        // await clearDeviceCacheOnStartup()
        
        // 每次显示应用时打印认证状态
        if AuthManager.shared.isAuthenticated {
            Log.info("🔐 用户已登录")
            // 打印用户信息
            UserDefaultsManager.shared.printUserData()

            // 应用启动时进行一次令牌检查和刷新（使用TokenRefreshManager）
            await tokenRefreshManager.performStartupTokenCheck()
        } else {
            Log.info("🔒 用户未登录")
        }
    }
    
    // MARK: - 设备缓存清理
    
    /// 应用启动时清除设备缓存
    private func clearDeviceCacheOnStartup() async {
        Log.info("🧹 应用启动 - 开始清除设备缓存")
        
        // 清除UserDefaults中所有可能的设备相关缓存
        await MainActor.run {
            let keysToRemove = [
                "DeviceStatusCache",
                "DeviceCacheLastUpdate", 
                "cached_devices",
                "device_cache_expiry",
                "last_device_refresh",
                "cached_cats",
                "cats_cache_expiry",
                "hidden_cats_cache",
                "hidden_cats_cache_expiry",
                "device_list_cache",
                "sensor_status_cache",
                "video_segments_cache",
                "device_dates_cache"
            ]
            
            for key in keysToRemove {
                UserDefaults.standard.removeObject(forKey: key)
            }
            
            UserDefaults.standard.synchronize()
            Log.info("✅ UserDefaults中的设备缓存已清除")
        }
        
        // 清除DeviceManager的缓存
        await MainActor.run {
            let deviceManager = DeviceManager()
            deviceManager.clearCache()
            Log.info("✅ DeviceManager缓存已清除")
        }
        
        // 清除通用缓存管理器中的缓存
        do {
            try await CacheManager.shared.clearAllCache()
            Log.info("✅ CacheManager缓存已清除")
        } catch {
            Log.error("❌ 清除CacheManager缓存失败: \(error.localizedDescription)")
        }
        
        Log.info("✅ 应用启动设备缓存清理完成")
    }
    
    /// 用户登录成功后清除设备缓存
    private func clearDeviceCacheOnLogin() async {
        Log.info("🔑 用户登录成功 - 开始清除设备缓存以确保显示最新数据")
        
        // 清除UserDefaults中所有可能的设备相关缓存
        await MainActor.run {
            let keysToRemove = [
                "DeviceStatusCache",
                "DeviceCacheLastUpdate", 
                "cached_devices",
                "device_cache_expiry",
                "last_device_refresh",
                "device_list_cache",
                "sensor_status_cache",
                "video_segments_cache",
                "device_dates_cache"
            ]
            
            for key in keysToRemove {
                UserDefaults.standard.removeObject(forKey: key)
            }
            
            UserDefaults.standard.synchronize()
            Log.info("✅ 登录后UserDefaults中的设备缓存已清除")
        }
        
        // 清除DeviceManager的缓存
        await MainActor.run {
            let deviceManager = DeviceManager()
            deviceManager.clearCache()
            Log.info("✅ 登录后DeviceManager缓存已清除")
        }
        
        Log.info("✅ 用户登录后设备缓存清理完成")
    }

    // MARK: - 生命周期管理
    
    private func handleScenePhaseChange(from oldPhase: ScenePhase, to newPhase: ScenePhase) {
        switch newPhase {
        case .active:
            Log.info("📱 应用进入前台")
            if authManager.isAuthenticated {
                // 应用进入前台时的完整数据刷新流程
                Task {
                    // 1. 首先刷新认证token状态
                    await tokenRefreshManager.handleAppBecomeActive()
                    
                    // 2. 刷新关键数据以确保显示最新信息
                    await refreshAppDataOnForeground()
                }
            }
        case .inactive:
            Log.debug("📱 应用变为非活跃状态")
        case .background:
            Log.info("📱 应用进入后台")
            // 应用进入后台时取消认证过期验证任务
            authExpirationTask?.cancel()
            authExpirationTask = nil
        @unknown default:
            Log.debug("📱 应用状态变化: \(newPhase)")
        }
    }
    
    // MARK: - 前台数据刷新
    
    /// 应用进入前台时刷新关键数据
    private func refreshAppDataOnForeground() async {
        Log.info("🔄 应用进入前台 - 开始刷新关键数据")
        
        // 创建并发任务组来同时执行多个刷新操作
        await withTaskGroup(of: Void.self) { group in
            // 刷新VideoPlayerViewModel的最新数据 (影响HomeView和CareView)
            group.addTask {
                await VideoPlayerViewModel.shared.refreshLatestData()
                Log.info("✅ VideoPlayerViewModel数据刷新完成")
            }
            
            // 刷新猫咪数据 (影响HomeView的头像和基本信息)
            group.addTask {
                await CatManager.shared.refreshAllCats()
                Log.info("✅ 猫咪数据刷新完成")
            }
            
            // 等待所有任务完成
            await group.waitForAll()
        }
        
        // 发送数据刷新完成通知，让相关视图更新
        await MainActor.run {
            NotificationCenter.default.post(name: .dataRefreshedOnForeground, object: nil)
        }
        
        Log.info("✅ 应用前台数据刷新完成")
    }

    // MARK: - VideoPlayerViewModel 数据初始化触发
    
    /// 触发VideoPlayerViewModel数据初始化的方法（不创建新实例）
    private func initializeVideoPlayerData() async {
        // 确保用户已认证
        guard authManager.isAuthenticated else {
            Log.warning("⚠️ 用户未认证，跳过VideoPlayerViewModel数据初始化")
            return
        }
        
        Log.info("🎬 触发VideoPlayerViewModel数据初始化")
        
        // 只通过通知触发VideoPlayerViewModel的数据初始化，不创建新实例
        // ContentView中的VideoPlayerViewModel实例会响应这个通知
        await MainActor.run {
            NotificationCenter.default.post(name: .authenticationStatusChanged, object: nil)
        }
    }
}

// 添加NotificationCenter扩展
extension Notification.Name {
    static let authorizationFailed = Notification.Name("AuthorizationFailedNotification")
    static let authenticationExpired = Notification.Name("AuthenticationExpiredNotification")
}
