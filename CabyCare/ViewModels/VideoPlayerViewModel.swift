import Foundation
import AVKit
import AVFoundation
import SwiftUI  // Add SwiftUI import for withAnimation
import os  // 添加 os 导入来支持 OSAllocatedUnfairLock

@MainActor
final class VideoPlayerViewModel: NSObject, ObservableObject {
    // 添加共享单例
    static let shared = VideoPlayerViewModel()
    
    // 设备管理器 - 负责管理设备及其相关视频段
    private let deviceManager = DeviceManager()
    
    // 猫咪管理器 - 负责管理猫咪数据，用于显示猫咪信息
    private let catManager = CatManager.shared
    
    @Published var segments: [VideoSegment] = []
    @Published var currentSegment: VideoSegment?
    @Published var isLoading = false
    @Published var error: String?
    @Published var availableDates: [Date] = []  // 添加可用日期列表
    @Published var dailyStats: DailyStats?

    @Published var isLoadingSegment: String? // 当前正在加载的视频ID

    @Published var isPlaying: Bool = false
    @Published var failedSegments: Set<String> = [] // 追踪加载失败的视频

    @Published var isRefreshing = false
    @Published var lastRefreshTime: Date?
    @Published var shouldShowRefreshHint = false

    @Published var volume: Double = 1.0

    // 添加进度相关的属性
    @Published var currentTime: Double = 0
    @Published var duration: Double = 0
    @Published var isSeeking: Bool = false

    // 在 VideoPlayerViewModel 中添加新的加载状态属性
    @Published var loadingStates: Set<LoadingState> = []

    // 添加用户交互状态追踪
    private var hasUserInteraction = false

    // Add properties for fullscreen and orientation support
    @Published var isFullscreen: Bool = false
    @Published var isLandscape: Bool = false

    // 添加缓存相关属性
    private var cachedSegments: [VideoSegment] = []
    private var lastFullLoadTime: Date?
    private let cacheExpirationInterval: TimeInterval = 300 // 5分钟缓存过期

    // 添加猫咪数据缓存
    @Published var catsCache: [String: CatProfile] = [:]  // animal_id -> CatProfile 映射

    // 添加任务管理，防止重复刷新
    private var currentRefreshTask: Task<Void, Never>?
    private var currentLoadTask: Task<Void, Never>?
    
    // 添加刷新锁，防止重复操作
    private var refreshLock = false
    
    // 请求去重机制
    private var activeRequests: Set<String> = []
    private var requestResults: [String: [VideoSegment]] = [:]
    
    // 添加初始数据加载状态跟踪
    @Published var hasInitialDataLoaded = false
    @Published var isInitialLoading = false

    enum LoadingState: Hashable {
        case initial
        case videoList
        case videoSegment(String)
        case refresh
        case catData  // 添加猫咪数据加载状态
    }

    struct CatDailyStat: Identifiable {
        let id = UUID()
        let cat: CatType
        let excretionCount: Int
        let totalDuration: TimeInterval
    }

    struct DailyStats {
        let date: Date
        let videoCount: Int
        let totalDuration: TimeInterval
        let segments: [VideoSegment]
        let catStats: [CatDailyStat] // 添加猫咪统计数组
    }

    @Published var selectedDate: Date? {
        didSet {
            if let date = selectedDate {
                // 如果是用户手动选择日期，标记已有交互
                if oldValue != nil {
                    hasUserInteraction = true
                }
                Task { @MainActor in
                    // 智能加载：先检查缓存，再决定是否需要API调用
                    await loadVideoDataForDate(date)
                    updateDailyStats(for: date)
                }
            }
        }
    }

    let player = AVPlayer()
    private var timeObserver: Any?
    private let loadingQueue = DispatchQueue(label: "com.aby.videoLoading", qos: .userInitiated)
    private var loadingTask: Task<Void, Never>?

    private var playerTimeObserver: Any?
    private var playerItemObserver: NSKeyValueObservation?

    // 添加新的观察者属性
    private var statusObserver: NSKeyValueObservation?
    private var itemObserver: NSKeyValueObservation?

    // 添加新的属性
    private let cache = VideoDataCache.shared
    private var backgroundUpdateTimer: Timer?
    private let backgroundUpdateInterval: TimeInterval = 300 // 5分钟更新一次

    // 添加一个弱引用来跟踪添加时间观察者的player实例
    private weak var timeObserverPlayer: AVPlayer?

    enum VideoLoadMode {
        case availableDates  // 加载可用日期列表
        case dailyVideos     // 加载指定日期的视频
    }

    override init() {
        super.init()
        setupPlayer()
        setupAudioSession()
        setupPlayerObservers()
        setupOrientationObservers() // Add orientation observers

        // 添加认证状态观察
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAuthenticationChanged),
            name: .authenticationStatusChanged,
            object: nil
        )

        // 添加授权失败观察
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAuthorizationFailed),
            name: .authorizationFailed,
            object: nil
        )

        // 添加前后台切换通知观察
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAppDidBecomeActive),
            name: UIApplication.didBecomeActiveNotification,
            object: nil
        )

        // 添加授权过期观察
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAuthenticationExpired),
            name: .authenticationExpired,
            object: nil
        )

        // 添加用户登出观察
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleUserLogout),
            name: .userLoggedOut,
            object: nil
        )

        // 添加设备缓存清除通知观察
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleDeviceCacheCleared),
            name: .deviceCacheCleared,
            object: nil
        )

        // 不在初始化时立即加载数据，而是等待明确调用 ensureInitialDataLoaded()
        Log.info("📱 VideoPlayerViewModel 初始化完成，等待明确调用初始数据加载")
    }

    private func setupPlayer() {
        // 配置播放器基本属性
        player.automaticallyWaitsToMinimizeStalling = true
        player.allowsExternalPlayback = true

        // 添加播放状态通知观察
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handlePlaybackEnd),
            name: .AVPlayerItemDidPlayToEndTime,
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handlePlaybackStalled),
            name: .AVPlayerItemPlaybackStalled,
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleFailedToPlayToEndTime),
            name: .AVPlayerItemFailedToPlayToEndTime,
            object: nil
        )

        // 添加时间观察
        setupTimeObserver()
    }

    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()

            // 使用与 LivePlayerView 相同的音频会话配置
            try audioSession.setCategory(
                .playback,
                mode: .moviePlayback,
                options: [.allowAirPlay]
            )

            // 配置音频参数
            try audioSession.setPreferredIOBufferDuration(0.005)
            try audioSession.setPreferredSampleRate(44100)

            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
        } catch {
            print("❌ Failed to set audio session: \(error)")
        }
    }

    private func setupPlayerObservers() {
        // 观察播放器状态
        statusObserver = player.observe(\.status, options: [.new]) { [weak self] player, _ in
            Task { @MainActor in
                self?.handlePlayerStatusChange(player)
            }
        }

        // 观察播放项状态
        itemObserver = player.currentItem?.observe(\.status, options: [.new]) { [weak self] item, _ in
            Task { @MainActor in
                self?.handlePlayerItemStatusChange(item)
            }
        }

        // 添加播放完成通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handlePlaybackEnded),
            name: .AVPlayerItemDidPlayToEndTime,
            object: player.currentItem
        )

        // 添加播放进度观察
        itemObserver = player.currentItem?.observe(\.duration) { [weak self] item, _ in
            guard let self = self else { return }
            Task { @MainActor in
                if item.duration.isValid {
                    self.duration = item.duration.seconds
                }
            }
        }
    }

    // Add setup for orientation observers
    private func setupOrientationObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleOrientationChange),
            name: UIDevice.orientationDidChangeNotification,
            object: nil
        )
    }

    private func updatePlayingState() {
        Task { @MainActor in
            isPlaying = player.timeControlStatus == .playing
        }
    }

    @MainActor
    func togglePlayback(_ segment: VideoSegment) {
        if segment.id == currentSegment?.id {
            if player.timeControlStatus == .playing {
                player.pause()
                isPlaying = false
            } else {
                // 如果是播放结束状态，需要重新加载视频
                if player.currentItem?.currentTime() == player.currentItem?.duration {
                    playVideo(segment)
                } else {
                    player.play()
                    isPlaying = true
                }
            }
        } else {
            playVideo(segment)
        }
    }

    @MainActor
    func playVideo(_ segment: VideoSegment) {
        setLoading(.videoSegment(segment.id), isLoading: true)

        // 重置状态
        currentTime = 0
        duration = 0
        isSeeking = false

        currentSegment = segment
        isLoadingSegment = segment.id

        guard let url = URL(string: segment.url) else {
            Log.error("❌ Invalid URL for segment: \(segment.id)")
            error = "Invalid video URL"
            return
        }

        // 创建播放项，添加必要的配置
        let asset = AVURLAsset(url: url, options: [
            AVURLAssetPreferPreciseDurationAndTimingKey: true
        ])

        // 设置资源加载代理
        asset.resourceLoader.setDelegate(self, queue: .main)

        let playerItem = AVPlayerItem(asset: asset)

        // 观察播放项状态
        statusObserver?.invalidate()
        statusObserver = playerItem.observe(\.status) { [weak self] item, _ in
            guard let self = self else { return }
            Task { @MainActor in
                switch item.status {
                case .readyToPlay:
                    self.isLoadingSegment = nil
                    self.failedSegments.remove(segment.id)
                    if self.player.rate == 0 {
                        self.player.playImmediately(atRate: 1.0)
                    }
                    self.duration = item.duration.seconds
                    self.isPlaying = true
                    Log.debug("🎬 Playing segment: \(segment.id)")
                case .failed:
                    Log.error("❌ Player item failed: \(segment.id)")
                    if let error = item.error {
                        Log.error("Error details: \(error.localizedDescription)")
                    }
                    self.isLoadingSegment = nil
                    self.failedSegments.insert(segment.id)
                    self.isPlaying = false
                case .unknown:
                    break
                @unknown default:
                    break
                }
            }
        }

        // 清理旧的通知观察者
        NotificationCenter.default.removeObserver(self, name: .AVPlayerItemDidPlayToEndTime, object: player.currentItem)
        NotificationCenter.default.removeObserver(self, name: .AVPlayerItemPlaybackStalled, object: player.currentItem)
        NotificationCenter.default.removeObserver(self, name: .AVPlayerItemFailedToPlayToEndTime, object: player.currentItem)

        // 添加新的通知观察
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handlePlaybackEnd),
            name: .AVPlayerItemDidPlayToEndTime,
            object: playerItem
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handlePlaybackStalled),
            name: .AVPlayerItemPlaybackStalled,
            object: playerItem
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleFailedToPlayToEndTime),
            name: .AVPlayerItemFailedToPlayToEndTime,
            object: playerItem
        )

        // 替换当前播放项
        player.replaceCurrentItem(with: playerItem)

        // 在播放开始后移除加载状态
        setLoading(.videoSegment(segment.id), isLoading: false)
    }

    @objc private func handlePlaybackEnded(_ notification: Notification) {
        Task { @MainActor in
            isPlaying = false
        }
    }

    override func observeValue(forKeyPath keyPath: String?,
                             of object: Any?,
                             change: [NSKeyValueChangeKey : Any]?,
                             context: UnsafeMutableRawPointer?) {

        if let playerItem = object as? AVPlayerItem, keyPath == #keyPath(AVPlayerItem.status) {
            Task { @MainActor in
                switch playerItem.status {
                case .readyToPlay:
                    Log.debug("Player item ready to play")
                    // 如果播放器已准备就绪但尚未开始播放，则开始播放
                    if player.rate == 0 {
                        player.playImmediately(atRate: 1.0)
                    }
                case .failed:
                    if let error = playerItem.error {
                        Log.error("Player item failed: \(error.localizedDescription)")
                        self.error = "Playback failed: \(error.localizedDescription)"
                    }
                case .unknown:
                    Log.debug("Player item status unknown")
                @unknown default:
                    Log.debug("Player item status: unknown default case")
                }
            }
        }
    }

    nonisolated private func log(_ message: String) {
        Task { @MainActor in
            let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
            print("[\(timestamp)] VideoPlayerViewModel: \(message)")
        }
    }

    // 主动清理资源的方法，应该在视图消失时调用
    func cleanupResources() {
        Task { @MainActor in
            // 停止播放器
            player.pause()
            player.replaceCurrentItem(with: nil)
            
            // 清理KVO观察者
            statusObserver?.invalidate()
            itemObserver?.invalidate()
            playerItemObserver?.invalidate()
            
            // 安全地移除时间观察者
            removeTimeObserverSafely()
            
            // 清理定时器
            backgroundUpdateTimer?.invalidate()
            backgroundUpdateTimer = nil
            
            // 取消加载任务
            loadingTask?.cancel()
            loadingTask = nil
            
            print("VideoPlayerViewModel resources cleaned up")
        }
    }
    
    deinit {
        // 取消所有正在进行的任务
        currentRefreshTask?.cancel()
        currentLoadTask?.cancel()
        
        // 移除通知观察者（这是安全的）
        NotificationCenter.default.removeObserver(self)
        
        print("🧹 VideoPlayerViewModel 已清理完成")
    }

    // 设备时区属性 - 从 DeviceManager 获取
    private var deviceTimezone: String {
        return deviceManager.getDeviceTimezone()
    }
    
    // 基于设备时区的日历对象
    private var deviceCalendar: Calendar {
        return deviceManager.deviceCalendar()
    }

    @MainActor
    func loadVideos(mode: VideoLoadMode, targetDate: Date? = nil) async {
        // 在异步上下文中进行更严格的认证检查
        let isAuthenticated = AuthManager.shared.isAuthenticated
        guard isAuthenticated else {
            Log.info("ℹ️ 用户未认证，跳过视频数据加载")
            return
        }
        
        let accessToken = AuthManager.shared.accessToken
        guard let accessToken = accessToken, !accessToken.isEmpty else {
            Log.info("ℹ️ 访问令牌无效，跳过视频数据加载")
            return
        }

        guard let userId = UserDefaultsManager.shared.userId else {
            Log.warning("⚠️ 未找到用户ID，跳过视频数据加载")
            return
        }

        // 防止重复加载
        guard !isLoading else {
            Log.info("📱 VideoPlayerViewModel: 已在加载中，跳过重复请求")
            return
        }

        // 取消当前的加载任务
        currentLoadTask?.cancel()

        isLoading = true
        error = nil

        // 创建新的加载任务
        currentLoadTask = Task { @MainActor in
            defer {
                isLoading = false
                currentLoadTask = nil
            }
            
            do {
                // 检查任务是否被取消
                try Task.checkCancellation()
                
                // 1. 加载设备（只在设备列表为空时加载）
                if deviceManager.devices.isEmpty {
                    let devices = try await deviceManager.loadDevices(for: userId)
                    Log.info("📱 VideoPlayerViewModel loadVideos: 成功加载设备列表，设备数量=\(devices.count)")
                    
                    // 详细打印每个设备信息
                    for device in devices {
                        Log.info("📱 VideoPlayerViewModel loadVideos设备详情: ID=\(device.deviceId), 名称=\(device.name), 时区=\(device.timezone)")
                    }
                }
                
                // 检查任务是否被取消
                try Task.checkCancellation()
                
                // 确保有设备可用
                guard !deviceManager.devices.isEmpty else {
                    throw NSError(domain: "VideoPlayer", code: -1, userInfo: [NSLocalizedDescriptionKey: "No devices found"])
                }
                
                // 2. 根据模式加载视频段
                switch mode {
                case .availableDates:
                    try await loadAvailableDates()
                case .dailyVideos:
                    guard let date = targetDate else {
                        throw NSError(domain: "VideoPlayer", code: -1, userInfo: [NSLocalizedDescriptionKey: "No target date provided"])
                    }
                    try await loadDailyVideos(for: date)
                }
                
                // 更新缓存时间
                lastRefreshTime = Date()

            } catch is CancellationError {
                Log.info("ℹ️ 加载任务被取消")
            } catch {
                // 检查是否是网络请求被取消的错误
                if let networkError = error as? NetworkError, networkError == .requestCancelled {
                    Log.warning("⚠️ 网络请求被取消，可能是由于重复加载操作")
                } else if let networkError = error as? NetworkError {
                    switch networkError {
                    case .unauthorized:
                        NotificationCenter.default.post(name: .authorizationFailed, object: nil)
                        self.error = nil
                    default:
                        self.error = nil
                        Log.error("Network error: \(error.localizedDescription)")
                    }
                } else {
                    self.error = nil
                    Log.error("Error: \(error.localizedDescription)")
                }
            }
        }
        
        // 等待任务完成
        await currentLoadTask?.value
    }
    
    // 提取加载可用日期的逻辑
    @MainActor
    private func loadAvailableDates() async throws {
        // 加载过去一年的所有视频 - 遍历所有设备
        let calendar = Calendar.current
        let now = Date()
        guard let pastDate = calendar.date(byAdding: .year, value: -1, to: calendar.startOfDay(for: now)) else {
            throw NSError(domain: "VideoPlayer", code: -1, userInfo: [NSLocalizedDescriptionKey: "Date calculation error"])
        }
        
        let startDate = pastDate
        let endDate = calendar.date(byAdding: .day, value: 1, to: calendar.startOfDay(for: now))!
        
        // 收集所有设备的日期集合
        var allAvailableDates: Set<Date> = []
        var allSegments: [VideoSegment] = []
        
        // 遍历所有设备加载视频段
        for device in deviceManager.devices {
            Log.info("📱 VideoPlayerViewModel: 正在加载设备 \(device.name) (\(device.deviceId)) 的视频数据")
            
            do {
                // 检查任务是否被取消
                try Task.checkCancellation()
                
                // 加载设备的所有视频段
                _ = try await deviceManager.loadSegments(for: device, startDate: startDate, endDate: endDate)
                
                // 获取该设备的可用日期和片段
                let deviceDates = deviceManager.getAvailableDates(for: device.deviceId)
                let deviceSegments = deviceManager.getSegments(for: device.deviceId)
                
                Log.info("📱 VideoPlayerViewModel: 设备 \(device.name) 有 \(deviceDates.count) 个可用日期，\(deviceSegments.count) 个视频片段")
                
                // 验证设备名称是否正确设置
                if let firstSegment = deviceSegments.first {
                    Log.info("📱 VideoPlayerViewModel: 第一个视频片段的设备名称: \(firstSegment.deviceName ?? "未设置")")
                }
                
                // 添加到总集合中
                allAvailableDates.formUnion(deviceDates)
                allSegments.append(contentsOf: deviceSegments)
            } catch {
                Log.error("❌ 设备 \(device.name) 加载视频失败: \(error.localizedDescription)")
                // 继续处理其他设备，不中断整个流程
                continue
            }
        }
        
        // 更新VM的可用日期和片段（按日期降序排列）
        self.availableDates = Array(allAvailableDates).sorted(by: >)
        self.segments = allSegments
        
        Log.info("📱 VideoPlayerViewModel: 总共收集到 \(self.availableDates.count) 个可用日期，\(self.segments.count) 个视频片段")
        
        // 调试：打印每个segment的设备名称
        for (index, segment) in self.segments.prefix(5).enumerated() {
            Log.info("📱 VideoPlayerViewModel: 片段[\(index)] 设备名称=\(segment.deviceName ?? "未设置"), 开始时间=\(segment.start)")
        }
        
        // 只在没有用户交互且没有选中日期时，自动选择最新日期
        if !hasUserInteraction && selectedDate == nil {
            if let latestDate = availableDates.first {
                self.selectedDate = latestDate
            }
        }
    }
    
    // 提取加载日视频的逻辑
    @MainActor
    private func loadDailyVideos(for date: Date) async throws {
        // 添加调试信息，显示实际传递的日期
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        dateFormatter.timeZone = TimeZone.current
        Log.info("📅 VideoPlayerViewModel: 请求加载日期 \(dateFormatter.string(from: date)) (本地时间)")
        Log.info("📅 VideoPlayerViewModel: 目标日期原始值 \(date)")
        
        // 收集该日期所有设备的视频段
        var allDailySegments: [VideoSegment] = []
        
        // 遍历所有设备获取特定日期的视频段
        for device in deviceManager.devices {
            do {
                // 检查任务是否被取消
                try Task.checkCancellation()
                
                let dailySegments = deviceManager.getDailySegments(for: device, date: date)
                
                // 如果该日期没有视频，尝试通过加载整天的数据来获取
                if dailySegments.isEmpty {
                    let deviceCalendar = deviceManager.deviceCalendar(for: device.deviceId)
                    let startDate = deviceCalendar.startOfDay(for: date)
                    let endDate = deviceCalendar.date(byAdding: .day, value: 1, to: startDate)!
                    
                    _ = try await deviceManager.loadSegments(for: device, startDate: startDate, endDate: endDate)
                    
                    // 重新获取该日期的视频段
                    let refreshedSegments = deviceManager.getDailySegments(for: device, date: date)
                    allDailySegments.append(contentsOf: refreshedSegments)
                    
                    Log.info("📱 VideoPlayerViewModel: 设备 \(device.name) 重新加载后获得 \(refreshedSegments.count) 个视频片段")
                } else {
                    allDailySegments.append(contentsOf: dailySegments)
                    Log.info("📱 VideoPlayerViewModel: 设备 \(device.name) 直接获得 \(dailySegments.count) 个视频片段")
                }
            } catch {
                Log.error("❌ 设备 \(device.name) 加载日期 \(date) 的视频失败: \(error.localizedDescription)")
                // 继续处理其他设备，不中断整个流程
                continue
            }
        }
        
        // 按时间排序并处理片段，确保每个片段都有唯一的 index
        allDailySegments.sort { $0.start > $1.start }
        self.segments = allDailySegments.enumerated().map { index, segment in
            var updatedSegment = segment
            updatedSegment.index = index
            return updatedSegment
        }
        
        Log.info("📱 VideoPlayerViewModel: 日期 \(date) 共找到 \(self.segments.count) 个视频片段")
        
        // 调试：打印每个segment的设备名称
        for (index, segment) in self.segments.enumerated() {
            Log.info("📱 VideoPlayerViewModel: 片段[\(index)] 设备名称=\(segment.deviceName ?? "未设置"), 开始时间=\(segment.start)")
        }
        
        // 更新每日统计
        updateDailyStats(for: date)
    }
    
    /// 智能加载指定日期的视频数据
    /// 优先使用缓存，只在必要时才发起 API 请求
    @MainActor
    private func loadVideoDataForDate(_ date: Date) async {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)
        
        Log.info("📅 VideoPlayerViewModel: 开始智能加载日期 \(dateString) 的数据")
        
        // 检查是否有设备数据
        if deviceManager.devices.isEmpty {
            Log.info("📅 VideoPlayerViewModel: 设备列表为空，尝试加载设备数据")
            // 如果没有设备数据，需要先加载设备
            await loadVideos(mode: .availableDates)
            return
        }
        
        // 收集所有设备在该日期的缓存数据
        var allCachedSegments: [VideoSegment] = []
        var devicesMissingData: [Device] = []
        
        // 检查每个设备是否有该日期的缓存数据
        for device in deviceManager.devices {
            // 优先使用DeviceManager的智能缓存检查
            if deviceManager.hasDataInCache(for: device.deviceId, date: date) {
                let cachedSegments = deviceManager.getDailySegments(for: device, date: date)
                allCachedSegments.append(contentsOf: cachedSegments)
                Log.info("📅 VideoPlayerViewModel: 设备 \(device.name) 有 \(cachedSegments.count) 个缓存片段")
            } else {
                // 检查该日期是否在可用日期列表中
                let deviceAvailableDates = deviceManager.getAvailableDates(for: device.deviceId)
                let calendar = deviceManager.deviceCalendar(for: device.deviceId)
                let hasDataForDate = deviceAvailableDates.contains { calendar.isDate($0, inSameDayAs: date) }
                
                if hasDataForDate {
                    // 有数据但未缓存，需要加载
                    devicesMissingData.append(device)
                    Log.info("📅 VideoPlayerViewModel: 设备 \(device.name) 在日期 \(dateString) 有数据但未缓存")
                } else {
                    // 检查是否是今天或者近期日期，可能需要刷新
                    let daysDifference = calendar.dateComponents([.day], from: date, to: Date()).day ?? 0
                    if daysDifference <= 7 { // 一周内的数据，可能需要刷新
                        devicesMissingData.append(device)
                        Log.info("📅 VideoPlayerViewModel: 设备 \(device.name) 在近期日期 \(dateString) 可能有新数据，需要检查")
                    } else {
                        Log.info("📅 VideoPlayerViewModel: 设备 \(device.name) 在历史日期 \(dateString) 确认没有数据")
                    }
                }
            }
        }
        
        // 如果有设备缺少数据，只为这些设备发起 API 请求
        if !devicesMissingData.isEmpty {
            Log.info("📅 VideoPlayerViewModel: 需要为 \(devicesMissingData.count) 个设备加载数据")
            
            for device in devicesMissingData {
                do {
                    let calendar = deviceManager.deviceCalendar(for: device.deviceId)
                    let startDate = calendar.startOfDay(for: date)
                    let endDate = calendar.date(byAdding: .day, value: 1, to: startDate)!
                    
                    let newSegments = try await deviceManager.loadSegments(for: device, startDate: startDate, endDate: endDate)
                    
                    // 筛选出该日期的数据
                    let dailySegments = newSegments.filter { segment in
                        segment.start >= startDate && segment.start < endDate
                    }
                    
                    allCachedSegments.append(contentsOf: dailySegments)
                    Log.info("📅 VideoPlayerViewModel: 设备 \(device.name) 新加载 \(dailySegments.count) 个片段")
                    
                } catch {
                    Log.error("❌ VideoPlayerViewModel: 设备 \(device.name) 加载失败: \(error.localizedDescription)")
                    
                    // 检查是否是网络错误，如果是历史日期的网络错误，不要显示错误
                    let calendar = deviceManager.deviceCalendar(for: device.deviceId)
                    let daysDifference = calendar.dateComponents([.day], from: date, to: Date()).day ?? 0
                    
                    if daysDifference > 7 {
                        // 历史日期的网络错误，静默处理
                        Log.info("ℹ️ 历史日期 \(dateString) 的网络错误已忽略，使用现有缓存数据")
                    } else {
                        // 近期日期的错误，可能需要用户关注
                        Log.warning("⚠️ 近期日期 \(dateString) 的数据加载失败: \(error.localizedDescription)")
                    }
                }
            }
        } else {
            Log.info("📅 VideoPlayerViewModel: 所有数据都在缓存中，无需 API 请求")
        }
        
        // 更新视图数据
        allCachedSegments.sort { $0.start > $1.start }
        self.segments = allCachedSegments.enumerated().map { index, segment in
            var updatedSegment = segment
            updatedSegment.index = index
            return updatedSegment
        }
        
        Log.info("📅 VideoPlayerViewModel: 日期 \(dateString) 最终获得 \(self.segments.count) 个视频片段")
    }

    private func fetchUserDevices(userId: String) async throws -> [Device] {
        // 在异步上下文中检查认证状态
        let isAuthenticated = await MainActor.run { AuthManager.shared.isAuthenticated }
        guard isAuthenticated else {
            Log.info("ℹ️ 用户未认证，跳过设备列表获取")
            throw NetworkError.unauthorized
        }
        
        // 在异步上下文中获取访问令牌
        let accessToken = await MainActor.run { AuthManager.shared.accessToken }
        guard let accessToken = accessToken, !accessToken.isEmpty else {
            Log.info("ℹ️ 访问令牌无效，跳过设备列表获取")
            throw NetworkError.unauthorized
        }

        let url = URL(string: Configuration.API.Device.getAccessibleDevicesPath(userId: userId))!
        
        // 使用新的可访问设备 API
        let response: AccessibleDevicesResponse = try await NetworkManager.shared.request(url)
        
        // 打印获取到的设备数据
        Log.info("📱 VideoPlayerViewModel获取可访问设备成功: 状态=\(response.status), 消息=\(response.message), 设备数量=\(response.data.count)")
        
        // 详细打印每个设备信息
        for device in response.data {
            Log.info("📱 VideoPlayerViewModel设备详情: ID=\(device.deviceId), 名称=\(device.name), 型号=\(device.model), 状态=\(device.status)")
        }
        
        // 将 AccessibleDevice 转换为 Device
        return response.data.map { accessibleDevice in
            Device(
                deviceId: accessibleDevice.deviceId,
                userId: accessibleDevice.userId,
                hardwareSn: accessibleDevice.hardwareSn,
                name: accessibleDevice.name,
                model: accessibleDevice.model,
                timezone: accessibleDevice.timezone,
                firmwareVersion: accessibleDevice.firmwareVersion,
                status: accessibleDevice.status,
                lastHeartbeat: parseDate(accessibleDevice.lastHeartbeat),
                lastActive: parseDate(accessibleDevice.lastActive),
                createdAt: parseDate(accessibleDevice.createdAt) ?? Date(),
                updatedAt: parseDate(accessibleDevice.updatedAt) ?? Date()
            )
        }
    }
    
    /// 解析日期字符串
    private func parseDate(_ dateString: String?) -> Date? {
        guard let dateString = dateString else { return nil }
        return DateFormatterUtil.date(from: dateString, format: .iso8601)
    }

    // 添加获取设备时区的方法
    func getDeviceTimezone() -> String {
        return deviceTimezone
    }

    private func updateDailyStats(for date: Date? = nil) {
        guard let date = date else {
            dailyStats = nil
            return
        }

        // 使用设备时区而不是UTC
        var calendar = Calendar.current
        calendar.timeZone = TimeZone(identifier: deviceTimezone) ?? .current

        // 获取选定日期的开始和结束时间
        let startOfDay = calendar.startOfDay(for: date)
        let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)!

        // 使用更严格的日期范围过滤
        let dailySegments = segments.filter { segment in
            let segmentDate = segment.start
            return segmentDate >= startOfDay && segmentDate < endOfDay
        }

        // 按时间排序
        let sortedSegments = dailySegments.sorted { $0.start > $1.start }

        // 计算每只猫的统计数据
        var catStatsDict: [CatType: (count: Int, duration: TimeInterval)] = [:]

        for segment in sortedSegments {
            let cat = TestDataGenerator.randomCat(forId: segment.id)
            let behavior = TestDataGenerator.randomBehavior(forId: segment.id)

            if behavior == .excretion {
                var current = catStatsDict[cat] ?? (count: 0, duration: 0)
                current.count += 1
                current.duration += segment.duration
                catStatsDict[cat] = current
            }
        }

        // 转换为 CatDailyStat 数组
        let catStats = catStatsDict.map { cat, stats in
            CatDailyStat(
                cat: cat,
                excretionCount: stats.count,
                totalDuration: stats.duration
            )
        }.sorted { $0.excretionCount > $1.excretionCount }

        // 更新统计数据
        dailyStats = DailyStats(
            date: date,
            videoCount: sortedSegments.count,
            totalDuration: sortedSegments.reduce(0) { $0 + $1.duration },
            segments: sortedSegments,
            catStats: catStats
        )
    }

    private func getNextSegment(after segment: VideoSegment) -> VideoSegment? {
        guard let currentIndex = segments.firstIndex(where: { $0.id == segment.id }) else {
            Log.error("❌ Cannot find current segment in segments array")
            return nil
        }

        let nextIndex = segments.index(after: currentIndex)
        guard nextIndex < segments.count else {
            return nil
        }

        return segments[nextIndex]
    }

    @objc private func handleAppDidBecomeActive() {
        Task { @MainActor [weak self] in
            guard let self = self else { return }

            // 如果距离上次刷新超过5分钟，或者当前选中的是今天的日期，则刷新
            if let lastRefresh = lastRefreshTime,
               Date().timeIntervalSince(lastRefresh) > 300 ||
               (selectedDate.map { Calendar.current.isDateInToday($0) } ?? false) {
                await refreshVideoList()
            }
        }
    }

    @MainActor
    func refreshVideoList() async {
        guard !isRefreshing else { return }

        isRefreshing = true
        defer {
            isRefreshing = false
            lastRefreshTime = Date()
        }

        do {
            // 记住当前选中的日期
            let currentSelectedDate = selectedDate

            // 加载新数据
            await loadVideos(mode: .availableDates)

            // 如果有用户交互，保持当前选中的日期
            if hasUserInteraction, let date = currentSelectedDate {
                await loadVideos(mode: .dailyVideos, targetDate: date)
                updateDailyStats(for: date)
            }

            // 更新缓存
            await cache.updateCache(
                dates: availableDates,
                segments: segments,
                lastSelectedDate: selectedDate
            )

            shouldShowRefreshHint = false
        }
    }

    /// 刷新并自动选择最新日期的数据
    @MainActor
    func refreshLatestData() async {
        // 更强的防重复调用检查
        guard !isRefreshing && !refreshLock else { 
            Log.warning("⚠️ 刷新操作已在进行中，跳过重复请求")
            return 
        }
        
        // 取消当前的刷新任务
        currentRefreshTask?.cancel()
        
        // 设置锁和状态
        refreshLock = true
        isRefreshing = true
        
        // 创建新的刷新任务
        currentRefreshTask = Task { @MainActor in
            defer {
                refreshLock = false
                isRefreshing = false
                lastRefreshTime = Date()
                currentRefreshTask = nil
            }
            
            Log.info("🔄 开始刷新最新数据...")
            
            do {
                // 检查任务是否被取消
                try Task.checkCancellation()
                
                // 首先加载最新的可用日期列表
                await loadVideos(mode: .availableDates)
                
                // 再次检查任务是否被取消
                try Task.checkCancellation()
                
                // 自动选择最新的可用日期
                if let latestDate = availableDates.max() {
                    Log.info("📅 自动选择最新日期: \(Configuration.DateFormat.formatISO8601(latestDate))")
                    
                    // 更新选中日期，但不触发额外的加载（因为我们马上就要手动加载）
                    selectedDate = latestDate
                    
                    // 检查任务是否被取消
                    try Task.checkCancellation()
                    
                    // 加载最新日期的视频数据
                    await loadVideos(mode: .dailyVideos, targetDate: latestDate)
                    updateDailyStats(for: latestDate)
                    
                    Log.info("✅ 最新数据刷新完成，共加载 \(segments.count) 个视频片段")
                    Log.info("📅 可用日期数量: \(availableDates.count)")
                    
                    // 添加调试日志，显示前几个可用日期
                    let formatter = DateFormatter()
                    formatter.dateFormat = "yyyy-MM-dd"
                    for (index, date) in availableDates.prefix(5).enumerated() {
                        Log.info("📅 可用日期[\(index)]: \(formatter.string(from: date))")
                    }
                } else {
                    Log.info("⚠️ 没有可用的日期数据")
                }
                
                // 检查任务是否被取消
                try Task.checkCancellation()
                
                // 更新缓存
                await cache.updateCache(
                    dates: availableDates,
                    segments: segments,
                    lastSelectedDate: selectedDate
                )
                
                shouldShowRefreshHint = false
                
            } catch is CancellationError {
                Log.info("ℹ️ 刷新任务被取消")
            } catch {
                // 检查是否是网络请求被取消的错误
                if let networkError = error as? NetworkError, networkError == .requestCancelled {
                    Log.warning("⚠️ 网络请求被取消，可能是由于重复刷新操作")
                } else {
                    Log.error("❌ 刷新最新数据失败: \(error.localizedDescription)")
                    self.error = "刷新失败: \(error.localizedDescription)"
                }
            }
        }
        
        // 等待任务完成
        await currentRefreshTask?.value
    }

    /// 强制刷新当前选中日期的数据
    @MainActor
    func refreshCurrentDateData() async {
        guard !isRefreshing else { return }
        guard let currentDate = selectedDate else { return }
        
        isRefreshing = true
        defer {
            isRefreshing = false
            lastRefreshTime = Date()
        }
        
        Log.info("🔄 刷新当前日期数据: \(Configuration.DateFormat.formatISO8601(currentDate))")
        
        // 重新加载当前日期的视频数据
        await loadVideos(mode: .dailyVideos, targetDate: currentDate)
        updateDailyStats(for: currentDate)
        
        Log.info("✅ 当前日期数据刷新完成，共加载 \(segments.count) 个视频片段")
        
        shouldShowRefreshHint = false
    }

    @objc private func handlePlaybackEnd(_ notification: Notification) {
        Task { @MainActor in
            isPlaying = false
            currentTime = duration  // 确保进度条显示在结束位置

            // 重置播放器到开始位置，但不自动播放
            player.seek(to: .zero)
            player.pause()
        }
    }

    @objc private func handlePlaybackStalled(_ notification: Notification) {
        let currentSegment = self.currentSegment
        let player = self.player

        Task { @MainActor in
            if let playerItem = player.currentItem,
               !playerItem.isPlaybackLikelyToKeepUp {
                player.pause()

                if let segment = currentSegment {
                    try? await Task.sleep(nanoseconds: 2_000_000_000)
                    playVideo(segment)
                }
            }
        }
    }

    @objc private func handleFailedToPlayToEndTime(_ notification: Notification) {
        // Capture values before sending to main actor
        if let error = notification.userInfo?[AVPlayerItemFailedToPlayToEndTimeErrorKey] as? Error {
            let errorMessage = "Failed to complete playback: \(error.localizedDescription)"
            let currentSegment = self.currentSegment
            
            Task { @MainActor [weak self] in
                guard let self = self else { return }
                Log.error("Playback error: \(error.localizedDescription)")
                self.error = errorMessage

                if let segment = currentSegment {
                    self.playVideo(segment)
                }
            }
        }
    }

    // 添加播放器状态处理方法
    @MainActor
    private func handlePlayerStatusChange(_ player: AVPlayer) {
        switch player.status {
        case .failed:
            print("❌ Player failed: \(String(describing: player.error))")
            if let error = player.error as NSError? {
                print("Error domain: \(error.domain)")
                print("Error code: \(error.code)")
                print("Error description: \(error.localizedDescription)")
                print("Error user info: \(error.userInfo)")
            }
            handlePlaybackError(player.error)
        case .readyToPlay:
            isLoading = false
            player.play()
        case .unknown:
            print("⚠️ Player status unknown")
        @unknown default:
            print("⚠️ Player status: unknown default case")
        }
    }

    // 添加播放项状态处理方法
    @MainActor
    private func handlePlayerItemStatusChange(_ item: AVPlayerItem) {
        switch item.status {
        case .failed:
            print("❌ PlayerItem failed: \(String(describing: item.error))")
            if let error = item.error as NSError? {
                print("Error domain: \(error.domain)")
                print("Error code: \(error.code)")
                print("Error description: \(error.localizedDescription)")
                if let errorLog = item.errorLog() {
                    print("Error log: \(errorLog)")
                    errorLog.events.forEach { event in
                        print("Event: \(event.errorComment ?? "No comment")")
                        print("Error status: \(event.errorStatusCode)")
                    }
                }
            }
            handlePlaybackError(item.error)
        case .readyToPlay:
            isLoading = false
            player.play()
        case .unknown:
            print("⚠️ PlayerItem status unknown")
        @unknown default:
            print("⚠️ PlayerItem status: unknown default case")
        }
    }

    @MainActor
    private func handlePlaybackError(_ error: Error?) {
        let errorMessage = error?.localizedDescription ?? "Unknown playback error"
        print("❌ Playback error: \(errorMessage)")
        self.error = errorMessage
        self.isLoading = false
        self.isPlaying = false

        // 标记当前片段为失败
        if let currentSegment = currentSegment {
            failedSegments.insert(currentSegment.id)
        }
    }

    @MainActor
    func stopPlayback() {
        print("⏹️ Stopping playback")

        // 取消当前加载任务
        loadingTask?.cancel()

        // 停止播放
        player.pause()

        // 安全地移除时间观察者
        removeTimeObserverSafely()

        // 清理播放项观察者
        playerItemObserver?.invalidate()
        playerItemObserver = nil

        // 移除当前播放项
        player.replaceCurrentItem(with: nil)

        // 重置状态
        currentSegment = nil
        error = nil
        isLoading = false
        isPlaying = false
        isLoadingSegment = nil
    }

    private func setupPlayerItemObservers(_ playerItem: AVPlayerItem) {
        // 清理旧的观察者
        itemObserver?.invalidate()

        // 观察播放项状态
        itemObserver = playerItem.observe(\.status, options: [.new]) { [weak self] item, _ in
            Task { @MainActor in
                self?.handlePlayerItemStatusChange(item)
            }
        }

        // 添加播放完成通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handlePlaybackEnded),
            name: .AVPlayerItemDidPlayToEndTime,
            object: playerItem
        )

        // 添加播放失败通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleFailedToPlayToEndTime),
            name: .AVPlayerItemFailedToPlayToEndTime,
            object: playerItem
        )

        // 添加播放卡顿通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handlePlaybackStalled),
            name: .AVPlayerItemPlaybackStalled,
            object: playerItem
        )

        // 配置播放项
        playerItem.preferredForwardBufferDuration = 5
        playerItem.canUseNetworkResourcesForLiveStreamingWhilePaused = true
    }

    // 修改缓存加载方法
    @MainActor
    private func loadCachedData() async {
        if let cachedData = await cache.loadCache() {
            self.availableDates = cachedData.dates
            self.segments = cachedData.segments

            // 只在没有用户交互时处理缓存的选中日期
            if !hasUserInteraction {
                if let lastSelectedDate = cachedData.lastSelectedDate {
                    self.selectedDate = lastSelectedDate
                    updateDailyStats(for: lastSelectedDate)
                } else if let latestDate = availableDates.first {
                    self.selectedDate = latestDate
                    updateDailyStats(for: latestDate)
                }
            }
        }
    }

    // 添加后台更新定时器设置
    private func setupBackgroundUpdate() {
        backgroundUpdateTimer?.invalidate()
        backgroundUpdateTimer = Timer.scheduledTimer(
            withTimeInterval: backgroundUpdateInterval,
            repeats: true
        ) { [weak self] _ in
            Task { @MainActor [weak self] in
                guard let self = self else { return }

                // 如果选中的是当天，则刷新视频列表
                if let selectedDate = self.selectedDate,
                   Calendar.current.isDateInToday(selectedDate) {
                    await self.refreshVideoList()
                } else {
                    // 检查是否有新的可用日期
                    await self.loadVideos(mode: .availableDates)
                }
            }
        }
    }

    // 添加进度控制方法
    func seek(to time: Double) {
        isSeeking = true
        let cmTime = CMTime(seconds: time, preferredTimescale: CMTimeScale(NSEC_PER_SEC))

        // 使用更精确的定位方法
        player.seek(to: cmTime, toleranceBefore: .zero, toleranceAfter: .zero) { [weak self] finished in
            guard let self = self else { return }
            if finished {
                Task { @MainActor in
                    self.currentTime = time
                    self.isSeeking = false
                    // 如果播放器是暂停状态，恢复播放
                    if self.player.timeControlStatus == .paused {
                        self.player.play()
                    }
                }
            }
        }
    }

    // 在需要配置请求头的地方直接使用 NetworkManager 的方法
    @MainActor
    private func setupRequest(_ url: URL) -> URLRequest {
        var request = URLRequest(url: url)
        let token = AuthManager.shared.accessToken
        NetworkManager.shared.configureRequestHeaders(&request, contentType: .video, authToken: token)
        return request
    }

    // 修改加载状态管理方法
    @MainActor
    private func setLoading(_ state: LoadingState, isLoading: Bool) {
        if isLoading {
            loadingStates.insert(state)
        } else {
            loadingStates.remove(state)
        }
    }

    @MainActor
    func handleSegmentAppear(_ segment: VideoSegment) async {
        // 在这里处理视频段出现时的逻辑
        // 由于这是在 Task 中执行的，可以安全地更新状态
    }

    // 修改初始化数据方法
    @MainActor
    private func initializeData() async {
        guard AuthManager.shared.isAuthenticated else {
            Log.warning("⚠️ 尝试初始化数据但用户未认证")
            return
        }

        // 重置用户交互状态
        hasUserInteraction = false

        // 1. 并行预加载猫咪数据和缓存数据
        async let catDataTask: Void = preloadCatData()
        async let cachedDataTask: Void = loadCachedData()
        
        _ = await catDataTask
        _ = await cachedDataTask

        // 2. 在后台静默更新数据
        Task {
            await refreshVideoList()
        }

        // 3. 设置定时更新
        setupBackgroundUpdate()
    }
    
    /// 确保初始数据已加载（只在首次启动时执行）
    @MainActor
    func ensureInitialDataLoaded() async {
        // 如果已经加载过或正在加载，直接返回
        guard !hasInitialDataLoaded && !isInitialLoading else {
            Log.info("📱 VideoPlayerViewModel: 初始数据已加载或正在加载中，跳过重复请求")
            return
        }
        
        // 检查认证状态
        guard AuthManager.shared.isAuthenticated else {
            Log.info("ℹ️ 用户未认证，跳过初始数据加载")
            return
        }
        
        isInitialLoading = true
        
        Log.info("📱 VideoPlayerViewModel: 开始首次初始数据加载...")
        
        do {
            // 1. 加载设备数据
            if let userId = UserDefaultsManager.shared.userId {
                _ = try await deviceManager.loadDevices(for: userId)
                Log.info("📱 VideoPlayerViewModel: 设备数据加载完成，设备数量: \(deviceManager.devices.count)")
            }
            
            // 2. 预加载猫咪数据
            await preloadCatData()
            Log.info("📱 VideoPlayerViewModel: 猫咪数据预加载完成")
            
            // 3. 加载可用日期和最新数据
            await loadVideos(mode: .availableDates)
            Log.info("📱 VideoPlayerViewModel: 初始视频数据加载完成，可用日期: \(availableDates.count)")
            
            // 4. 预加载近期历史数据用于HomeView统计
            await preloadRecentHistoryForStatistics()
            Log.info("📱 VideoPlayerViewModel: 近期历史数据预加载完成")
            
            // 5. 设置定时更新
            setupBackgroundUpdate()
            
            hasInitialDataLoaded = true
            Log.info("✅ VideoPlayerViewModel: 首次初始数据加载完成")
            
        } catch {
            Log.error("❌ VideoPlayerViewModel: 初始数据加载失败: \(error.localizedDescription)")
            // 即使失败也标记为已尝试，避免重复尝试
            hasInitialDataLoaded = true
        }
        
        isInitialLoading = false
    }
    
    /// 获取缓存的统计数据（不触发API调用）
    @MainActor
    func getCachedDataForStatistics() -> (segments: [VideoSegment], devices: [Device], catsCache: [String: CatProfile]) {
        return (
            segments: segments,
            devices: deviceManager.devices,
            catsCache: catsCache
        )
    }
    
    /// 预加载近期历史数据用于HomeView统计
    @MainActor
    private func preloadRecentHistoryForStatistics() async {
        guard deviceManager.devices.count > 0 else {
            Log.info("📱 VideoPlayerViewModel: 没有设备，跳过历史数据预加载")
            return
        }
        
        let now = Date()
        let twoDaysAgo = Calendar.current.date(byAdding: .hour, value: -48, to: now) ?? now
        
        Log.info("📱 VideoPlayerViewModel: 开始预加载近48小时的历史数据用于统计...")
        
        var allHistorySegments: [VideoSegment] = []
        
        // 从所有设备加载近期历史数据
        for device in deviceManager.devices {
            do {
                let deviceSegments = try await deviceManager.loadSegments(
                    for: device,
                    startDate: twoDaysAgo,
                    endDate: now
                )
                allHistorySegments.append(contentsOf: deviceSegments)
                Log.info("📱 VideoPlayerViewModel: 从设备 \(device.name) 加载了 \(deviceSegments.count) 个历史视频段")
            } catch {
                Log.error("❌ VideoPlayerViewModel: 从设备 \(device.name) 加载历史数据失败: \(error.localizedDescription)")
                // 继续尝试其他设备，不要因为一个设备失败就停止
            }
        }
        
        // 将历史数据合并到现有segments中，避免重复
        let existingIds = Set(segments.map { $0.id })
        let newSegments = allHistorySegments.filter { !existingIds.contains($0.id) }
        
        segments.append(contentsOf: newSegments)
        segments.sort { $0.start > $1.start } // 按时间倒序排列
        
        Log.info("📱 VideoPlayerViewModel: 历史数据预加载完成，新增 \(newSegments.count) 个视频段，总共 \(segments.count) 个")
        
        // 详细日志：显示预加载的数据统计
        let uniqueAnimalIds = Set(segments.compactMap { $0.animal_id })
        let nilAnimalIdCount = segments.filter { $0.animal_id == nil }.count
        Log.info("📱 VideoPlayerViewModel: 预加载数据包含猫咪ID: \(uniqueAnimalIds)")
        Log.info("📱 VideoPlayerViewModel: 有 \(nilAnimalIdCount) 个视频段的animal_id为nil")
        
        // 检查前几个视频段的详细信息
        for (index, segment) in segments.prefix(3).enumerated() {
            Log.info("📱 VideoPlayerViewModel: 视频段[\(index)] animal_id=\(segment.animal_id ?? "nil"), 时间=\(segment.start.formattedString())")
        }
        
        if !segments.isEmpty {
            let timeRange = "\(segments.last?.start.formattedString() ?? "未知") - \(segments.first?.start.formattedString() ?? "未知")"
            Log.info("📱 VideoPlayerViewModel: 预加载数据时间范围: \(timeRange)")
        }
    }

    // 添加一个等待缓存清理完成的方法
    @MainActor
    private func clearCacheAndWait() async {
        // 创建一个任务组来等待所有清理操作完成
        await withTaskGroup(of: Void.self) { group in
            // 清除 URLSession 缓存
            URLCache.shared.removeAllCachedResponses()

            // 清除视频数据缓存
            group.addTask {
                await VideoDataCache.shared.clearCache()
                Log.info("✅ 视频清单缓存已清除")
            }

            // 清除视频缓存管理器中的缓存
            group.addTask {
                await withCheckedContinuation { continuation in
                    // VideoCacheManager.shared.clearCache {
                    //     continuation.resume()
                    // }
                    // 使用 CacheManager 替代
                    CacheManager.shared.cleanupIfNeeded()
                    continuation.resume()
                }
                Log.info("✅ 视频缓存管理器缓存已清除")
            }

            // 等待所有清理任务完成
            await group.waitForAll()
        }

        Log.info("✅ 所有缓存清理完成")
    }

    // 修改认证状态变化处理方法
    @objc private func handleAuthenticationChanged(_ notification: Notification) {
        Task { @MainActor in
            if AuthManager.shared.isAuthenticated {
                // 用户登录时，重置初始数据加载状态
                hasInitialDataLoaded = false
                isInitialLoading = false
                
                // 清除缓存并初始化数据
                clearCache()
                catsCache = [:]  // 清除猫咪缓存
                
                // 触发初始数据加载
                await ensureInitialDataLoaded()
                Log.info("✅ 用户登录，初始数据加载完成")
            } else {
                // 清理现有数据
                stopPlayback()
                segments = []
                availableDates = []
                selectedDate = Date() // 确保使用当前日期
                error = nil
                clearCache()
                catsCache = [:]  // 清除猫咪缓存
                hasInitialDataLoaded = false
                isInitialLoading = false
            }
        }
    }

    // 修改授权失败处理逻辑
    @objc private func handleAuthorizationFailed(_ notification: Notification) {
        Task { @MainActor in
            // 延迟检查认证状态，给令牌刷新一些时间
            try? await Task.sleep(nanoseconds: 3_000_000_000)  // 3秒延迟

            // 再次检查认证状态
            if !AuthManager.shared.isAuthenticated {
                // 如果仍未授权，则清理所有数据和缓存
                stopPlayback()
                segments = []
                availableDates = []
                selectedDate = Date() // 确保使用当前日期
                error = nil
                catsCache = [:]  // 清除猫咪缓存
                hasInitialDataLoaded = false
                isInitialLoading = false

                // 确保不显示任何错误消息
                error = nil

                // 清除所有缓存
                await clearCacheAndWait()
                Log.info("🧹 认证失败，已清除所有缓存")
            } else {
                // 授权已恢复，尝试重新加载数据
                error = nil
                if !hasInitialDataLoaded {
                    await ensureInitialDataLoaded()
                }
            }
        }
    }

    // 修改处理认证过期方法
    @objc private func handleAuthenticationExpired(_ notification: Notification) {
        Task { @MainActor in
            // 延迟检查认证状态，给令牌刷新一些时间
            try? await Task.sleep(nanoseconds: 3_000_000_000)  // 3秒延迟

            // 检查认证状态
            if AuthManager.shared.isAuthenticated {
                // 授权已恢复，清除错误
                error = nil
                // 确保初始数据已加载
                if !hasInitialDataLoaded {
                    await ensureInitialDataLoaded()
                }
                // 尝试重新加载或继续当前操作
                if let currentSegment = currentSegment {
                    // 尝试继续播放当前视频
                    playVideo(currentSegment)
                }
            } else {
                // 仍未授权，但不显示错误消息
                error = nil
                hasInitialDataLoaded = false
                isInitialLoading = false
            }
        }
    }

    @objc private func handleUserLogout(_ notification: Notification) {
        Task { @MainActor in
            // 停止播放
            stopPlayback()

            // 清除所有数据
            segments = []
            availableDates = []
            selectedDate = Date() // 确保使用当前日期
            error = nil
            clearCache()
            catsCache = [:]  // 清除猫咪缓存
            hasInitialDataLoaded = false
            isInitialLoading = false

            // 清除所有缓存
            await clearCacheAndWait()
            Log.info("✅ 用户登出，已清除所有缓存和数据")
        }
    }
    
    @objc private func handleDeviceCacheCleared(_ notification: Notification) {
        Task { @MainActor in
            Log.info("🧹 收到设备缓存清除通知，清理VideoPlayerViewModel中的设备相关缓存")
            
            // 清除DeviceManager的缓存
            deviceManager.clearCache()
            
            // 清除视频相关的缓存数据
            clearCache()
            
            // 如果用户已认证并且有初始数据，重置状态以便重新加载
            if AuthManager.shared.isAuthenticated && hasInitialDataLoaded {
                Log.info("🔄 重置初始数据加载状态，以便获取最新的设备数据")
                hasInitialDataLoaded = false
                
                // 触发重新加载最新数据
                await ensureInitialDataLoaded()
            }
            
            Log.info("✅ VideoPlayerViewModel设备缓存清理完成")
        }
    }

    private func clearCache() {
        cachedSegments = []
        lastFullLoadTime = nil
    }

    // 添加重置用户交互状态的方法
    func resetUserInteraction() {
        hasUserInteraction = false
    }

    // Handle device orientation changes
    @objc private func handleOrientationChange() {
        Task { @MainActor in
            // Only proceed if a video is currently loaded and playing.
            guard self.isPlaying, self.currentSegment != nil else { return }

            let deviceOrientation = UIDevice.current.orientation
            
            // Ensure the orientation is a valid interface orientation (portrait or landscape).
            // Ignore flat, faceUp, faceDown, unknown.
            guard deviceOrientation.isPortrait || deviceOrientation.isLandscape else { return }

            // Determine the target fullscreen state: true if landscape, false if portrait.
            let targetFullscreenState = deviceOrientation.isLandscape

            // Only call setFullscreen if the current state is different from the target state.
            if self.isFullscreen != targetFullscreenState {
                self.setFullscreen(targetFullscreenState)
            }
        }
    }

    // Toggle fullscreen mode
    @MainActor
    func toggleFullscreen() {
        self.setFullscreen(!self.isFullscreen)
    }

    // Set fullscreen mode directly
    @MainActor
    func setFullscreen(_ fullscreen: Bool) {
        if self.isFullscreen == fullscreen { // Already in the desired state
            if fullscreen { // Re-asserting fullscreen state
                if self.isLandscape {
                    OrientationManager.shared.lockLandscape()
                } else {
                    OrientationManager.shared.lockPortrait()
                }
            }
            return
        }

        if fullscreen { // ---- Entering Fullscreen ----
            let currentDeviceOrientation = UIDevice.current.orientation
            if currentDeviceOrientation.isLandscape {
                self.isLandscape = true
                OrientationManager.shared.lockLandscape()
            } else { // Device is Portrait or other. For fullscreen triggered by gesture (already landscape) this path isn't taken.
                     // If triggered by button while in portrait, it will enter fullscreen in portrait.
                self.isLandscape = false
                OrientationManager.shared.lockPortrait()
            }
            
            Task {
                try? await Task.sleep(nanoseconds: 100_000_000) // 150ms, slightly increased delay
                if self.isFullscreen != true { 
                    withAnimation {
                        self.isFullscreen = true
                    }
                }
            }

        } else { // ---- Exiting Fullscreen ----
            self.isLandscape = false 
            OrientationManager.shared.lockPortrait()
            if self.isFullscreen != false { 
                withAnimation {
                    self.isFullscreen = false
                }
            }
        }
    }

    private func setupTimeObserver() {
        removeTimeObserverSafely()
        
        let interval = CMTime(seconds: 0.5, preferredTimescale: CMTimeScale(NSEC_PER_SEC))
        
        timeObserver = player.addPeriodicTimeObserver(forInterval: interval, queue: .main) { [weak self] time in
            Task { @MainActor in
                guard let self = self, !self.isSeeking else { return }
                self.currentTime = time.seconds
            }
        }
        
        // 记录添加时间观察者的player实例
        timeObserverPlayer = player
    }

    private func removeTimeObserverSafely() {
        if let observer = timeObserver {
            // 确保从添加它的同一个player实例中移除
            if let observerPlayer = timeObserverPlayer {
                observerPlayer.removeTimeObserver(observer)
            } else {
                // 如果没有记录原始player，使用当前player
                player.removeTimeObserver(observer)
            }
            timeObserver = nil
            timeObserverPlayer = nil
        }
    }

    // MARK: - 猫咪数据管理方法
    
    /// 预加载猫咪数据，建立 animal_id 到猫咪信息的映射
    @MainActor
    func preloadCatData() async {
        guard AuthManager.shared.isAuthenticated else {
            Log.info("ℹ️ 用户未认证，跳过猫咪数据预加载")
            return
        }
        
        setLoading(.catData, isLoading: true)
        
        // 使用 CatManager 加载所有猫咪数据（包括正常和隐藏的）
        await catManager.refreshAllCats()
        
        // 构建 animal_id -> CatProfile 映射
        var newCatsCache: [String: CatProfile] = [:]
        
        // 添加正常状态的猫咪
        for cat in catManager.cats {
            newCatsCache[cat.id] = cat
        }
        
        // 添加隐藏状态的猫咪
        for cat in catManager.hiddenCats {
            newCatsCache[cat.id] = cat
        }
        
        self.catsCache = newCatsCache
        
        Log.info("🐱 成功预加载 \(newCatsCache.count) 只猫咪的数据")
        
        setLoading(.catData, isLoading: false)
    }
    
    /// 根据 animal_id 获取猫咪信息
    func getCatInfo(for animalId: String?) -> CatProfile? {
        guard let animalId = animalId else { return nil }
        return catsCache[animalId]
    }
    
    /// 获取猫咪名称，如果找不到则返回默认名称
    func getCatName(for animalId: String?) -> String {
        guard let animalId = animalId,
              let cat = catsCache[animalId] else {
            return NSLocalizedString("video_cat_unknown", value: "未知猫咪", comment: "")
        }
        return cat.name
    }
    
    /// 获取猫咪头像URL，如果找不到则返回nil
    func getCatAvatarUrl(for animalId: String?) -> String? {
        guard let animalId = animalId,
              let cat = catsCache[animalId] else {
            return nil
        }
        return cat.avatarUrl
    }
    
    // MARK: - 为HomeView提供的数据访问方法
    
    /// 获取指定时间范围内特定猫咪的如厕记录
    /// 这个方法会智能地使用缓存数据或从API获取数据
    @MainActor
    func getToiletSegments(for catId: String, from startDate: Date, to endDate: Date) async -> [VideoSegment] {
        var allSegments: [VideoSegment] = []
        
        // 检查当前缓存的数据是否覆盖了需要的时间范围
        let cachedTimeRange = segments.map { $0.start }.sorted()
        let needsMoreData = cachedTimeRange.isEmpty || 
                           (cachedTimeRange.first! > startDate || cachedTimeRange.last! < endDate)
        
        Log.debug("🏠 VideoPlayerViewModel: 当前缓存有 \(segments.count) 个视频段")
        Log.debug("🏠 VideoPlayerViewModel: 缓存时间范围: \(cachedTimeRange.first?.formattedString() ?? "无") - \(cachedTimeRange.last?.formattedString() ?? "无")")
        Log.debug("🏠 VideoPlayerViewModel: 需要的时间范围: \(startDate.formattedString()) - \(endDate.formattedString())")
        Log.debug("🏠 VideoPlayerViewModel: 是否需要加载更多数据: \(needsMoreData)")
        
        if needsMoreData {
            Log.info("🏠 VideoPlayerViewModel: 需要加载更多历史数据来覆盖时间范围 \(startDate) 到 \(endDate)")
            
            // 创建请求标识符，避免重复请求
            let requestKey = "toiletSegments_\(catId)_\(startDate.timeIntervalSince1970)_\(endDate.timeIntervalSince1970)"
            
            // 检查是否有相同的请求正在进行
            if activeRequests.contains(requestKey) {
                Log.info("🏠 VideoPlayerViewModel: 检测到重复请求，等待现有请求完成...")
                // 等待现有请求完成，然后使用结果
                while activeRequests.contains(requestKey) {
                    try? await Task.sleep(nanoseconds: 100_000_000) // 等待100ms
                }
                if let cachedResult = requestResults[requestKey] {
                    Log.info("🏠 VideoPlayerViewModel: 使用重复请求的结果 \(cachedResult.count) 个视频段")
                    allSegments = cachedResult
                } else {
                    // 如果没有缓存结果，回退到现有数据
                    allSegments = segments
                    Log.warning("⚠️ VideoPlayerViewModel: 重复请求完成但无结果，使用现有缓存数据")
                }
            } else {
                // 标记请求正在进行
                activeRequests.insert(requestKey)
                
                var loadingSuccessful = false
                
                // 从所有设备获取指定时间范围的数据
                for device in deviceManager.devices {
                    do {
                        let deviceSegments = try await deviceManager.loadSegments(
                            for: device, 
                            startDate: startDate, 
                            endDate: endDate
                        )
                        allSegments.append(contentsOf: deviceSegments)
                        loadingSuccessful = true
                    } catch {
                        Log.error("❌ VideoPlayerViewModel: 获取设备 \(device.name) 的历史数据失败: \(error.localizedDescription)")
                        // 不要continue，而是记录错误并继续尝试其他设备
                    }
                }
                
                // 如果加载失败且有缓存数据，则回退到使用缓存数据
                if !loadingSuccessful && !segments.isEmpty {
                    Log.warning("⚠️ VideoPlayerViewModel: API请求失败，回退使用缓存的 \(segments.count) 个视频段")
                    allSegments = segments
                } else {
                    Log.info("🏠 VideoPlayerViewModel: 从API获取到 \(allSegments.count) 个历史视频段")
                }
                
                // 缓存结果并清理请求标记
                requestResults[requestKey] = allSegments
                activeRequests.remove(requestKey)
                
                // 定时清理缓存结果（5分钟后）
                Task {
                    try? await Task.sleep(nanoseconds: 300_000_000_000) // 5分钟
                    requestResults.removeValue(forKey: requestKey)
                }
            }
        } else {
            // 使用缓存的数据
            allSegments = segments
            Log.info("🏠 VideoPlayerViewModel: 使用缓存的 \(allSegments.count) 个视频段")
            Log.debug("🏠 VideoPlayerViewModel: 缓存中前5个视频段: \(allSegments.prefix(5).map { "ID:\($0.id), 时间:\($0.start.formattedString())" })")
        }
        
        // 过滤出属于指定猫咪且在指定时间范围内的如厕记录
        let filteredSegments = allSegments.filter { segment in
            // 检查时间范围
            let timeMatch = segment.start >= startDate && segment.start <= endDate
            
            // 检查猫咪ID是否匹配（主要匹配逻辑）
            let idMatch = segment.animal_id == catId
            
            // 临时解决方案：如果animal_id为nil，基于其他条件判断是否为如厕记录
            let isToiletRecord: Bool
            if segment.animal_id == nil {
                // 临时解决方案：如果没有animal_id，将有效的如厕记录分配给所有猫咪
                // 检查是否有有效的猫咪体重数据，这表明猫咪使用了设备
                let hasValidWeight = segment.weight_cat > 0.5 && segment.weight_cat < 20.0 // 合理的猫咪体重范围
                let hasValidDuration = segment.duration > 10 && segment.duration < 3600 // 合理的如厕时长（10秒-1小时）
                
                isToiletRecord = hasValidWeight && hasValidDuration
                
                if isToiletRecord && timeMatch {
                    Log.debug("🐱 VideoPlayerViewModel: [临时方案] 基于体重和时长识别如厕记录: 时间=\(segment.start.formattedString()), 体重=\(segment.weight_cat)kg, 时长=\(segment.duration)s")
                }
            } else {
                isToiletRecord = idMatch
                
                if idMatch && timeMatch {
                    Log.debug("🐱 VideoPlayerViewModel: 通过ID匹配找到猫咪 \(catId) 的视频段: \(segment.start.formattedString())")
                }
            }
            
            return isToiletRecord && timeMatch
        }
        
        Log.info("🐱 VideoPlayerViewModel: 猫咪 \(catId) 在指定时间范围内有 \(filteredSegments.count) 次如厕记录")
        
        // 详细日志：显示所有找到的记录
        if filteredSegments.isEmpty {
            Log.debug("🐱 VideoPlayerViewModel: 没有找到猫咪 \(catId) 的如厕记录")
            // 显示所有视频段的猫咪ID，帮助调试
            let uniqueAnimalIds = Set(allSegments.compactMap { $0.animal_id })
            let nilAnimalIdCount = allSegments.filter { $0.animal_id == nil }.count
            Log.debug("🐱 VideoPlayerViewModel: 缓存中所有猫咪ID: \(uniqueAnimalIds)")
            Log.debug("🐱 VideoPlayerViewModel: 有 \(nilAnimalIdCount) 个视频段的animal_id为nil")
            Log.debug("🐱 VideoPlayerViewModel: 目标猫咪ID: \(catId)")
            
            // 详细检查前几个视频段
            for (index, segment) in allSegments.prefix(5).enumerated() {
                Log.debug("🐱 VideoPlayerViewModel: 段[\(index)] animal_id='\(segment.animal_id ?? "nil")', 时间=\(segment.start.formattedString())")
            }
        } else {
            Log.info("🐱 VideoPlayerViewModel: 找到的记录时间: \(filteredSegments.prefix(3).map { $0.start.formattedString() })")
        }
        
        // 按时间倒序排序（最新的在前面）
        return filteredSegments.sorted { $0.start > $1.start }
    }
    
    /// 获取所有设备，供HomeView使用
    @MainActor
    func getDevices() -> [Device] {
        return deviceManager.devices
    }
    
    /// 确保设备数据已加载
    @MainActor
    func ensureDevicesLoaded() async {
        if deviceManager.devices.isEmpty {
            if let userId = UserDefaultsManager.shared.userId {
                do {
                    _ = try await deviceManager.loadDevices(for: userId)
                    Log.info("🏠 VideoPlayerViewModel: 加载了 \(deviceManager.devices.count) 个设备")
                } catch {
                    Log.error("❌ VideoPlayerViewModel: 加载设备失败: \(error.localizedDescription)")
                }
            }
        }
    }
}

extension UIDeviceOrientation {
    var isLandscape: Bool {
        return self == .landscapeLeft || self == .landscapeRight
    }
}

// 添加视频加载队列
private let videoLoadingQueue = DispatchQueue(label: "com.aby.videoLoading", qos: .userInitiated)

// 修改 ResourceLoaderDelegate - 确保所有方法都是 nonisolated 并使用NetworkManager
final class ResourceLoaderDelegate: NSObject, AVAssetResourceLoaderDelegate, Sendable {
    private let activeDataTask = OSAllocatedUnfairLock<URLSessionDataTask?>(initialState: nil)
    private let loadingQueue = DispatchQueue(label: "com.aby.resourceloader", qos: .userInitiated)
    private let isCancelled = OSAllocatedUnfairLock(initialState: false)
    private let isLoading = OSAllocatedUnfairLock(initialState: false)
    
    // 添加缺失的属性
    private nonisolated(unsafe) let fileManager = FileManager.default
    private let maxCacheSize: UInt64 = 500 * 1024 * 1024  // 500MB
    private let queue = DispatchQueue(label: "com.aby.videocache")
    private let activeDownloads = OSAllocatedUnfairLock<[String: URLSessionDownloadTask]>(initialState: [:])

    override init() {
        super.init()
    }

    func cancel() {
        isCancelled.withLock { $0 = true }
        activeDataTask.withLock { $0?.cancel() }
    }

    nonisolated func resourceLoader(_ resourceLoader: AVAssetResourceLoader,
                       shouldWaitForLoadingOfRequestedResource loadingRequest: AVAssetResourceLoadingRequest) -> Bool {

        // 如果已取消或正在加载，不处理新的请求
        let shouldSkip = isCancelled.withLock { $0 } || isLoading.withLock { $0 }
        guard !shouldSkip else {
            return false
        }

        isLoading.withLock { $0 = true }

        // 取消之前的请求
        activeDataTask.withLock { $0?.cancel() }

        guard let url = loadingRequest.request.url else {
            Task { @MainActor in
                loadingRequest.finishLoading(with: NSError(domain: "", code: -1, userInfo: nil))
                self.isLoading.withLock { $0 = false }
            }
            return false
        }

        // 使用NetworkManager处理资源请求
        Task { @MainActor in
            do {
                // 使用NetworkManager获取数据
                let data: Data = try await NetworkManager.shared.fetchData(url, contentType: .video)
                
                // 设置内容信息
                if let contentInfo = loadingRequest.contentInformationRequest {
                    contentInfo.contentType = "video/MP2T"
                    contentInfo.contentLength = Int64(data.count)
                    contentInfo.isByteRangeAccessSupported = true
                }

                // 响应数据
                loadingRequest.dataRequest?.respond(with: data)
                loadingRequest.finishLoading()
                
                self.isLoading.withLock { $0 = false }
                
            } catch {
                loadingRequest.finishLoading(with: error)
                self.isLoading.withLock { $0 = false }
            }
        }
        
        return true
    }

    func moveToCache(from tempURL: URL, to cacheURL: URL) async throws {
        try await withCheckedThrowingContinuation { continuation in
            queue.async {
                do {
                    if self.fileManager.fileExists(atPath: cacheURL.path) {
                        try self.fileManager.removeItem(at: cacheURL)
                    }
                    try self.fileManager.moveItem(at: tempURL, to: cacheURL)
                    continuation.resume()
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }

    private func cleanupCacheIfNeeded() {
        queue.async { [weak self] in
            guard let self = self,
                  let cacheDir = try? self.fileManager.url(
                    for: .cachesDirectory,
                    in: .userDomainMask,
                    appropriateFor: nil,
                    create: false
                  ) else { return }

            // 获取所有缓存文件
            guard let files = try? self.fileManager.contentsOfDirectory(
                at: cacheDir,
                includingPropertiesForKeys: [.fileSizeKey, .creationDateKey]
            ) else { return }

            // 计算总大小并按日期排序
            let sortedFiles = files.compactMap { url -> (URL, Date, UInt64)? in
                guard let resources = try? url.resourceValues(forKeys: [.fileSizeKey, .creationDateKey]),
                      let size = resources.fileSize,
                      let date = resources.creationDate
                else { return nil }
                return (url, date, UInt64(size))
            }.sorted { $0.1 < $1.1 }

            var totalSize: UInt64 = sortedFiles.reduce(0) { $0 + $1.2 }

            // 如果超过限制，删除旧文件
            for (url, _, size) in sortedFiles where totalSize > self.maxCacheSize {
                try? self.fileManager.removeItem(at: url)
                totalSize -= size
            }
        }
    }

    nonisolated func clearCache(completion: (@Sendable () -> Void)? = nil) {
        queue.async { [weak self] in
            guard let self = self else {
                DispatchQueue.main.async {
                    completion?()
                }
                return
            }

            // 取消所有活动的下载任务
            self.activeDownloads.withLock { downloads in
                downloads.values.forEach { $0.cancel() }
                downloads.removeAll()
            }

            DispatchQueue.main.async {
                completion?()
            }
        }
    }

    nonisolated func resourceLoader(_ resourceLoader: AVAssetResourceLoader, didCancel loadingRequest: AVAssetResourceLoadingRequest) {
        // 取消相关的网络请求
        self.cancel()
    }
}

// 添加 CMTime 扩展来检查时间是否有效
extension CMTime {
    var isValid: Bool {
        return !self.isIndefinite && !self.isNegativeInfinity && !self.isPositiveInfinity
    }
}

// 添加资源加载代理方法
extension VideoPlayerViewModel: AVAssetResourceLoaderDelegate {
    nonisolated func resourceLoader(_ resourceLoader: AVAssetResourceLoader,
                       shouldWaitForLoadingOfRequestedResource loadingRequest: AVAssetResourceLoadingRequest) -> Bool {

        // 获取原始 URL
        guard let url = loadingRequest.request.url else {
            loadingRequest.finishLoading(with: NSError(domain: "", code: -1, userInfo: nil))
            return false
        }

        // 创建网络请求
        Task { @MainActor in
            do {
                // 使用NetworkManager统一处理请求
                let data: Data = try await NetworkManager.shared.fetchData(url, contentType: .video)

                // 解析 M3U8 内容
                if let content = String(data: data, encoding: .utf8),
                   content.contains("#EXTM3U") {
                    do {
                        // 处理 M3U8 播放列表
                        let segments = parseM3U8WithByteRanges(content)
                        // 按顺序加载所有片段
                        let fullData = try await loadAllSegments(segments)

                        if let contentInfo = loadingRequest.contentInformationRequest {
                            contentInfo.contentType = "video/MP2T"
                            contentInfo.contentLength = Int64(fullData.count)
                            contentInfo.isByteRangeAccessSupported = true
                        }

                        loadingRequest.dataRequest?.respond(with: fullData)
                        loadingRequest.finishLoading()
                    } catch {
                        loadingRequest.finishLoading(with: error)
                    }
                } else {
                    // 直接返回数据
                    if let contentInfo = loadingRequest.contentInformationRequest {
                        contentInfo.contentType = "video/MP2T"
                        contentInfo.contentLength = Int64(data.count)
                        contentInfo.isByteRangeAccessSupported = true
                    }

                    loadingRequest.dataRequest?.respond(with: data)
                    loadingRequest.finishLoading()
                }
            } catch {
                loadingRequest.finishLoading(with: error)
            }
        }

        return true
    }

    private func parseM3U8WithByteRanges(_ content: String) -> [(url: String, byteRange: Range<Int>)] {
        var segments: [(url: String, byteRange: Range<Int>)] = []
        let lines = content.components(separatedBy: .newlines)

        var currentByteRange: Range<Int>?
        var currentURL: String?
        var currentDuration: Double?
        var totalDuration: Double = 0

        for line in lines {
            if line.hasPrefix("#EXTINF:") {
                // 解析片段时长
                let durationStr = line.dropFirst("#EXTINF:".count).split(separator: ",")[0]
                currentDuration = Double(durationStr)
            } else if line.hasPrefix("#EXT-X-BYTERANGE:") {
                // 解析字节范围
                let rangeStr = line.dropFirst("#EXT-X-BYTERANGE:".count)
                let parts = rangeStr.split(separator: "@")
                if parts.count == 2,
                   let length = Int(parts[0]),
                   let offset = Int(parts[1]) {
                    currentByteRange = offset..<(offset + length)
                }
            } else if !line.hasPrefix("#") && !line.isEmpty {
                // 处理 URL
                currentURL = line
                if let url = currentURL,
                   let range = currentByteRange,
                   let duration = currentDuration {
                    segments.append((url, range))
                    totalDuration += duration
                }
            }
        }

        Log.debug("📊 Total segments: \(segments.count), Total duration: \(totalDuration)s")
        return segments
    }

    private func loadAllSegments(_ segments: [(url: String, byteRange: Range<Int>)]) async throws -> Data {
        var fullData = Data()

        for (index, segment) in segments.enumerated() {
            // 构建完整的 URL
            let fullURLString = Configuration.API.Video.segmentPath(segmentUrl: segment.url)
            guard let url = URL(string: fullURLString) else {
                throw NetworkError.invalidURL
            }

            Log.debug("🔄 Loading segment \(index + 1)/\(segments.count)")
            Log.debug("📍 Range: \(segment.byteRange.lowerBound)-\(segment.byteRange.upperBound-1)")

            do {
                // 使用NetworkManager处理Range请求
                let rangeHeader = "bytes=\(segment.byteRange.lowerBound)-\(segment.byteRange.upperBound-1)"
                let data = try await NetworkManager.shared.fetchRangeDataWithRefresh(
                    url,
                    rangeHeader: rangeHeader,
                    contentType: .video
                )

                fullData.append(data)
                Log.debug("✅ Loaded segment \(index + 1): \(data.count) bytes")
            } catch {
                Log.error("❌ Error loading segment \(index + 1): \(error.localizedDescription)")
                throw error
            }
        }

        Log.debug("📦 Total data size: \(fullData.count) bytes")
        return fullData
    }
}
