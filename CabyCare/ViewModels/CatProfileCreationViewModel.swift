import Foundation
import SwiftUI
import Vision

@MainActor
class CatProfileCreationViewModel: ObservableObject {
    @Published var profile = CatProfileCreation()
    @Published var currentPage = 0
    @Published var isShowingCancelConfirmation = false
    @Published var isShowingInvalidPhotosAlert = false
    @Published var isProcessing = false
    @Published var error: String?
    @Published var isSubmitting = false
    @Published var totalSteps = 6
    
    // MARK: - Avatar Management
    @Published var selectedAvatar: UIImage?
    @Published var isShowingAvatarPicker = false
    
    // MARK: - Edit Mode Properties
    @Published var isEditMode = false
    @Published var editingCatId: String?
    @Published var originalCat: CatProfile?
    
    // MARK: - Initialization
    init() {
        // Default initialization for creation mode
    }
    
    init(editingCat: CatProfile) {
        self.isEditMode = true
        self.editingCatId = editingCat.id
        self.originalCat = editingCat
        loadCatProfileForEditing(editingCat)
    }
    
    // MARK: - Edit Mode Methods
    private func loadCatProfileForEditing(_ cat: CatProfile) {
        profile.name = cat.name
        
        // Parse gender from API format
        switch cat.gender {
        case 1, 11, 10:
            profile.gender = .male
        case -1, -11, -10:
            profile.gender = .female
        default:
            profile.gender = .unknown
        }
        
        // Parse neutered status from API format
        switch cat.gender {
        case 10, -10:
            profile.neuteredStatus = .neutered
        case 11, -11:
            profile.neuteredStatus = .notNeutered
        default:
            profile.neuteredStatus = .unknown
        }
        
        // Parse weight from formatted string (e.g., "3.5kg" -> 3.5)
        if cat.weight != NSLocalizedString("cat_weight_unknown", comment: "") {
            // Remove "kg" suffix and parse as Double
            let weightString = cat.weight.replacingOccurrences(of: "kg", with: "").trimmingCharacters(in: .whitespaces)
            profile.weight = Double(weightString)
        } else {
            profile.weight = nil
        }
        
        // Set birth date
        profile.birthDate = cat.birthDate
        
        // Load existing avatar from server if available
        if let avatarUrl = cat.avatarUrl, !avatarUrl.isEmpty {
            loadExistingAvatar(from: avatarUrl)
        }
        
        // TODO: Load existing photos from server if available
        // This would require additional API endpoint to fetch cat photos
    }
    
    // MARK: - Navigation
    func nextPage() {
        guard currentPage < 4 else { return }
        currentPage += 1
    }
    
    func previousPage() {
        guard currentPage > 0 else { return }
        currentPage -= 1
    }
    
    // MARK: - Avatar Management
    private func loadExistingAvatar(from urlString: String) {
        guard URL(string: urlString) != nil else { return }
        
        Task {
            do {
                // 添加缓存破坏参数以确保获取最新头像
                var urlStringWithCacheBuster = urlString
                if !urlStringWithCacheBuster.contains("?") {
                    urlStringWithCacheBuster += "?v=\(Int(Date().timeIntervalSince1970))"
                } else {
                    urlStringWithCacheBuster += "&v=\(Int(Date().timeIntervalSince1970))"
                }
                
                guard let cacheBustedUrl = URL(string: urlStringWithCacheBuster) else { return }
                
                let imageData = try await NetworkManager.shared.fetchData(cacheBustedUrl, contentType: .image)
                if let image = UIImage(data: imageData) {
                    // 确保加载的头像也经过方形处理
                    let processedImage = await processImageToSquare(image)
                    await MainActor.run {
                        self.selectedAvatar = processedImage
                        Log.debug("🐱 已从服务器加载并处理现有头像为方形，URL: \(urlStringWithCacheBuster)")
                    }
                }
            } catch {
                Log.error("🐱 加载现有头像失败: \(error.localizedDescription)")
            }
        }
    }
    
    // 将图像处理为方形（与CatManager中的逻辑保持一致）
    private func processImageToSquare(_ image: UIImage) async -> UIImage {
        return await withCheckedContinuation { continuation in
            Task { @MainActor in
                // 1. 裁剪为正方形
                let croppedImage = self.cropToSquare(image: image)
                
                // 2. 调整为合适的尺寸
                let resizedImage = self.resizeImage(image: croppedImage, targetSize: CGSize(width: 512, height: 512))
                
                continuation.resume(returning: resizedImage)
            }
        }
    }
    
    /// 将图片裁剪为正方形（居中裁剪）
    private func cropToSquare(image: UIImage) -> UIImage {
        let originalSize = image.size
        let sideLength = min(originalSize.width, originalSize.height)
        
        let x = (originalSize.width - sideLength) / 2
        let y = (originalSize.height - sideLength) / 2
        let cropRect = CGRect(x: x, y: y, width: sideLength, height: sideLength)
        
        guard let cgImage = image.cgImage?.cropping(to: cropRect) else {
            return image
        }
        
        return UIImage(cgImage: cgImage, scale: image.scale, orientation: image.imageOrientation)
    }
    
    /// 调整图片尺寸
    private func resizeImage(image: UIImage, targetSize: CGSize) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: targetSize)
        let resizedImage = renderer.image { _ in
            image.draw(in: CGRect(origin: .zero, size: targetSize))
        }
        return resizedImage
    }
    
    func setAvatar(_ image: UIImage) {
        selectedAvatar = compressImage(image, quality: Configuration.CatProfileCreation.photoCompressionQuality)
        Log.debug("🐱 头像已设置")
    }
    
    func removeAvatar() {
        selectedAvatar = nil
        Log.debug("🐱 头像已移除")
    }
    
    func selectAvatarFromPhotos(at index: Int) {
        guard profile.validatedPhotos.indices.contains(index) else { return }
        let photo = profile.validatedPhotos[index]
        if photo.isValid {
            selectedAvatar = photo.image
            Log.debug("🐱 从验证照片中选择头像，索引: \(index)")
        }
    }
    
    // MARK: - Photo Management
    func addPhotos(_ images: [UIImage]) {
        let remainingSlots = Configuration.CatProfileCreation.maxPhotos - profile.validatedPhotos.count
        let imagesToAdd = Array(images.prefix(remainingSlots))
        
        // 记录添加前的索引，用于计算添加了多少张照片
        let beforeCount = profile.validatedPhotos.count
        
        // 确保UI更新在主线程执行
        DispatchQueue.main.async {
            // 立即添加所有照片
            for image in imagesToAdd {
                guard let compressedImage = self.compressImage(image, quality: Configuration.CatProfileCreation.photoCompressionQuality) else {
                    print("Failed to compress image.")
                    continue
                }
                
                // 添加到数组中，使用明确的pending状态
                self.profile.validatedPhotos.append((
                    image: compressedImage,
                    isValid: false,
                    catBoundingBox: nil,
                    validationStatus: .pending
                ))
            }
        }
        
        // 启动验证任务
        Task {
            // 添加延迟
            try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒延迟
            
            // 在Task内重新计算索引范围
            let afterCount = profile.validatedPhotos.count
            let addedCount = afterCount - beforeCount

            // 从数组末尾开始验证新添加的照片
            if addedCount > 0 {
                for index in (afterCount - addedCount)..<afterCount {
                    if Task.isCancelled { break }
                    await validatePhoto(at: index)
                }
            }
        }
    }
    
    private func validatePhoto(at index: Int) async {
        guard index < profile.validatedPhotos.count else { return }
        
        // Update status to validating
        await MainActor.run {
            profile.validatedPhotos[index].validationStatus = .validating
        }
        
        // Try original orientation
        if let (validatedImage, isValid, catBox) = await validateCompressedImage(profile.validatedPhotos[index].image) {
            if isValid {
                await MainActor.run {
                    profile.validatedPhotos[index] = (
                        image: validatedImage,
                        isValid: true,
                        catBoundingBox: catBox,
                        validationStatus: .validated
                    )
                }
                return
            }
        }
        
        // If original orientation failed, try rotations
        let rotations: [CGFloat] = [.pi/2, .pi, 3*(.pi/2)] // 90, 180, 270 degrees
        for rotation in rotations {
            guard index < profile.validatedPhotos.count else { return } // Check if photo still exists
            
            let rotatedImage = profile.validatedPhotos[index].image.rotate(by: rotation)
            if let (validatedImage, isValid, catBox) = await validateCompressedImage(rotatedImage) {
                if isValid {
                    await MainActor.run {
                        profile.validatedPhotos[index] = (
                            image: validatedImage,
                            isValid: true,
                            catBoundingBox: catBox,
                            validationStatus: .validated
                        )
                    }
                    return
                }
            }
        }
        
        // If all orientations failed, mark as invalid
        if index < profile.validatedPhotos.count {
            await MainActor.run {
                profile.validatedPhotos[index].validationStatus = .failed
                profile.validatedPhotos[index].isValid = false
            }
        }
    }

    func compressImage(_ image: UIImage) -> UIImage? {
        return compressImage(image, quality: Configuration.CatProfileCreation.photoCompressionQuality)
    }
    
    func validateCompressedImage(_ image: UIImage) async -> (UIImage, Bool, CGRect?)? {
        // Detect cat bounding box
        let catBoundingBox = await detectCat(in: image)
        
        // The image is considered valid for preview if a cat bounding box is found.
        // If no box is found, it's still a processed image but might not be ideal for the special preview centering.
        if let _ = catBoundingBox {
            return (image, true, catBoundingBox)
        } else {
            // No cat box found, mark as not valid for special centering
            return (image, false, nil)
        }
    }
    
    private func detectCat(in image: UIImage) async -> CGRect? {
        guard let cgImage = image.cgImage else {
            print("Failed to get CGImage.")
            return nil
        }
        
        return await withCheckedContinuation { (continuation: CheckedContinuation<CGRect?, Never>) in
            // 创建专用的后台队列来执行Vision框架操作
            let visionQueue = DispatchQueue(label: "com.cabycare.vision", qos: .userInitiated)
            
            visionQueue.async {
                var continuationResumed = false // Flag to track if continuation has been resumed
                let lock = NSLock() // 添加锁来保护continuation状态

                let request = VNRecognizeAnimalsRequest { (request, error) in
                    lock.lock()
                    defer { lock.unlock() }
                    
                    // Ensure continuation is resumed only once
                    guard !continuationResumed else { return }

                    if let error = error {
                        print("Animal recognition error: \(error.localizedDescription)")
                        continuationResumed = true
                        continuation.resume(returning: nil)
                        return
                    }
                    
                    guard let results = request.results as? [VNRecognizedObjectObservation] else {
                        print("No animal recognition results or results are not VNRecognizedObjectObservation.")
                        continuationResumed = true
                        continuation.resume(returning: nil)
                        return
                    }
                    
                    var bestCatObservation: VNRecognizedObjectObservation? = nil
                    var highestConfidence: Float = 0.0

                    for observation in results {
                        for label in observation.labels {
                            if label.identifier == VNAnimalIdentifier.cat.rawValue && label.confidence > Configuration.CatProfileCreation.minCatConfidence {
                                if label.confidence > highestConfidence {
                                    highestConfidence = label.confidence
                                    bestCatObservation = observation
                                }
                                break 
                            }
                        }
                    }
                    
                    continuationResumed = true // Mark as resumed before calling resume
                    if let catObs = bestCatObservation {
                        print("Cat detected with bounding box: \(catObs.boundingBox) and confidence: \(highestConfidence)")
                        continuation.resume(returning: catObs.boundingBox)
                    } else {
                        print("No cat found with sufficient confidence.")
                        continuation.resume(returning: nil)
                    }
                }
                
                // For simulator, explicitly use CPU only to avoid potential Metal/ANE issues
                #if targetEnvironment(simulator)
                let allDevices = MLComputeDevice.allComputeDevices
                for device in allDevices {
                    // CPU device
                    if device.description.contains("MLCPUComputeDevice") {
                        request.setComputeDevice(.some(device), for: .main)
                        break
                    }
                }
                #endif

                let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
                do {
                    try handler.perform([request])
                } catch {
                    lock.lock()
                    if !continuationResumed {
                        print("Failed to perform animal recognition (handler.perform error): \(error.localizedDescription)")
                        continuationResumed = true
                        continuation.resume(returning: nil)
                    }
                    lock.unlock()
                }
            }
        }
    }
    
    func deletePhoto(at index: Int) {
        guard profile.validatedPhotos.indices.contains(index) else { return }
        profile.validatedPhotos.remove(at: index)
    }

    func previewProfile() async -> (Bool, CatProfileCreation.ProfileStatus) {
        isSubmitting = true
        
        // Validate profile
        guard profile.isNameValid else {
            isSubmitting = false
            return (false, .nameInvalid)
        }
        
        // Only use validated photos
        let validatedPhotos = profile.validatedPhotos.filter { $0.isValid }
        if validatedPhotos.isEmpty {
            isSubmitting = false
            return (false, .noValidPhoto)
        }

        return (true, .ok)
    }
    
    // MARK: - Submission
    func submitProfile() async -> (Bool, CatProfileCreation.ProfileStatus) {
        isSubmitting = true
        
        // Only use validated photos
        let validatedPhotos = profile.validatedPhotos.filter { $0.isValid }
        
        // Prepare data for submission
        do {
            let success: Bool
            
            if isEditMode, let catId = editingCatId {
                // Update existing cat
                success = try await updateCatProfile(catId: catId, photos: validatedPhotos.map { $0.image })
            } else {
                // Create new cat
                let profileData = prepareProfileData()
                success = try await sendProfileToServer(profileData, photos: validatedPhotos.map { $0.image })
            }
            
            DispatchQueue.main.async {
                self.isSubmitting = false
            }
            
            return (success, .ok)
        } catch {
            print("Error submitting profile: \(error.localizedDescription)")
            DispatchQueue.main.async {
                self.isSubmitting = false
            }
            return (false, .submissionFailed)
        }
    }
    
    // MARK: - Private helpers
    
    private func compressImage(_ image: UIImage, quality: CGFloat) -> UIImage? {
        guard let imageData = image.jpegData(compressionQuality: quality) else { return nil }
        return UIImage(data: imageData)
    }

    private func validateCatImage(_ image: UIImage) async -> (Bool, String?) {
        guard let cgImage = image.cgImage else {
            return (false, "Failed to get CGImage from UIImage.")
        }
        
        return await withCheckedContinuation { continuation in
            // 创建专用的后台队列来执行Vision框架操作
            let visionQueue = DispatchQueue(label: "com.cabycare.vision.validate", qos: .userInitiated)
            
            visionQueue.async {
                let request = VNRecognizeAnimalsRequest()
                let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
                var returnValue = (false, "")
                var countCat = 0
                
                #if targetEnvironment(simulator)
                let allDevices = MLComputeDevice.allComputeDevices
                for device in allDevices {
                    // CPU device
                    if device.description.contains("MLCPUComputeDevice") {
                        request.setComputeDevice(.some(device), for: .main)
                        break
                    }
                }
                #endif
                
                do {
                    try handler.perform([request])
                    
                    if let results = request.results {
                        for observation in results {
                            for label in observation.labels where label.identifier == "Cat" {
                                if label.confidence >= Configuration.CatProfileCreation.minCatConfidence {
                                    print("Cat detected with confidence: \(label.confidence)")
                                    countCat += 1
                                } else {
                                    print("Cat detected but confidence is: \(label.confidence)")
                                }
                            }
                        }
                        if countCat == 1 {
                            returnValue.0 = true
                        } else if countCat > 1 {
                            returnValue.1 = "Multiple cats detected."
                        } else {
                            returnValue.1 = "No cat detected."
                        }
                    } else {
                        returnValue.1 = "No results from Vision request."
                    }
                } catch {
                    print("Failed to perform vision request: \(error.localizedDescription)")
                    returnValue.1 = "Vision request failed: \(error.localizedDescription)"
                }
                
                continuation.resume(returning: returnValue)
            }
        }
    }
    
    private func prepareProfileData() -> [String: Any] {
        var data: [String: Any] = [
            "name": profile.name,
            "gender": profile.gender.rawValue,
            "neutered_status": profile.neuteredStatus.rawValue
        ]
        
        if let weight = profile.weight {
            data["weight"] = weight
        }
        
        if let birthDate = profile.birthDate {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd"
            data["birth_date"] = formatter.string(from: birthDate)
        }
        
        return data
    }
    
    private func sendProfileToServer(_ profileData: [String: Any], photos: [UIImage]) async throws -> Bool {
        // 使用CatManager来创建猫咪档案
        let success = try await CatManager.shared.createCat(
            name: profile.name,
            gender: profile.gender,
            neuteredStatus: profile.neuteredStatus,
            weight: profile.weight,
            birthDate: profile.birthDate,
            photos: photos,
            avatar: selectedAvatar
        )
        
        return success
    }
    
    private func updateCatProfile(catId: String, photos: [UIImage]) async throws -> Bool {
        // 使用CatManager来更新猫咪档案
        let success = try await CatManager.shared.updateCat(
            catId: catId,
            name: profile.name,
            gender: profile.gender,
            neuteredStatus: profile.neuteredStatus,
            weight: profile.weight,
            birthDate: profile.birthDate,
            photos: photos,
            avatar: selectedAvatar
        )
        
        return success
    }
    
    // MARK: - Validation
    enum ValidationError: LocalizedError {
        case invalidName
        case invalidWeight
        case invalidPhoto
        
        var errorDescription: String? {
            switch self {
            case .invalidName:
                return NSLocalizedString("cat_creation_error_invalid_name", comment: "")
            case .invalidWeight:
                return NSLocalizedString("cat_creation_error_invalid_weight", comment: "")
            case .invalidPhoto:
                return NSLocalizedString("cat_creation_error_invalid_photo", comment: "")
            }
        }
    }
}

// MARK: - API Models
struct CreateCatProfileRequest: Codable {
    let name: String
    let gender: Int8
    let weight: Double
    let birthDate: Date?
    let photosBase64: [String]
}

struct CreateCatProfileResponse: Codable {
    let status: String
    let message: String?
}

// MARK: - Photo Validation Status
extension CatProfileCreation {
    enum ValidationStatus {
        case pending
        case validating
        case validated
        case failed
    }
}

// MARK: - UIImage Extension
extension UIImage {
    func rotate(by radians: CGFloat) -> UIImage {
        let rotatedSize = CGRect(origin: .zero, size: size)
            .applying(CGAffineTransform(rotationAngle: radians))
            .integral.size
        UIGraphicsBeginImageContext(rotatedSize)
        defer { UIGraphicsEndImageContext() }
        
        guard let context = UIGraphicsGetCurrentContext() else { return self }
        context.translateBy(x: rotatedSize.width/2, y: rotatedSize.height/2)
        context.rotate(by: radians)
        draw(in: CGRect(x: -size.width/2, y: -size.height/2, width: size.width, height: size.height))
        
        return UIGraphicsGetImageFromCurrentImageContext() ?? self
    }
}
