import SwiftUI

struct RefreshableScrollView<Content: View>: View {
    let showsIndicators: Bool
    let onRefresh: (@escaping @Sendable () -> Void) async -> Void
    let content: Content
    
    @State private var isRefreshing = false
    @State private var progress: CGFloat = 0
    
    init(
        showsIndicators: Bool = true,
        onRefresh: @escaping (@escaping @Sendable () -> Void) async -> Void,
        @ViewBuilder content: () -> Content
    ) {
        self.showsIndicators = showsIndicators
        self.onRefresh = onRefresh
        self.content = content()
    }
    
    var body: some View {
        ScrollView(showsIndicators: showsIndicators) {
            ZStack(alignment: .top) {
                GeometryReader { geometry in
                    Color.clear.preference(
                        key: ScrollOffsetPreferenceKey.self,
                        value: geometry.frame(in: .named("scroll")).minY
                    )
                }
                .frame(height: 0)
                
                VStack(spacing: 0) {
                    if isRefreshing {
                        ProgressView()
                            .frame(height: 44)
                    }
                    content
                }
            }
        }
        .coordinateSpace(name: "scroll")
        .onPreferenceChange(ScrollOffsetPreferenceKey.self) { offset in
            Task { @MainActor in
                if offset > 50 && !isRefreshing {
                    isRefreshing = true
                    await onRefresh {
                        Task { @MainActor in
                            isRefreshing = false
                        }
                    }
                }
            }
        }
    }
}

private struct ScrollOffsetPreferenceKey: PreferenceKey {
    nonisolated(unsafe) static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
} 