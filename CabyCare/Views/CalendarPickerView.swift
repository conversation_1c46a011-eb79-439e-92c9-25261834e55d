import SwiftUI

// 添加 Log 导入，确保调试功能正常工作
// 注意：这个 import 可能不需要，因为 Log 可能通过其他方式可用
// 如果编译错误，可以移除这个 import

struct CalendarPickerView: View {
    @Binding var selectedDate: Date?
    let availableDates: [Date]
    let accentColor: Color
    let onDateSelected: (() -> Void)?
    let deviceTimezone: String
    
    @State private var currentWeek: Date
    @State private var dragOffset: CGFloat = 0
    @State private var isDragging = false
    @State private var animationDisabled = false
    
    private var calendar: Calendar {
        var calendar = Calendar.current
        calendar.timeZone = TimeZone(identifier: deviceTimezone) ?? .current
        calendar.firstWeekday = 1 // 确保周日是一周的第一天
        return calendar
    }
    
    private var monthYearFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMMM yyyy"
        formatter.timeZone = TimeZone(identifier: deviceTimezone) ?? .current
        return formatter
    }
    
    // 获取当前周数
    private var weekOfYearString: String {
        let weekNumber = calendar.component(.weekOfYear, from: currentWeek)
        return "Week \(weekNumber)"
    }
    
    // 获取当前显示的月份
    private var currentMonth: String {
        return monthYearFormatter.string(from: currentWeek)
    }
    
    init(selectedDate: Binding<Date?>, availableDates: [Date], accentColor: Color, deviceTimezone: String = "UTC", onDateSelected: (() -> Void)? = nil) {
        self._selectedDate = selectedDate
        self.availableDates = availableDates
        self.accentColor = accentColor
        self.deviceTimezone = deviceTimezone
        self.onDateSelected = onDateSelected
        
        // 初始化当前周为选中日期所在的周
        var calendar = Calendar.current
        calendar.timeZone = TimeZone(identifier: deviceTimezone) ?? .current
        calendar.firstWeekday = 1 // 周日为一周的第一天
        
        let initialDate = selectedDate.wrappedValue ?? availableDates.first ?? Date()
        
        // 找出当前日期所在周的第一天
        let weekday = calendar.component(.weekday, from: initialDate)
        let daysToSubtract = weekday - 1 // 周日为1，所以减1
        
        if let startOfWeek = calendar.date(byAdding: .day, value: -daysToSubtract, to: initialDate) {
            self._currentWeek = State(initialValue: startOfWeek)
        } else {
            self._currentWeek = State(initialValue: initialDate)
        }
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 整个可滑动的日历区域
            VStack(spacing: 4) {
                // 月份和周数显示
                HStack(spacing: 0) {
                    Text(weekOfYearString)
                        .font(.headline)
                        .foregroundColor(.primary)
                        .padding(.leading, 16)
                    
                    Spacer()
                    
                    Text(currentMonth)
                        .font(.footnote)
                        .foregroundColor(.primary)
                        .padding(.trailing, 16)
                }
                .padding(.top, 12)
                .padding(.bottom, 6)
                
                // 星期标题行
                HStack(spacing: 0) {
                    ForEach(0..<7, id: \.self) { index in
                        Text(calendar.veryShortWeekdaySymbols[index])
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .frame(maxWidth: .infinity)
                    }
                }
                .padding(.bottom, 6)
                
                // 滑动视图区域
                weekViewPager
                .padding(.bottom, 12)
            }
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
            )
            .contentShape(Rectangle()) // 确保整个区域可点击
            .gesture(
                DragGesture()
                    .onChanged { value in
                        if !isDragging {
                            animationDisabled = true
                        }
                        isDragging = true
                        dragOffset = value.translation.width
                    }
                    .onEnded { value in
                        let width = UIScreen.main.bounds.width
                        let threshold: CGFloat = width * 0.2 // 20%的屏幕宽度作为阈值
                        
                        let predictedEndTranslation = value.predictedEndTranslation.width
                        let velocity = abs(predictedEndTranslation - value.translation.width) / 100
                        let isSwiftSwipe = velocity > 1.0
                        
                        if abs(value.translation.width) > threshold || isSwiftSwipe {
                            let direction = value.translation.width > 0 ? 1 : -1
                            
                            if let newWeek = calendar.date(
                                byAdding: .day,
                                value: -7 * direction,
                                to: currentWeek
                            ) {
                                // 执行无动画切换
                                withAnimation(nil) {
                                    dragOffset = direction > 0 ? width : -width
                                }
                                
                                // 延迟一帧后更新currentWeek，并重置动画状态
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.01) {
                                    currentWeek = newWeek
                                    dragOffset = 0
                                    isDragging = false
                                    
                                    // 延迟一帧后重新启用动画
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.01) {
                                        animationDisabled = false
                                    }
                                }
                            }
                        } else {
                            // 如果拖动距离不够，回到原位
                            withAnimation(.spring(response: 0.3)) {
                                dragOffset = 0
                                isDragging = false
                            }
                            
                            // 动画结束后重新启用动画
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                animationDisabled = false
                            }
                        }
                    }
            )
            .padding(.horizontal, 8)
        }
    }
    
    // 分页周视图
    private var weekViewPager: some View {
        ZStack {
            // 当前周视图
            weekView(for: currentWeek)
                .offset(x: dragOffset)
                .animation(animationDisabled ? nil : .spring(response: 0.3), value: dragOffset)
            
            // 如果正在向右拖动，显示上一周
            if isDragging && dragOffset > 0 {
                if let prevWeekDate = calendar.date(byAdding: .day, value: -7, to: currentWeek) {
                    weekView(for: prevWeekDate)
                        .offset(x: dragOffset - UIScreen.main.bounds.width)
                        .animation(animationDisabled ? nil : .spring(response: 0.3), value: dragOffset)
                }
            }
            
            // 如果正在向左拖动，显示下一周
            if isDragging && dragOffset < 0 {
                if let nextWeekDate = calendar.date(byAdding: .day, value: 7, to: currentWeek) {
                    weekView(for: nextWeekDate)
                        .offset(x: dragOffset + UIScreen.main.bounds.width)
                        .animation(animationDisabled ? nil : .spring(response: 0.3), value: dragOffset)
                }
            }
        }
        .clipped() // 裁剪溢出内容
        .frame(height: 60) // 确保高度固定
    }
    
    // 单周视图
    private func weekView(for startOfWeek: Date) -> some View {
        HStack(spacing: 0) {
            ForEach(0..<7, id: \.self) { dayOffset in
                if let date = calendar.date(byAdding: .day, value: dayOffset, to: startOfWeek) {
                    dayCell(for: date)
                        .frame(maxWidth: .infinity)
                }
            }
        }
        .frame(maxWidth: .infinity)
    }
    
    // 单个日期单元格
    private func dayCell(for date: Date) -> some View {
        let isAvailable = availableDates.contains { calendar.isDate($0, inSameDayAs: date) }
        let isSelected = selectedDate.map { calendar.isDate($0, inSameDayAs: date) } ?? false
        let isToday = calendar.isDateInToday(date)
        
        return Button {
            if isAvailable {
                withAnimation(.spring(response: 0.3)) {
                    selectedDate = date
                    onDateSelected?()
                }
            }
        } label: {
            ZStack {
                RoundedRectangle(cornerSize: .init(width: 10, height: 10))
                    .fill(isSelected ? accentColor : (isToday ? Color.gray.opacity(0.15) : Color.clear))
                    .frame(width: 44, height: 36)
                    .overlay(
                        RoundedRectangle(cornerSize: .init(width: 10, height: 10))
                            .stroke(isAvailable && !isSelected ? accentColor : Color.clear, lineWidth: 1)
                    )

                // 日期文字
                Text("\(calendar.component(.day, from: date))")
                    .font(.system(.body, design: .rounded))
                    .fontWeight(isToday || isSelected ? .bold : .regular)
                    .foregroundColor(isSelected ? .white : (isAvailable ? (isToday ? accentColor : .primary) : .secondary))
            }
            .padding(.vertical, 4)
        }
        .buttonStyle(PlainButtonStyle()) // 防止按钮状态影响手势
        .disabled(!isAvailable)
    }
}

// 日历辅助扩展
private extension Calendar {
    func startOfMonth(for date: Date) -> Date {
        let components = dateComponents([.year, .month], from: date)
        return self.date(from: components) ?? date
    }
}
