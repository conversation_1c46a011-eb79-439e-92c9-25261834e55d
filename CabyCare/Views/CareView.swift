import SwiftUI

struct CareView
: View {
    @ObservedObject var viewModel: VideoPlayerViewModel
    @State private var showLivePlayer = false
    @ObservedObject private var notificationManager = NotificationManager.shared
    @ObservedObject private var catManager = CatManager.shared
    @Environment(\.colorScheme) var colorScheme
    @State private var hasInitialized = false
    @State private var isManualRefreshing = false
    @State private var lastRefreshTime: Date?

    var body: some View {
        NavigationView {
            VideoListView(viewModel: viewModel)
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .principal) {
                        HStack {
                            Text("Care")
                                .font(.headline)
                                .foregroundColor(AppTheme.textColor(for: colorScheme))
                            
                            // 显示刷新状态指示器
                            if viewModel.isRefreshing {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .padding(.leading, 8)
                            }
                        }
                    }
                    ToolbarItem(placement: .navigationBarTrailing) {
                        HStack(spacing: 12) {
                            // 手动刷新按钮
                            Button(action: {
                                Task {
                                    await performManualRefresh()
                                }
                            }) {
                                Image(systemName: "arrow.clockwise")
                                    .foregroundColor(isManualRefreshing ? .gray : AppTheme.primaryColor(for: colorScheme))
                                    .rotationEffect(.degrees(isManualRefreshing ? 360 : 0))
                                    .animation(
                                        isManualRefreshing ? 
                                        .linear(duration: 1.0).repeatForever(autoreverses: false) : 
                                        .default, 
                                        value: isManualRefreshing
                                    )
                            }
                            .disabled(isManualRefreshing || viewModel.isRefreshing)
                            
                            NavigationLink(destination: NotificationSettingsView()) {
                                Image(systemName: "gear")
                                    .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                            }
                        }
                    }
                }
        }
        .task {
            guard !hasInitialized else { return }
            hasInitialized = true
            
            async let notificationTask: Void = notificationManager.initializeSettings()
            async let catDataTask: Void = initializeCatData()
            
            _ = await notificationTask
            _ = await catDataTask
            
            Log.info("🏠 CareView 初始化完成")
        }
        .onReceive(NotificationCenter.default.publisher(for: .authenticationStatusChanged)) { _ in
            if AuthManager.shared.isAuthenticated && !hasInitialized {
                Task {
                    await initializeCatData()
                }
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .dataRefreshedOnForeground)) { _ in
            // 当应用进入前台且数据已刷新时，无需额外操作，因为VideoPlayerViewModel已经刷新了
            Log.info("📱 CareView: 收到前台数据刷新通知，VideoPlayerViewModel已自动刷新")
        }
        .refreshable {
            // SwiftUI 原生的下拉刷新支持
            await performManualRefresh()
        }
        .overlay(
            // 刷新成功提示
            Group {
                if let refreshTime = lastRefreshTime,
                   Date().timeIntervalSince(refreshTime) < 2.0 {
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                        Text("已刷新最新数据")
                            .font(.caption)
                            .foregroundColor(.primary)
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(.regularMaterial)
                    )
                    .transition(.move(edge: .top).combined(with: .opacity))
                }
            }
            .animation(.spring(), value: lastRefreshTime),
            alignment: .top
        )
    }
    
    /// 执行手动刷新
    private func performManualRefresh() async {
        guard !isManualRefreshing && !viewModel.isRefreshing else { return }
        
        isManualRefreshing = true
        defer { isManualRefreshing = false }
        
        Log.info("🔄 用户手动刷新开始")
        
        // 先刷新猫咪数据
        await catManager.refreshAllCats()
        
        // 然后刷新最新的视频数据
        await viewModel.refreshLatestData()
        
        // 更新最后刷新时间，用于显示成功提示
        lastRefreshTime = Date()
        
        Log.info("✅ 用户手动刷新完成")
        
        // 触发轻微的触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
    
    private func initializeCatData() async {
        guard AuthManager.shared.isAuthenticated else {
            Log.info("ℹ️ 用户未认证，跳过猫咪数据初始化")
            return
        }
        
        await viewModel.preloadCatData()
        
        await catManager.refreshAllCats()
        
        Log.info("🐱 CareView: 猫咪数据初始化完成")
    }
}
