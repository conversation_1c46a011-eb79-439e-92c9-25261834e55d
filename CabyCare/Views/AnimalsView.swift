import SwiftUI

enum CatListTab: String, CaseIterable {
    case normal = "正常"
    case hidden = "隐藏"
    
    var title: String {
        switch self {
        case .normal:
            return NSLocalizedString("animals_tab_normal", comment: "")
        case .hidden:
            return NSLocalizedString("animals_tab_hidden", comment: "")
        }
    }
    
    var icon: String {
        switch self {
        case .normal:
            return "cat"
        case .hidden:
            return "eye.slash"
        }
    }
}

struct AnimalsView: View {
    @Environment(\.colorScheme) var colorScheme
    @StateObject private var catManager = CatManager.shared
    @State private var showingAddMenu = false
    @State private var showingCatCreation = false
    @State private var showingDeviceCreation = false
    @State private var showingError = false
    @State private var showingHiddenCats = false
    @State private var selectedTab: CatListTab = .normal
    @State private var avatarRefreshTrigger = 0  // 用于触发头像刷新
    @State private var hasAppeared = false  // 跟踪视图是否已经出现过
    @State private var lastRefreshTime: Date?  // 跟踪最后刷新时间

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Tab selector - always at top
                Picker("Cat List Type", selection: $selectedTab) {
                    ForEach(CatListTab.allCases, id: \.self) { tab in
                        Label(tab.title, systemImage: tab.icon)
                            .tag(tab)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding(.horizontal)
                .padding(.top, 8)
                
                // Content based on selected tab - fills remaining space
                if selectedTab == .normal {
                    normalCatsView
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    hiddenCatsView
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .principal) {
                    HStack {
                        Text(NSLocalizedString("animals_title", comment: ""))
                            .font(.headline)
                            .foregroundColor(AppTheme.textColor(for: colorScheme))
                        Spacer()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button {
                            showingCatCreation = true
                        } label: {
                            Label(NSLocalizedString("add_cat", comment: ""), systemImage: "cat")
                        }
                        
                        Button {
                            showingDeviceCreation = true
                        } label: {
                            Label(NSLocalizedString("add_device", comment: ""), systemImage: "plus.circle")
                        }
                    } label: {
                        Image(systemName: "plus")
                    }
                }
            }
            .sheet(isPresented: $showingCatCreation) {
                CatProfileCreationView()
            }
            .sheet(isPresented: $showingDeviceCreation) {
                // TODO: Implement device creation view
                Text("Device Creation View")
            }
            .alert(
                NSLocalizedString("animals_error_title", comment: ""),
                isPresented: $showingError,
                actions: {
                    Button(NSLocalizedString("animals_retry", comment: "")) {
                        Task {
                            await catManager.refreshAllCats(forceRefresh: true)
                        }
                    }
                    Button(NSLocalizedString("ok", comment: ""), role: .cancel) {}
                },
                message: {
                    Text(catManager.error ?? "")
                }
            )
            .onAppear {
                // 只在首次出现或距离上次刷新超过30秒时才刷新
                let shouldRefresh = !hasAppeared || 
                    (lastRefreshTime.map { Date().timeIntervalSince($0) > 30 } ?? true)
                
                if shouldRefresh {
                    Log.debug("🐱 AnimalsView: 首次加载或需要刷新，开始获取猫咪数据")
                    hasAppeared = true
                    lastRefreshTime = Date()
                    
                    Task {
                        await catManager.refreshAllCats(forceRefresh: false)
                    }
                } else {
                    Log.debug("🐱 AnimalsView: 使用现有数据，跳过刷新")
                }
            }
            .onChange(of: catManager.error) { _, newError in
                showingError = newError != nil
            }
            .onReceive(catManager.$cats) { _ in
                avatarRefreshTrigger += 1
            }
            .onReceive(catManager.$hiddenCats) { _ in
                avatarRefreshTrigger += 1
            }
            .onReceive(NotificationCenter.default.publisher(for: .avatarUpdated)) { _ in
                // 当收到头像更新通知时，触发头像刷新
                avatarRefreshTrigger += 1
                Log.debug("🖼️ 收到头像更新通知，触发刷新: \(avatarRefreshTrigger)")
            }
        }
    }
    
    // MARK: - Normal Cats View
    private var normalCatsView: some View {
        ZStack {
            if catManager.isLoading && catManager.cats.isEmpty {
                // Loading state when no cached data
                VStack(spacing: 16) {
                    ProgressView()
                        .scaleEffect(1.2)
                    Text(NSLocalizedString("animals_loading", comment: ""))
                        .foregroundColor(.secondary)
                }
            } else if catManager.cats.isEmpty {
                // Empty state
                VStack(spacing: 20) {
                    Spacer()
                    
                    Image(systemName: "cat")
                        .font(.system(size: 60))
                        .foregroundColor(.secondary)
                    
                    Text(NSLocalizedString("animals_no_cats_message", comment: ""))
                        .font(.headline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                    
                    Button(action: {
                        showingCatCreation = true
                    }) {
                        HStack {
                            Image(systemName: "plus")
                            Text(NSLocalizedString("add_cat", comment: ""))
                        }
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(AppTheme.buttonTextColor(for: colorScheme))
                        .padding(.vertical, 12)
                        .padding(.horizontal, 20)
                        .background(AppTheme.buttonColor(for: colorScheme))
                        .cornerRadius(10)
                    }
                    
                    Spacer()
                }
                .padding()
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                // Content with cats
                ScrollView {
                    LazyVStack(spacing: 20) {
                        ForEach(catManager.cats) { cat in
                            CatProfileCard(cat: cat, isHidden: false, avatarRefreshTrigger: avatarRefreshTrigger)
                        }
                    }
                    .padding()
                }
                .refreshable {
                    await catManager.refreshAllCats(forceRefresh: true)
                    // 触发头像刷新
                    avatarRefreshTrigger += 1
                }
            }
        }
    }
    
    // MARK: - Hidden Cats View
    private var hiddenCatsView: some View {
        ZStack {
            if catManager.isLoading && catManager.hiddenCats.isEmpty {
                // Loading state when no cached data
                VStack(spacing: 16) {
                    ProgressView()
                        .scaleEffect(1.2)
                    Text(NSLocalizedString("animals_loading_hidden", comment: ""))
                        .foregroundColor(.secondary)
                }
            } else if catManager.hiddenCats.isEmpty {
                // Empty state
                VStack(spacing: 20) {
                    Spacer()
                    
                    Image(systemName: "eye.slash")
                        .font(.system(size: 60))
                        .foregroundColor(.secondary)
                    
                    Text(NSLocalizedString("animals_no_hidden_cats_message", comment: ""))
                        .font(.headline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                    
                    Spacer()
                }
                .padding()
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                // Content with hidden cats
                ScrollView {
                    LazyVStack(spacing: 20) {
                        ForEach(catManager.hiddenCats) { cat in
                            CatProfileCard(cat: cat, isHidden: true, avatarRefreshTrigger: avatarRefreshTrigger)
                        }
                    }
                    .padding()
                }
                .refreshable {
                    await catManager.refreshAllCats(forceRefresh: true)
                    // 触发头像刷新
                    avatarRefreshTrigger += 1
                }
            }
        }
    }
}

struct CatProfileCard: View {
    let cat: CatProfile
    let isHidden: Bool
    let avatarRefreshTrigger: Int
    @Environment(\.colorScheme) var colorScheme
    @StateObject private var catManager = CatManager.shared
    @State private var showingActionSheet = false
    @State private var showingDeleteAlert = false
    @State private var showingEditSheet = false
    @State private var isPerformingAction = false

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header with cat avatar, name and basic info
            HStack(spacing: 16) {
                // Cat avatar
                CatAvatarView(
                    avatarUrl: cat.avatarUrl,
                    size: 60,
                    cornerRadius: 12,
                    refreshTrigger: avatarRefreshTrigger
                )
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(cat.name)
                        .font(.title2)
                        .bold()
                    
                    // 品种、性别、绝育状态在同一行
                    HStack(spacing: 4) {
                        Text(NSLocalizedString(getCatGender(gender: cat.gender), comment: ""))
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        // 绝育状态 - 仅当已绝育时显示
                        if let neuteredStatus = cat.neuteredStatus,
                           neuteredStatus == NSLocalizedString("cat_neutered_status", comment: "") {
                            Text("·")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            Text(neuteredStatus)
                                .font(.subheadline)
                                .foregroundColor(.green)
                        }
                    }
                }

                Spacer()
                
                // Edit menu button
                Button(action: {
                    showingActionSheet = true
                }) {
                    Image(systemName: "ellipsis")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.secondary)
                        .frame(width: 24, height: 24)
                }
                .disabled(isPerformingAction)
            }

            Divider()

            // Cat details
            HStack(spacing: 20) {
                StatItem(
                    icon: "calendar",
                    value: cat.age,
                    label: NSLocalizedString("cat_creation_age_label", comment: ""),
                    color: AppTheme.primaryColor(for: colorScheme)
                )

                StatItem(
                    icon: "scalemass",
                    value: cat.weight,
                    label: NSLocalizedString("cat_creation_weight_label", comment: ""),
                    color: AppTheme.primaryColor(for: colorScheme)
                )

                if cat.healthStatus != .healthy {
                    StatItem(
                        icon: "heart",
                        value: cat.healthStatus.localizedString,
                        label: NSLocalizedString("cat_health_status_label", comment: ""),
                        color: healthStatusColor(cat.healthStatus)
                    )
                }
            }
        }
        .padding()
        .background(AppTheme.secondaryBackgroundColor(for: colorScheme))
        .cornerRadius(16)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(AppTheme.primaryColor(for: colorScheme).opacity(0.2), lineWidth: 1)
        )
        .overlay(
            Group {
                if isPerformingAction {
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.black.opacity(0.3))
                        .overlay(
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        )
                }
            }
        )
        .confirmationDialog(
            NSLocalizedString("cat_edit_menu_title", comment: ""),
            isPresented: $showingActionSheet
        ) {
            // Edit action (available for both normal and hidden cats)
            Button(NSLocalizedString("cat_action_edit", comment: "")) {
                showingEditSheet = true
            }
            
            if isHidden {
                // Hidden cat actions
                Button(NSLocalizedString("cat_action_restore", comment: "")) {
                    Task {
                        await performAction {
                            try await catManager.restoreCat(catId: cat.id)
                        }
                    }
                }
            } else {
                // Normal cat actions
                Button(NSLocalizedString("cat_action_hide", comment: "")) {
                    Task {
                        await performAction {
                            try await catManager.hideCat(catId: cat.id)
                        }
                    }
                }
            }
            
            Button(NSLocalizedString("cat_action_delete", comment: ""), role: .destructive) {
                showingDeleteAlert = true
            }
            
            Button(NSLocalizedString("cancel", comment: ""), role: .cancel) {}
        }
        .alert(
            NSLocalizedString("cat_delete_confirmation_title", comment: ""),
            isPresented: $showingDeleteAlert
        ) {
            Button(NSLocalizedString("cat_action_delete", comment: ""), role: .destructive) {
                Task {
                    await performAction {
                        try await catManager.deleteCat(catId: cat.id)
                    }
                }
            }
            Button(NSLocalizedString("cancel", comment: ""), role: .cancel) {}
        } message: {
            Text(NSLocalizedString("cat_delete_confirmation_message", comment: ""))
        }
        .sheet(isPresented: $showingEditSheet) {
            CatProfileCreationView(editingCat: cat)
        }
    }
    
    // MARK: - Helper Methods
    private func performAction(_ action: @escaping () async throws -> Void) async {
        isPerformingAction = true
        
        do {
            try await action()
        } catch {
            Log.error("❌ 执行猫咪操作失败: \(error.localizedDescription)")
            // TODO: Show error message to user
        }
        
        isPerformingAction = false
    }

    private func healthStatusColor(_ status: CatHealthStatus) -> Color {
        switch status {
        case .healthy:
            return .green
        case .needsAttention:
            return .yellow
        case .sick:
            return .red
        }
    }
}

#Preview {
    AnimalsView()
}