import SwiftUI

/// 令牌测试界面 - 用于开发和调试令牌刷新机制
struct TokenTestingView: View {
    @StateObject private var tokenTester = TokenExpiryTester.shared
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var isRefreshing = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 令牌状态显示区域
                tokenStatusSection
                
                // 基础测试功能
                basicTestingSection
                
                // 高级测试功能
                advancedTestingSection
                
                // 恢复功能
                recoverySection
                
                // 最后测试结果
                testResultSection
            }
            .padding()
        }
        .navigationTitle("🧪 令牌测试工具")
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            tokenTester.updateTokenStatus()
        }
        .refreshable {
            tokenTester.updateTokenStatus()
        }
        .alert("测试结果", isPresented: $showingAlert) {
            Button("确定", role: .cancel) {}
        } message: {
            Text(alertMessage)
        }
    }
    
    // MARK: - 令牌状态区域
    
    private var tokenStatusSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "info.circle.fill")
                    .foregroundColor(.blue)
                Text("令牌状态")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("刷新") {
                    tokenTester.updateTokenStatus()
                }
                .font(.caption)
                .buttonStyle(.bordered)
            }
            
            Text(tokenTester.currentTokenStatus)
                .font(.system(.caption, design: .monospaced))
                .padding()
                .frame(maxWidth: .infinity, alignment: .leading)
                .background(Color(.systemGray6))
                .cornerRadius(8)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
    
    // MARK: - 基础测试区域
    
    private var basicTestingSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "hammer.fill")
                    .foregroundColor(.orange)
                Text("基础测试")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                TestButton(
                    title: "设置令牌过期",
                    subtitle: "立即过期",
                    icon: "clock.badge.exclamationmark",
                    color: .red
                ) {
                    tokenTester.forceAccessTokenExpiry()
                    showResult("访问令牌已设置为过期状态")
                }
                
                TestButton(
                    title: "设置即将过期",
                    subtitle: "2分钟后过期",
                    icon: "clock.arrow.circlepath",
                    color: .orange
                ) {
                    tokenTester.forceAccessTokenNearExpiry()
                    showResult("访问令牌设置为即将过期")
                }
                
                TestButton(
                    title: "损坏访问令牌",
                    subtitle: "格式无效",
                    icon: "key.slash",
                    color: .red
                ) {
                    tokenTester.corruptAccessToken()
                    showResult("访问令牌已损坏")
                }
                
                TestButton(
                    title: "损坏刷新令牌",
                    subtitle: "刷新失败",
                    icon: "arrow.triangle.2.circlepath.slash",
                    color: .red
                ) {
                    tokenTester.corruptRefreshToken()
                    showResult("刷新令牌已损坏")
                }
            }
            
            // 手动刷新按钮（单独一行）
            TestButton(
                title: "手动触发令牌刷新",
                subtitle: "测试刷新机制",
                icon: "arrow.triangle.2.circlepath",
                color: .blue,
                fullWidth: true
            ) {
                isRefreshing = true
                Task {
                    await tokenTester.triggerTokenRefresh()
                    isRefreshing = false
                    showResult(tokenTester.lastTestResult)
                }
            }
            .disabled(isRefreshing)
            .overlay {
                if isRefreshing {
                    ProgressView()
                        .scaleEffect(0.8)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
    
    // MARK: - 高级测试区域
    
    private var advancedTestingSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "gearshape.2.fill")
                    .foregroundColor(.purple)
                Text("高级测试")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                TestButton(
                    title: "损坏所有令牌",
                    subtitle: "强制重新登录",
                    icon: "xmark.shield",
                    color: .red
                ) {
                    tokenTester.corruptBothTokens()
                    showResult("所有令牌已损坏")
                }
                
                TestButton(
                    title: "清除过期信息",
                    subtitle: "移除时间戳",
                    icon: "trash.clock",
                    color: .gray
                ) {
                    tokenTester.clearTokenExpiryInfo()
                    showResult("过期时间信息已清除")
                }
                
                TestButton(
                    title: "测试静默刷新",
                    subtitle: "模拟前台切换",
                    icon: "arrow.up.circle",
                    color: .green
                ) {
                    tokenTester.testSilentRefresh()
                    showResult("静默刷新已触发")
                }
                
                TestButton(
                    title: "网络错误场景",
                    subtitle: "需断网测试",
                    icon: "wifi.slash",
                    color: .orange
                ) {
                    tokenTester.simulateNetworkErrorDuringRefresh()
                    showResult("请断开网络连接测试")
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
    
    // MARK: - 恢复功能区域
    
    private var recoverySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "arrow.clockwise.circle.fill")
                    .foregroundColor(.green)
                Text("恢复功能")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            TestButton(
                title: "重置所有令牌",
                subtitle: "清除并重新登录",
                icon: "arrow.counterclockwise.circle",
                color: .green,
                fullWidth: true
            ) {
                Task {
                    await tokenTester.resetTokensToNormalState()
                    showResult("已登出，请重新登录")
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
    
    // MARK: - 测试结果区域
    
    private var testResultSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "doc.text.fill")
                    .foregroundColor(.blue)
                Text("最后测试结果")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            if !tokenTester.lastTestResult.isEmpty {
                Text(tokenTester.lastTestResult)
                    .font(.system(.caption, design: .monospaced))
                    .padding()
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
            } else {
                Text("尚未进行测试")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding()
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
    
    // MARK: - 辅助方法
    
    private func showResult(_ message: String) {
        alertMessage = message
        showingAlert = true
        tokenTester.updateTokenStatus()
    }
}

// MARK: - 测试按钮组件

struct TestButton: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    var fullWidth: Bool = false
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
                
                Text(subtitle)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .padding(.vertical, 12)
            .padding(.horizontal, fullWidth ? 16 : 8)
            .frame(maxWidth: fullWidth ? .infinity : nil)
            .background(Color(.systemGray6))
            .cornerRadius(8)
        }
        .buttonStyle(.plain)
    }
}

// MARK: - 预览

struct TokenTestingView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            TokenTestingView()
        }
    }
} 