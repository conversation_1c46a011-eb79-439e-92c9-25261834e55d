import SwiftUI
import PhotosUI
import UIKit

// MARK: - Avatar Management View
struct AvatarManagementView: View {
    @Binding var selectedAvatar: UIImage?
    @Binding var isShowingAvatarPicker: Bool
    let validatedPhotos: [(image: UIImage, isValid: Bool, catBoundingBox: CGRect?, validationStatus: CatProfileCreation.ValidationStatus)]
    let onAvatarSelected: (UIImage) -> Void
    let onAvatarRemoved: () -> Void
    let onSelectFromPhotos: (Int) -> Void
    
    @State private var showingPhotosPicker = false
    @State private var showingCamera = false
    @State private var showingImageEditor = false
    @State private var showingPhotoSelection = false
    @State private var selectedPhotoItems: [PhotosPickerItem] = []
    @State private var tempImage: UIImage?
    
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        VStack(spacing: 20) {
            // 头像预览区域
            avatarPreviewSection
            
            // 操作按钮区域
            avatarActionsSection
            
            // 从现有照片选择
            if !validatedPhotos.isEmpty {
                existingPhotosSection
            }
        }
        .padding(24)
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 4)
        .sheet(isPresented: $showingPhotosPicker) {
            photoPickerSheet
        }
        .sheet(isPresented: $showingImageEditor) {
            if let image = tempImage {
                AvatarImageEditor(
                    image: image,
                    onSave: { editedImage in
                        onAvatarSelected(editedImage)
                        showingImageEditor = false
                        tempImage = nil
                        // 关闭整个头像管理界面
                        isShowingAvatarPicker = false
                    },
                    onCancel: {
                        showingImageEditor = false
                        tempImage = nil
                    }
                )
            }
        }
        .actionSheet(isPresented: $showingCamera) {
            ActionSheet(
                title: Text("选择头像"),
                buttons: [
                    .default(Text("拍照")) {
                        // TODO: 实现相机功能
                    },
                    .default(Text("从相册选择")) {
                        showingPhotosPicker = true
                    },
                    .cancel()
                ]
            )
        }
        .onChange(of: selectedPhotoItems) { _, newItems in
            Task {
                if let item = newItems.first,
                   let data = try? await item.loadTransferable(type: Data.self),
                   let image = UIImage(data: data) {
                    tempImage = image
                    showingImageEditor = true
                }
                selectedPhotoItems = []
            }
        }
    }
    
    // MARK: - Avatar Preview Section
    private var avatarPreviewSection: some View {
        VStack(spacing: 12) {
            Text("猫咪头像")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.primary)
            
            ZStack {
                // 头像容器
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.1))
                    .frame(width: 120, height: 120)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.gray.opacity(0.3), lineWidth: 2)
                    )
                
                if let avatar = selectedAvatar {
                    // 显示选中的头像
                    Image(uiImage: avatar)
                        .resizable()
                        .scaledToFill()
                        .frame(width: 120, height: 120)
                        .clipShape(RoundedRectangle(cornerRadius: 12))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(AppTheme.primaryColor(for: colorScheme), lineWidth: 3)
                        )
                } else {
                    // 占位符
                    VStack(spacing: 8) {
                        Image(systemName: "cat.fill")
                            .font(.system(size: 32))
                            .foregroundColor(.gray)
                        Text("未设置")
                            .font(.system(size: 12))
                            .foregroundColor(.gray)
                    }
                }
                
                // 编辑按钮
                if selectedAvatar != nil {
                    Button(action: {
                        if let avatar = selectedAvatar {
                            tempImage = avatar
                            showingImageEditor = true
                        }
                    }) {
                        Image(systemName: "pencil.circle.fill")
                            .font(.system(size: 24))
                            .foregroundColor(.white)
                            .background(
                                Circle()
                                    .fill(AppTheme.primaryColor(for: colorScheme))
                                    .frame(width: 32, height: 32)
                            )
                    }
                    .offset(x: 40, y: 40)
                }
            }
        }
    }
    
    // MARK: - Avatar Actions Section
    private var avatarActionsSection: some View {
        VStack(spacing: 12) {
            // 主要操作按钮
            HStack(spacing: 12) {
                // 选择新头像按钮
                Button(action: {
                    showingCamera = true
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "photo.badge.plus")
                            .font(.system(size: 16, weight: .medium))
                        Text("选择头像")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(AppTheme.primaryColor(for: colorScheme))
                    .foregroundColor(AppTheme.buttonTextColor(for: colorScheme))
                    .cornerRadius(10)
                }
                
                // 移除头像按钮
                if selectedAvatar != nil {
                    Button(action: onAvatarRemoved) {
                        HStack(spacing: 8) {
                            Image(systemName: "trash")
                                .font(.system(size: 16, weight: .medium))
                            Text("移除")
                                .font(.system(size: 16, weight: .medium))
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(Color.red.opacity(0.1))
                        .foregroundColor(.red)
                        .cornerRadius(10)
                    }
                }
            }
        }
    }
    
    // MARK: - Existing Photos Section
    private var existingPhotosSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("从验证照片中选择")
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.primary)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(Array(validatedPhotos.indices), id: \.self) { index in
                        let photo = validatedPhotos[index]
                        if photo.isValid {
                            Button(action: {
                                tempImage = photo.image
                                showingImageEditor = true
                            }) {
                                Image(uiImage: photo.image)
                                    .resizable()
                                    .scaledToFill()
                                    .frame(width: 80, height: 80)
                                    .clipShape(RoundedRectangle(cornerRadius: 12))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 12)
                                            .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                    )
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                }
                .padding(.horizontal, 4)
            }
        }
    }
    
    // MARK: - Photo Picker Sheet
    private var photoPickerSheet: some View {
        PhotosPicker(
            selection: $selectedPhotoItems,
            maxSelectionCount: 1,
            matching: .images,
            preferredItemEncoding: .automatic
        ) {
            Text("选择照片")
        }
    }
}

// MARK: - Avatar Image Editor
struct AvatarImageEditor: View {
    let image: UIImage
    let onSave: (UIImage) -> Void
    let onCancel: () -> Void
    
    @State private var scale: CGFloat = 1.0
    @State private var offset: CGSize = .zero
    @State private var rotation: Angle = .zero
    @State private var lastScaleValue: CGFloat = 1.0
    @State private var lastOffsetValue: CGSize = .zero
    @State private var lastRotationValue: Angle = .zero
    
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 编辑预览区域
                ZStack {
                    Color.black.opacity(0.1)
                        .frame(width: 300, height: 300)
                        .clipShape(RoundedRectangle(cornerRadius: 16))
                    
                    Image(uiImage: image)
                        .resizable()
                        .scaledToFill()
                        .scaleEffect(scale)
                        .offset(offset)
                        .rotationEffect(rotation)
                        .frame(width: 300, height: 300)
                        .clipShape(RoundedRectangle(cornerRadius: 16))
                        .gesture(
                            SimultaneousGesture(
                                MagnificationGesture()
                                    .onChanged { value in
                                        scale = lastScaleValue * value
                                    }
                                    .onEnded { _ in
                                        lastScaleValue = scale
                                    },
                                DragGesture()
                                    .onChanged { value in
                                        offset = CGSize(
                                            width: lastOffsetValue.width + value.translation.width,
                                            height: lastOffsetValue.height + value.translation.height
                                        )
                                    }
                                    .onEnded { _ in
                                        lastOffsetValue = offset
                                    }
                            )
                        )
                }
                .clipped()
                
                // 编辑控制区域
                VStack(spacing: 16) {
                    // 缩放控制
                    VStack(spacing: 8) {
                        Text("缩放")
                            .font(.system(size: 16, weight: .medium))
                        
                        HStack {
                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    scale = max(0.5, scale - 0.1)
                                    lastScaleValue = scale
                                }
                            }) {
                                Image(systemName: "minus.circle")
                                    .font(.system(size: 24))
                            }
                            
                            Slider(value: $scale, in: 0.5...3.0)
                                .onChange(of: scale) { _, newValue in
                                    lastScaleValue = newValue
                                }
                            
                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    scale = min(3.0, scale + 0.1)
                                    lastScaleValue = scale
                                }
                            }) {
                                Image(systemName: "plus.circle")
                                    .font(.system(size: 24))
                            }
                        }
                    }
                    
                    // 旋转控制
                    HStack(spacing: 20) {
                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                rotation = Angle(degrees: rotation.degrees - 90)
                                lastRotationValue = rotation
                            }
                        }) {
                            VStack(spacing: 4) {
                                Image(systemName: "rotate.left")
                                    .font(.system(size: 20))
                                Text("左转")
                                    .font(.system(size: 12))
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                        }
                        
                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                rotation = Angle(degrees: rotation.degrees + 90)
                                lastRotationValue = rotation
                            }
                        }) {
                            VStack(spacing: 4) {
                                Image(systemName: "rotate.right")
                                    .font(.system(size: 20))
                                Text("右转")
                                    .font(.system(size: 12))
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                        }
                        
                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                scale = 1.0
                                offset = .zero
                                rotation = .zero
                                lastScaleValue = 1.0
                                lastOffsetValue = .zero
                                lastRotationValue = .zero
                            }
                        }) {
                            VStack(spacing: 4) {
                                Image(systemName: "arrow.counterclockwise")
                                    .font(.system(size: 20))
                                Text("重置")
                                    .font(.system(size: 12))
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                        }
                    }
                }
                .padding(.horizontal)
                
                Spacer()
            }
            .padding()
            .navigationTitle("编辑头像")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("取消", action: onCancel),
                trailing: Button("保存") {
                    // 生成编辑后的图像
                    let editedImage = generateEditedImage()
                    onSave(editedImage)
                }
                .fontWeight(.semibold)
            )
        }
    }
    
    private func generateEditedImage() -> UIImage {
        // 创建图形上下文来生成编辑后的图像
        let size = CGSize(width: 512, height: 512) // 服务器要求的头像尺寸
        let renderer = UIGraphicsImageRenderer(size: size)
        
        return renderer.image { context in
            // 设置方形裁剪区域（不使用圆形裁剪）
            let rect = CGRect(origin: .zero, size: size)
            context.cgContext.clip(to: rect)
            
            // 保存当前图形状态
            context.cgContext.saveGState()
            
            // 计算变换 - 移动到中心点
            let center = CGPoint(x: size.width / 2, y: size.height / 2)
            context.cgContext.translateBy(x: center.x, y: center.y)
            
            // 应用用户的偏移
            context.cgContext.translateBy(x: offset.width, y: offset.height)
            
            // 应用旋转
            context.cgContext.rotate(by: CGFloat(rotation.radians))
            
            // 应用缩放
            context.cgContext.scaleBy(x: scale, y: scale)
            
            // 计算图像的绘制尺寸和位置
            // 确保图像填充整个圆形区域
            let imageSize = image.size
            let aspectRatio = imageSize.width / imageSize.height
            
            var drawSize: CGSize
            if aspectRatio > 1 {
                // 宽图：以高度为基准
                drawSize = CGSize(width: size.height * aspectRatio, height: size.height)
            } else {
                // 高图或正方形：以宽度为基准
                drawSize = CGSize(width: size.width, height: size.width / aspectRatio)
            }
            
            let drawRect = CGRect(
                x: -drawSize.width / 2,
                y: -drawSize.height / 2,
                width: drawSize.width,
                height: drawSize.height
            )
            
            // 绘制图像
            image.draw(in: drawRect)
            
            // 恢复图形状态
            context.cgContext.restoreGState()
        }
    }
}

// MARK: - Compact Avatar Selector
struct CompactAvatarSelector: View {
    @Binding var selectedAvatar: UIImage?
    let avatarUrl: String?
    let onTap: () -> Void
    
    @Environment(\.colorScheme) var colorScheme
    @State private var serverAvatar: UIImage?
    @State private var isLoadingServerAvatar = false
    
    init(selectedAvatar: Binding<UIImage?>, avatarUrl: String? = nil, onTap: @escaping () -> Void) {
        self._selectedAvatar = selectedAvatar
        self.avatarUrl = avatarUrl
        self.onTap = onTap
    }
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // 头像预览
                Group {
                    if let avatar = selectedAvatar {
                        // 优先显示用户选择的头像
                        Image(uiImage: avatar)
                            .resizable()
                            .scaledToFill()
                            .frame(width: 50, height: 50)
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(AppTheme.primaryColor(for: colorScheme), lineWidth: 2)
                            )
                    } else if let serverAvatar = serverAvatar {
                        // 显示从服务器加载的头像
                        Image(uiImage: serverAvatar)
                            .resizable()
                            .scaledToFill()
                            .frame(width: 50, height: 50)
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.gray.opacity(0.5), lineWidth: 1)
                            )
                    } else if isLoadingServerAvatar {
                        // 加载中状态
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.gray.opacity(0.1))
                            .frame(width: 50, height: 50)
                            .overlay(
                                ProgressView()
                                    .scaleEffect(0.8)
                            )
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                            )
                    } else {
                        // 默认占位符
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.gray.opacity(0.1))
                            .frame(width: 50, height: 50)
                            .overlay(
                                Image(systemName: "cat.fill")
                                    .font(.system(size: 20))
                                    .foregroundColor(.gray)
                            )
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                            )
                    }
                }
                
                // 文本信息
                VStack(alignment: .leading, spacing: 4) {
                    Text("猫咪头像")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.primary)
                    
                    Text(hasAvatar ? "点击编辑头像" : "点击设置头像")
                        .font(.system(size: 14))
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // 箭头图标
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.gray)
            }
            .padding(.vertical, 12)
            .padding(.horizontal, 16)
            .background(Color.gray.opacity(0.05))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
        .onAppear {
            loadServerAvatarIfNeeded()
        }
        .onChange(of: avatarUrl) { _, _ in
            loadServerAvatarIfNeeded()
        }
    }
    
    private var hasAvatar: Bool {
        return selectedAvatar != nil || serverAvatar != nil
    }
    
    private func loadServerAvatarIfNeeded() {
        // 如果已经有用户选择的头像，不需要加载服务器头像
        guard selectedAvatar == nil,
              let avatarUrl = avatarUrl,
              !avatarUrl.isEmpty else { return }
        
        isLoadingServerAvatar = true
        
        Task {
            do {
                // 添加缓存破坏参数
                var urlStringWithCacheBuster = avatarUrl
                if !urlStringWithCacheBuster.contains("?") {
                    urlStringWithCacheBuster += "?v=\(Int(Date().timeIntervalSince1970))"
                } else {
                    urlStringWithCacheBuster += "&v=\(Int(Date().timeIntervalSince1970))"
                }
                
                guard let url = URL(string: urlStringWithCacheBuster) else { return }
                
                let imageData = try await NetworkManager.shared.fetchData(url, contentType: .image)
                if let image = UIImage(data: imageData) {
                    await MainActor.run {
                        self.serverAvatar = image
                        self.isLoadingServerAvatar = false
                        Log.debug("🖼️ CompactAvatarSelector 已加载服务器头像")
                    }
                }
            } catch {
                await MainActor.run {
                    self.isLoadingServerAvatar = false
                }
                Log.error("🖼️ CompactAvatarSelector 加载服务器头像失败: \(error.localizedDescription)")
            }
        }
    }
}