import SwiftUI

/// 设备行视图
struct DeviceRow: View {
    let device: DeviceStatusResponse
    let isSelected: Bool
    let onRefreshStatus: ((String) async -> Void)?
    let isAutoRefreshing: Bool
    @Environment(\.colorScheme) var colorScheme
    @State private var isRefreshing = false
    @EnvironmentObject private var deviceManager: DeviceAndGroupManager
    
    init(device: DeviceStatusResponse, 
         isSelected: Bool, 
         onRefreshStatus: ((String) async -> Void)? = nil,
         isAutoRefreshing: Bool = false) {
        self.device = device
        self.isSelected = isSelected
        self.onRefreshStatus = onRefreshStatus
        self.isAutoRefreshing = isAutoRefreshing
    }
    
    var body: some View {
        HStack {
            // 设备图标
            Image(systemName: "video.fill")
                .font(.title3)
                .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                .frame(width: 32, height: 32)
                .background(AppTheme.primaryColor(for: colorScheme).opacity(0.1))
                .cornerRadius(8)
            
            // 设备信息
            VStack(alignment: .leading, spacing: 4) {
                Text(device.name)
                    .font(.headline)
                    .foregroundColor(AppTheme.textColor(for: colorScheme))
                    .lineLimit(1)
                
                Text(device.model)
                    .font(.subheadline)
                    .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                    .lineLimit(1)
                
                // 显示固件版本和位置信息
                HStack {
                    Text("v\(device.firmware)")
                        .font(.caption)
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                    
                    Spacer()
                    
                    Text(device.location)
                        .font(.caption)
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                }
                
                // 🔄 新增：传感器状态显示
                SensorStatusView(deviceId: device.id, size: .small)
            }
            
            Spacer()
            
            // 状态和操作区域
            VStack(alignment: .trailing, spacing: 6) {
                // 设备在线状态
                HStack(spacing: 4) {
                    Circle()
                        .fill(device.online == true ? Color.green : device.online == false ? Color.red : Color.gray)
                        .frame(width: 8, height: 8)
                    
                    Text(device.online == true ? "在线" : device.online == false ? "离线" : "未知")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(device.online == true ? .green : device.online == false ? .red : .gray)
                }
                
                OTAStatusIndicator(deviceId: device.id, size: .small)

                // 最后心跳时间
                if device.lastHeartbeat != nil {
                    Text(device.formattedLastHeartbeat)
                        .font(.caption2)
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                        .lineLimit(1)
                }
                
                // 刷新按钮和状态指示器
                HStack(spacing: 4) {
                    // 自动刷新状态指示器
                    if isAutoRefreshing {
                        ProgressView()
                            .scaleEffect(0.6)
                            .progressViewStyle(CircularProgressViewStyle(tint: AppTheme.primaryColor(for: colorScheme)))
                    } else if isRefreshing {
                        ProgressView()
                            .scaleEffect(0.6)
                            .progressViewStyle(CircularProgressViewStyle(tint: .blue))
                    }
                    
                    // 手动刷新按钮
                    Button(action: {
                        Task {
                            await refreshDevice()
                        }
                    }) {
                        Image(systemName: "arrow.clockwise")
                            .font(.caption)
                            .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                    }
                    .disabled(isRefreshing || isAutoRefreshing)
                }
            }
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isSelected ? AppTheme.primaryColor(for: colorScheme).opacity(0.1) : Color.clear)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(isSelected ? AppTheme.primaryColor(for: colorScheme) : Color.clear, lineWidth: 1)
                )
        )
        .onAppear {
            // 设备行出现时获取传感器状态
            Task {
                await deviceManager.fetchSensorStatus(for: device.id)
            }
        }
    }
    
    private func refreshDevice() async {
        isRefreshing = true
        defer { isRefreshing = false }
        
        // 刷新设备状态
        await onRefreshStatus?(device.id)
        
        // 同时刷新传感器状态
        await deviceManager.fetchSensorStatus(for: device.id)
    }
}

/// 传感器状态显示组件
struct SensorStatusView: View {
    let deviceId: String
    let size: SensorStatusSize
    @Environment(\.colorScheme) var colorScheme
    @EnvironmentObject private var deviceManager: DeviceAndGroupManager
    
    enum SensorStatusSize {
        case small
        case medium
        case large
        
        var iconSize: CGFloat {
            switch self {
            case .small: return 12
            case .medium: return 16
            case .large: return 20
            }
        }
        
        var font: Font {
            switch self {
            case .small: return .caption2
            case .medium: return .caption
            case .large: return .footnote
            }
        }
    }
    
    var body: some View {
        if let sensorStatus = deviceManager.getSensorStatus(for: deviceId) {
            if sensorStatus.hasErrors {
                HStack(spacing: 4) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.system(size: size.iconSize))
                        .foregroundColor(.orange)
                    
                    Text("\(sensorStatus.errorCount) 传感器异常")
                        .font(size.font)
                        .foregroundColor(.orange)
                }
            } else {
                HStack(spacing: 4) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: size.iconSize))
                        .foregroundColor(.green)
                    
                    Text("传感器正常")
                        .font(size.font)
                        .foregroundColor(.green)
                }
            }
        } else if deviceManager.isFetchingSensorStatus.contains(deviceId) {
            HStack(spacing: 4) {
                ProgressView()
                    .scaleEffect(0.6)
                    .progressViewStyle(CircularProgressViewStyle(tint: AppTheme.secondaryTextColor(for: colorScheme)))
                
                Text("检查传感器...")
                    .font(size.font)
                    .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
            }
        } else if deviceManager.sensorStatusErrors[deviceId] != nil {
            HStack(spacing: 4) {
                Image(systemName: "questionmark.circle.fill")
                    .font(.system(size: size.iconSize))
                    .foregroundColor(.gray)
                
                Text("传感器未知")
                    .font(size.font)
                    .foregroundColor(.gray)
            }
        } else {
            // 默认状态 - 不显示任何内容或显示占位符
            EmptyView()
        }
    }
}

// MARK: - Preview
struct DeviceRow_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 16) {
            // 在线设备
            DeviceRow(
                device: DeviceStatusResponse(
                    id: "device1",
                    name: "客厅猫厕所",
                    model: "CabyPro 2023",
                    firmware: "1.2.3",
                    online: true,
                    lastHeartbeat: "2023-06-01T15:30:00Z",
                    ipv4: "*************",
                    ipv6: "2001:db8::1"
                ),
                isSelected: false,
                onRefreshStatus: { _ in
                    // 模拟刷新操作
                    try? await Task.sleep(nanoseconds: 1_000_000_000)
                }
            )
            
            // 离线设备（已选中）
            DeviceRow(
                device: DeviceStatusResponse(
                    id: "device2",
                    name: "卧室猫厕所设备名称很长的情况",
                    model: "CabyLite 2023",
                    firmware: "1.1.0",
                    online: false,
                    lastHeartbeat: "2023-06-01T12:00:00Z",
                    ipv4: nil,
                    ipv6: nil
                ),
                isSelected: true,
                onRefreshStatus: { _ in
                    // 模拟刷新操作
                    try? await Task.sleep(nanoseconds: 1_000_000_000)
                },
                isAutoRefreshing: true
            )
        }
        .padding()
        .background(Color(.systemGroupedBackground))
    }
} 
