import SwiftUI

/// OTA状态指示器组件
struct OTAStatusIndicator: View {
    let deviceId: String
    let size: Size
    @StateObject private var otaManager = DeviceOTAStatusManager.shared
    @Environment(\.colorScheme) var colorScheme
    
    enum Size {
        case small   // 用于设备行
        case medium  // 用于卡片
        case large   // 用于详情页
        
        var iconSize: Font {
            switch self {
            case .small: return .caption
            case .medium: return .footnote
            case .large: return .title3
            }
        }
        
        var textSize: Font {
            switch self {
            case .small: return .caption2
            case .medium: return .caption
            case .large: return .footnote
            }
        }
        
        var spacing: CGFloat {
            switch self {
            case .small: return 2
            case .medium: return 4
            case .large: return 6
            }
        }
    }
    
    var body: some View {
        HStack(spacing: size.spacing) {
            if let otaStatus = otaManager.getOTAStatus(for: deviceId) {
                // OTA状态图标
                Image(systemName: otaStatus.status.systemImage)
                    .font(size.iconSize)
                    .foregroundColor(statusColor(otaStatus.status))
                    .rotationEffect(.degrees(otaStatus.status == .updating ? 360 : 0))
                    .animation(
                        otaStatus.status == .updating ?
                            .linear(duration: 2).repeatForever(autoreverses: false) :
                            .default,
                        value: otaStatus.status == .updating
                    )
                
                // 状态文本
                if size != .small {
                    Text(otaStatus.status.displayName)
                        .font(size.textSize)
                        .foregroundColor(statusColor(otaStatus.status))
                        .fontWeight(.medium)
                }
            } else if otaManager.isLoading.contains(deviceId) {
                // 加载状态
                Image(systemName: "circle.dotted")
                    .font(size.iconSize)
                    .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                    .rotationEffect(.degrees(360))
                    .animation(.linear(duration: 1).repeatForever(autoreverses: false), value: true)
                
                if size != .small {
                    Text("检查中")
                        .font(size.textSize)
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                }
            } else {
                // 无状态或错误状态
                Image(systemName: "circle")
                    .font(size.iconSize)
                    .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme).opacity(0.5))
                
                if size != .small {
                    Text("未知")
                        .font(size.textSize)
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                }
            }
        }
        .onAppear {
            // 页面出现时检查OTA状态
            Task {
                await otaManager.refreshOTAStatus(for: deviceId)
            }
        }
    }
    
    private func statusColor(_ status: DeviceOTAStatus) -> Color {
        switch status {
        case .idle:
            return AppTheme.secondaryTextColor(for: colorScheme)
        case .updating:
            return .blue
        case .failed:
            return .red
        case .completed:
            return .green
        }
    }
}

// MARK: - 详细的OTA状态视图

/// 详细的OTA状态视图（用于详情页面）
struct DetailedOTAStatusView: View {
    let deviceId: String
    @StateObject private var otaManager = DeviceOTAStatusManager.shared
    @Environment(\.colorScheme) var colorScheme
    @State private var isRefreshing = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题行
            HStack {
                Text("OTA升级状态")
                    .font(.headline)
                    .foregroundColor(AppTheme.textColor(for: colorScheme))
                
                Spacer()
                
                // 刷新按钮
                Button(action: refreshStatus) {
                    Image(systemName: "arrow.clockwise")
                        .font(.caption)
                        .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                        .rotationEffect(.degrees(isRefreshing ? 360 : 0))
                        .animation(
                            isRefreshing ?
                                .linear(duration: 1).repeatForever(autoreverses: false) :
                                .default,
                            value: isRefreshing
                        )
                }
                .disabled(isRefreshing)
            }
            
            if let otaStatus = otaManager.getOTAStatus(for: deviceId) {
                // 状态卡片
                VStack(spacing: 16) {
                    // 主要状态显示
                    HStack(spacing: 12) {
                        Image(systemName: otaStatus.status.systemImage)
                            .font(.title2)
                            .foregroundColor(statusColor(otaStatus.status))
                            .rotationEffect(.degrees(otaStatus.status == .updating ? 360 : 0))
                            .animation(
                                otaStatus.status == .updating ?
                                    .linear(duration: 2).repeatForever(autoreverses: false) :
                                    .default,
                                value: otaStatus.status == .updating
                            )
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text(otaStatus.status.displayName)
                                .font(.title3)
                                .fontWeight(.semibold)
                                .foregroundColor(statusColor(otaStatus.status))
                            
                            Text("最后更新: \(otaStatus.relativeTime)")
                                .font(.caption)
                                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                        }
                        
                        Spacer()
                    }
                    
                    // 状态详情
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("更新时间")
                                .font(.caption)
                                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                            Spacer()
                            Text(otaStatus.formattedLastUpdated)
                                .font(.caption)
                                .foregroundColor(AppTheme.textColor(for: colorScheme))
                        }
                        
                        if otaStatus.status == .updating {
                            HStack {
                                Text("预计完成")
                                    .font(.caption)
                                    .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                                Spacer()
                                Text("约3-5分钟")
                                    .font(.caption)
                                    .foregroundColor(.blue)
                            }
                        }
                    }
                    .padding(.top, 8)
                    .overlay(
                        Rectangle()
                            .frame(height: 1)
                            .foregroundColor(AppTheme.primaryColor(for: colorScheme).opacity(0.1)),
                        alignment: .top
                    )
                }
                .padding(.all, 16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(statusColor(otaStatus.status).opacity(0.05))
                        .stroke(statusColor(otaStatus.status).opacity(0.2), lineWidth: 1)
                )
                
                // 状态说明
                statusDescription(for: otaStatus.status)
                
            } else if otaManager.isLoading.contains(deviceId) {
                // 加载状态
                HStack {
                    Spacer()
                    ProgressView("正在获取OTA状态...")
                        .font(.caption)
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                    Spacer()
                }
                .padding(.vertical, 20)
                
            } else if let error = otaManager.errors[deviceId] {
                // 错误状态
                VStack(spacing: 8) {
                    HStack {
                        Image(systemName: "exclamationmark.triangle")
                            .foregroundColor(.orange)
                        Text("无法获取OTA状态")
                            .font(.subheadline)
                            .foregroundColor(AppTheme.textColor(for: colorScheme))
                        Spacer()
                    }
                    
                    Text(error.localizedDescription)
                        .font(.caption)
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
                .padding(.all, 12)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.orange.opacity(0.1))
                        .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                )
            } else {
                // 初始状态
                HStack {
                    Spacer()
                    VStack(spacing: 8) {
                        Image(systemName: "arrow.down.circle")
                            .font(.title2)
                            .foregroundColor(AppTheme.primaryColor(for: colorScheme).opacity(0.6))
                        Text("点击刷新按钮获取OTA状态")
                            .font(.caption)
                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                    }
                    Spacer()
                }
                .padding(.vertical, 20)
            }
        }
        .onAppear {
            // 页面出现时自动获取状态
            Task {
                await otaManager.refreshOTAStatus(for: deviceId)
            }
        }
    }
    
    private func refreshStatus() {
        isRefreshing = true
        Task {
            await otaManager.refreshOTAStatus(for: deviceId)
            await MainActor.run {
                isRefreshing = false
            }
        }
    }
    
    private func statusColor(_ status: DeviceOTAStatus) -> Color {
        switch status {
        case .idle:
            return AppTheme.secondaryTextColor(for: colorScheme)
        case .updating:
            return .blue
        case .failed:
            return .red
        case .completed:
            return .green
        }
    }
    
    @ViewBuilder
    private func statusDescription(for status: DeviceOTAStatus) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            switch status {
            case .idle:
                Text("设备当前处于空闲状态，没有进行固件升级。")
                    .font(.caption)
                    .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                
            case .updating:
                VStack(alignment: .leading, spacing: 4) {
                    Text("设备正在进行固件升级，请勿断开电源。")
                        .font(.caption)
                        .foregroundColor(.blue)
                        .fontWeight(.medium)
                    
                    Text("• 升级过程中设备会暂时离线并重启，这是正常现象")
                        .font(.caption)
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                    
                    Text("• 设备离线状态不影响固件升级进程")
                        .font(.caption)
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                    
                    Text("• 升级完成后设备将自动恢复在线状态")
                        .font(.caption)
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                }
                
            case .failed:
                VStack(alignment: .leading, spacing: 4) {
                    Text("固件升级失败，请检查网络连接后重试。")
                        .font(.caption)
                        .foregroundColor(.red)
                        .fontWeight(.medium)
                    
                    Text("• 确保设备电源稳定")
                        .font(.caption)
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                    
                    Text("• 检查网络连接是否正常")
                        .font(.caption)
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                    
                    Text("• 如果问题持续出现，请联系客服支持")
                        .font(.caption)
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                }
                
            case .completed:
                VStack(alignment: .leading, spacing: 4) {
                    Text("固件升级已完成，设备将自动重启。")
                        .font(.caption)
                        .foregroundColor(.green)
                        .fontWeight(.medium)
                    
                    Text("• 设备正在重启中，请等待1-2分钟")
                        .font(.caption)
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                    
                    Text("• 重启完成后将自动恢复在线状态")
                        .font(.caption)
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                    
                    Text("• 升级后新功能和改进将生效")
                        .font(.caption)
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                }
            }
        }
        .padding(.top, 8)
    }
}

// MARK: - Preview
struct OTAStatusIndicator_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            HStack {
                Text("小尺寸:")
                Spacer()
                OTAStatusIndicator(deviceId: "test1", size: .small)
            }
            
            HStack {
                Text("中等尺寸:")
                Spacer()
                OTAStatusIndicator(deviceId: "test2", size: .medium)
            }
            
            HStack {
                Text("大尺寸:")
                Spacer()
                OTAStatusIndicator(deviceId: "test3", size: .large)
            }
            
            Divider()
            
            DetailedOTAStatusView(deviceId: "test4")
        }
        .padding()
    }
} 