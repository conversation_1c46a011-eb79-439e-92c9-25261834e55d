import SwiftUI

// MARK: - Cat Avatar View
struct CatAvatarView: View {
    let avatarUrl: String?
    let size: CGFloat
    let cornerRadius: CGFloat
    let refreshTrigger: Int  // 用于触发刷新的计数器
    
    @State private var image: UIImage?
    @State private var isLoading = false
    @State private var loadError = false
    @State private var lastLoadedUrl: String = ""  // 记录最后成功加载的URL
    @Environment(\.colorScheme) var colorScheme
    
    init(avatarUrl: String?, size: CGFloat = 60, cornerRadius: CGFloat = 12, refreshTrigger: Int = 0) {
        self.avatarUrl = avatarUrl
        self.size = size
        self.cornerRadius = cornerRadius
        self.refreshTrigger = refreshTrigger
    }
    
    var body: some View {
        Group {
            if let image = image {
                Image(uiImage: image)
                    .resizable()
                    .scaledToFill()
                    .frame(width: size, height: size)
                    .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
            } else {
                avatarPlaceholder
            }
        }
        .onAppear {
            loadImageIfNeeded()
        }
        .onChange(of: avatarUrl) { _, newUrl in
            // URL 变化时清除缓存并重新加载
            if newUrl != lastLoadedUrl {
                image = nil
                lastLoadedUrl = ""
                loadImageIfNeeded()
            }
        }
        .onChange(of: refreshTrigger) { _, _ in
            // 刷新触发时，在后台静默加载新图片
            loadImageSilently()
        }
    }
    
    private var avatarPlaceholder: some View {
        RoundedRectangle(cornerRadius: cornerRadius)
            .fill(AppTheme.primaryColor(for: colorScheme).opacity(0.1))
            .frame(width: size, height: size)
            .overlay(
                Group {
                    if isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                            .tint(AppTheme.primaryColor(for: colorScheme))
                    } else {
                        Image(systemName: loadError ? "exclamationmark.triangle.fill" : "cat.fill")
                            .font(.system(size: size * 0.4))
                            .foregroundColor(AppTheme.primaryColor(for: colorScheme).opacity(0.6))
                    }
                }
            )
    }
    
    private func loadImageIfNeeded() {
        guard let avatarUrl = avatarUrl, 
              !avatarUrl.isEmpty else { return }
        
        // 如果已有图片且URL相同，不重新加载
        if image != nil && lastLoadedUrl == avatarUrl {
            return
        }
        
        guard let url = URL(string: avatarUrl) else { return }
        loadAuthenticatedImage(from: url, isInitialLoad: image == nil)
    }
    
    private func loadImageSilently() {
        guard let avatarUrl = avatarUrl, 
              !avatarUrl.isEmpty else { return }
        
        // 静默刷新：添加缓存破坏参数，但保持当前图片显示
        var urlString = avatarUrl
        if refreshTrigger > 0 {
            if !urlString.contains("?") {
                urlString += "?v=\(refreshTrigger)"
            } else {
                urlString += "&v=\(refreshTrigger)"
            }
        }
        
        guard let url = URL(string: urlString) else { return }
        loadAuthenticatedImage(from: url, isInitialLoad: false)
    }
    
    private func loadAuthenticatedImage(from url: URL, isInitialLoad: Bool = true) {
        // 只有在初始加载时才显示加载状态
        if isInitialLoad {
            isLoading = true
            loadError = false
        }
        
        Task {
            do {
                // 使用NetworkManager的fetchData方法获取原始图片数据
                let imageData = try await NetworkManager.shared.fetchData(
                    url,
                    method: .get,
                    contentType: .image
                )
                
                await MainActor.run {
                    if let uiImage = UIImage(data: imageData) {
                        self.image = uiImage
                        self.lastLoadedUrl = self.avatarUrl ?? ""
                        self.loadError = false
                        Log.debug("✅ 猫咪头像加载成功: \(url.absoluteString)")
                    } else {
                        // 静默刷新时，如果加载失败不更新错误状态（保持当前图片）
                        if isInitialLoad {
                            self.loadError = true
                            Log.error("🖼️ 无法从数据创建图片")
                        }
                    }
                    
                    if isInitialLoad {
                        self.isLoading = false
                    }
                }
            } catch {
                Log.error("❌ 加载猫咪头像失败: \(error.localizedDescription)")
                await MainActor.run {
                    // 静默刷新时，如果加载失败不更新错误状态（保持当前图片）
                    if isInitialLoad {
                        self.loadError = true
                        self.isLoading = false
                    }
                }
            }
        }
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 20) {
        HStack(spacing: 20) {
            CatAvatarView(avatarUrl: nil, size: 60)
            CatAvatarView(avatarUrl: "", size: 60)
            CatAvatarView(avatarUrl: "https://example.com/cat.jpg", size: 60)
        }
        
        HStack(spacing: 20) {
            CatAvatarView(avatarUrl: nil, size: 40, cornerRadius: 8)
            CatAvatarView(avatarUrl: nil, size: 80, cornerRadius: 16)
            CatAvatarView(avatarUrl: nil, size: 100, cornerRadius: 20)
        }
    }
    .padding()
} 