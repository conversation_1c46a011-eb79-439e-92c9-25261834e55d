import SwiftUI

struct RefreshHintView: View {
    @State private var isAnimating = false
    
    var body: some View {
        HStack {
            Image(systemName: "arrow.clockwise")
                .font(.system(size: 14, weight: .medium))
                .rotationEffect(.degrees(isAnimating ? 360 : 0))
                .animation(
                    .linear(duration: 2.0).repeatF<PERSON>ver(autoreverses: false),
                    value: isAnimating
                )
            
            VStack(alignment: .leading, spacing: 2) {
                Text("下拉刷新最新记录")
                    .font(.caption)
                    .fontWeight(.medium)
                
                Text("获取最新日期的视频数据")
                    .font(.caption2)
                    .opacity(0.7)
            }
        }
        .foregroundColor(.secondary)
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .padding(.horizontal, 16)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(.regularMaterial)
                .shadow(color: .black.opacity(0.05), radius: 2, y: 1)
        )
        .onAppear {
            isAnimating = true
        }
        .onDisappear {
            isAnimating = false
        }
    }
} 