import SwiftUI
import PhotosUI
import UIKit
import Vision

// MARK: - Intro Page
struct IntroPage: View {
    @Environment(\.colorScheme) var colorScheme
    var body: some View {
        VStack(spacing: 0) {
            Spacer()
            
            // Cat icon
            Image(systemName: "cat.fill")
                .font(.system(size: 70))
                .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                .padding(.bottom, 30)
            
            // Title
            Text(NSLocalizedString(CatProfileCreation.LocalizationKey.introTitle, comment: ""))
                .font(.system(size: 24, weight: .bold))
                .multilineTextAlignment(.center)
                .padding(.bottom, 16)
            
            // Description
            Text(NSLocalizedString(CatProfileCreation.LocalizationKey.introDescription, comment: ""))
                .font(.system(size: 17))
                .multilineTextAlignment(.leading)
                .foregroundColor(.secondary)
                .lineSpacing(4)
                .padding(.horizontal, 24)
                .padding(.top, 20)
            
            Spacer()
            Spacer()
        }
    }
}

// MARK: - Basic Info Page
struct BasicInfoPage: View {
    @Binding var profile: CatProfileCreation
    @FocusState private var isNameFieldFocused: Bool
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        VStack(spacing: 0) {
            // Heading
            Text(NSLocalizedString("cat_creation_basic_info", comment: ""))
                .font(.system(size: 22, weight: .bold))
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.top, 12)
                .padding(.bottom, 30)
            
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // Name field
                    VStack(alignment: .leading, spacing: 10) {
                        Text(NSLocalizedString(CatProfileCreation.LocalizationKey.nameLabel, comment: ""))
                            .font(.system(size: 17, weight: .semibold))
                        
                        ZStack(alignment: .trailing) {
                            TextField("", text: $profile.name)
                                .padding()
                                .background(Color.gray.opacity(0.1))
                                .cornerRadius(10)
                                .focused($isNameFieldFocused)
                            
                            if profile.name.isEmpty {
                                Text("*")
                                    .foregroundColor(.red)
                                    .padding(.trailing, 16)
                            }
                        }
                    }
                    
                    // Gender selection
                    VStack(alignment: .leading, spacing: 10) {
                        Text(NSLocalizedString(CatProfileCreation.LocalizationKey.genderLabel, comment: ""))
                            .font(.system(size: 17, weight: .semibold))
                        
                        HStack(spacing: 0) {
                            ForEach([CatProfileCreation.Gender.male, .female, .unknown], id: \.self) { gender in
                                Button(action: {
                                    profile.gender = gender
                                }) {
                                    Text(NSLocalizedString("cat_creation_\(gender.rawValue)", comment: ""))
                                        .frame(maxWidth: .infinity)
                                        .padding(.vertical, 12)
                                        .background(profile.gender == gender ? AppTheme.primaryColor(for: colorScheme) : Color.gray.opacity(0.1))
                                        .foregroundColor(profile.gender == gender ? AppTheme.buttonTextColor(for: colorScheme) : .primary)
                                }
                                .buttonStyle(PlainButtonStyle())
                                .cornerRadius(10)
                                .padding(.horizontal, 2)
                            }
                        }
                    }
                    
                    // Neutered status
                    VStack(alignment: .leading, spacing: 10) {
                        Text(NSLocalizedString(CatProfileCreation.LocalizationKey.neuteredLabel, comment: ""))
                            .font(.system(size: 17, weight: .semibold))
                        
                        HStack(spacing: 0) {
                            ForEach([CatProfileCreation.NeuteredStatus.neutered, .notNeutered, .unknown], id: \.self) { status in
                                Button(action: {
                                    profile.neuteredStatus = status
                                }) {
                                    Text(NSLocalizedString("cat_creation_\(status.rawValue)", comment: ""))
                                        .frame(maxWidth: .infinity)
                                        .padding(.vertical, 12)
                                        .background(profile.neuteredStatus == status ? AppTheme.primaryColor(for: colorScheme) : Color.gray.opacity(0.1))
                                        .foregroundColor(profile.neuteredStatus == status ? AppTheme.buttonTextColor(for: colorScheme) : .primary)
                                }
                                .buttonStyle(PlainButtonStyle())
                                .cornerRadius(10)
                                .padding(.horizontal, 2)
                            }
                        }
                    }
                    
                    // Required field note
                    Text(NSLocalizedString(CatProfileCreation.LocalizationKey.nameRequired, comment: ""))
                        .font(.system(size: 14))
                        .foregroundColor(.secondary)
                        .padding(.top, 8)
                }
            }
        }
        .padding(.horizontal)
        .onAppear {
            // Auto-focus name field when page appears
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                isNameFieldFocused = true
            }
        }
    }
}

// MARK: - Physical Info Page
struct PhysicalInfoPage: View {
    @Binding var profile: CatProfileCreation
    @Environment(\.colorScheme) var colorScheme
    @State private var isWeightUnknown = false
    @State private var isBirthDateUnknown = false
    @State private var showDatePicker = false
    
    var body: some View {
        VStack(spacing: 0) {
            // Previous page summary
            // VStack(alignment: .leading, spacing: 8) {
            //     Text(NSLocalizedString("cat_creation_basic_info", comment: ""))
            //         .font(.system(size: 16, weight: .semibold))
            //         .foregroundColor(.primary)
                
            //     HStack(spacing: 12) {
            //         Circle().fill(Color.secondary.opacity(0.5)).frame(width: 6, height: 6)
            //         Text(profile.name.isEmpty ? "-" : profile.name)
            //             .font(.system(size: 17, weight: .semibold))
                    
            //         if profile.gender != .unknown {
            //             HStack(spacing: 4) {
            //                 Circle().fill(Color.secondary.opacity(0.5)).frame(width: 6, height: 6)
            //                 Text(NSLocalizedString("cat_creation_\(profile.gender.rawValue)", comment: ""))
            //                     .font(.system(size: 15)).foregroundColor(.secondary)
            //             }
            //         }
                    
            //         if profile.neuteredStatus != .unknown {
            //             HStack(spacing: 4) {
            //                 Circle().fill(Color.secondary.opacity(0.5)).frame(width: 6, height: 6)
            //                 Text(NSLocalizedString("cat_creation_\(profile.neuteredStatus.rawValue)", comment: ""))
            //                     .font(.system(size: 15)).foregroundColor(.secondary)
            //             }
            //         }
            //         Spacer()
            //     }
            //     .frame(maxWidth: .infinity, alignment: .leading)
            // }
            // .padding(EdgeInsets(top: 12, leading: 16, bottom: 12, trailing: 16))
            // .background(Color.gray.opacity(0.1))
            // .cornerRadius(12)
            // .padding(.horizontal, 16)
            // .padding(.vertical, 12)
            
            // Heading
            Text(NSLocalizedString("cat_creation_physical_info", comment: ""))
                .font(.system(size: 22, weight: .bold))
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.vertical, 12)
            
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // Weight field
                    VStack(alignment: .leading, spacing: 10) {
                        Text(NSLocalizedString(CatProfileCreation.LocalizationKey.weightLabel, comment: ""))
                            .font(.system(size: 17, weight: .semibold))
                        
                        HStack {
                            if isWeightUnknown {
                                Text(NSLocalizedString("cat_creation_unknown", comment: ""))
                                    .padding()
                                    .frame(maxWidth: .infinity, alignment: .leading)
                                    .background(Color.gray.opacity(0.1))
                                    .cornerRadius(10)
                                    .foregroundColor(.secondary)
                            } else {
                                HStack {
                                    TextField("", value: $profile.weight, format: .number)
                                        .keyboardType(.decimalPad)
                                        .padding()
                                        .background(Color.gray.opacity(0.1))
                                        .cornerRadius(10)
                                    
                                    Text(NSLocalizedString(CatProfileCreation.LocalizationKey.weightUnit, comment: ""))
                                        .foregroundColor(.secondary)
                                        .padding(.leading, 4)
                                }
                            }
                            
                            Button(action: {
                                isWeightUnknown.toggle()
                                if isWeightUnknown {
                                    profile.weight = nil
                                }
                            }) {
                                Text(NSLocalizedString(CatProfileCreation.LocalizationKey.unknown, comment: ""))
                                    .font(.system(size: 15))
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 8)
                                    .background(isWeightUnknown ? AppTheme.primaryColor(for: colorScheme) : Color.gray.opacity(0.2))
                                    .foregroundColor(isWeightUnknown ? AppTheme.buttonTextColor(for: colorScheme) : .primary)
                                    .cornerRadius(8)
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                    
                    // Birth date field
                    VStack(alignment: .leading, spacing: 10) {
                        Text(NSLocalizedString(CatProfileCreation.LocalizationKey.birthDateLabel, comment: ""))
                            .font(.system(size: 17, weight: .semibold))
                        
                        HStack(spacing: 10) {
                            Group {
                                if isBirthDateUnknown {
                                    Text(NSLocalizedString("cat_creation_unknown", comment: ""))
                                        .padding()
                                        .frame(maxWidth: .infinity, alignment: .leading)
                                        .background(Color.gray.opacity(0.1))
                                        .cornerRadius(10)
                                        .foregroundColor(.secondary)
                                } else {
                                    Button(action: {
                                        showDatePicker.toggle()
                                        if showDatePicker && profile.birthDate == nil {
                                            profile.birthDate = Date() // Default to today if activating picker and no date set
                                        }
                                    }) {
                                        Text(profile.birthDate.map { DateFormatter.localizedString(from: $0, dateStyle: .medium, timeStyle: .none) } ?? NSLocalizedString("cat_creation_birth_date_label", comment: "Tap to select"))
                                            .frame(maxWidth: .infinity, alignment: .leading)
                                            .padding()
                                            .background(Color.gray.opacity(0.1))
                                            .cornerRadius(10)
                                            .foregroundColor(profile.birthDate == nil && !showDatePicker ? .secondary : .primary)
                                    }
                                }
                            }.frame(maxWidth: .infinity)

                            Button(action: {
                                isBirthDateUnknown.toggle()
                                if isBirthDateUnknown {
                                    profile.birthDate = nil
                                    showDatePicker = false
                                }
                            }) {
                                Text(NSLocalizedString(CatProfileCreation.LocalizationKey.unknown, comment: ""))
                                    .font(.system(size: 15))
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 11)
                                    .background(isBirthDateUnknown ? AppTheme.primaryColor(for: colorScheme) : Color.gray.opacity(0.2))
                                    .foregroundColor(isBirthDateUnknown ? AppTheme.buttonTextColor(for: colorScheme) : .primary)
                                    .cornerRadius(8)
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                        if showDatePicker && !isBirthDateUnknown {
                            CustomBirthDatePicker(selectedDate: $profile.birthDate)
                                .onChange(of: profile.birthDate) { _, _ in
                                    showDatePicker = false
                                }
                                .padding(.top, 24)
                        }
                    }
                    
                    // Optional field note
                    Text(NSLocalizedString(CatProfileCreation.LocalizationKey.skip, comment: ""))
                        .font(.system(size: 17))
                        .foregroundColor(.secondary)
                        .lineSpacing(4)
                        .padding(.vertical, 16)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
        }
        .padding(.horizontal)
        .onAppear {
            // Initialize UI state based on profile data
            isWeightUnknown = (profile.weight == nil)
            isBirthDateUnknown = (profile.birthDate == nil)
        }
    }
}

// MARK: - Photos Page
struct PhotosPage: View {
    @Binding var profile: CatProfileCreation
    @ObservedObject var viewModel: CatProfileCreationViewModel
    @State private var selectedItems: [PhotosPickerItem] = []
    @State private var newPhotoIndices: Set<Int> = []
    @Namespace private var animation
    
    var body: some View {
        VStack(spacing: 0) {
            // Heading
            Text(NSLocalizedString("cat_creation_photo_info", comment: ""))
                .font(.system(size: 22, weight: .bold))
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.vertical, 12)
            
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    // Description
                    Text(NSLocalizedString(CatProfileCreation.LocalizationKey.photosDescription, comment: "")
                        .replacingOccurrences(of: "[replace_to_cat_name]", with: profile.name))
                        .font(.system(size: 17))
                        .foregroundColor(.secondary)
                        .lineSpacing(4)
                        .padding(.vertical, 16)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    // Photo grid
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 3), spacing: 8) {
                        ForEach(profile.validatedPhotos.indices, id: \.self) { index in
                            PhotoCell(
                                image: profile.validatedPhotos[index].image,
                                isValid: profile.validatedPhotos[index].isValid,
                                validationStatus: profile.validatedPhotos[index].validationStatus,
                                onDelete: {
                                    withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                                        viewModel.deletePhoto(at: index)
                                    }
                                }
                            )
                            .matchedGeometryEffect(id: index, in: animation)
                            .transition(
                                .asymmetric(
                                    insertion: .scale(scale: 0.7)
                                        .combined(with: .opacity)
                                        .animation(.spring(response: 0.3, dampingFraction: 0.7)),
                                    removal: .scale(scale: 0.7)
                                        .combined(with: .opacity)
                                        .animation(.spring(response: 0.3, dampingFraction: 0.8))
                                )
                            )
                        }
                        
                        if profile.validatedPhotos.count < 9 {
                            PhotoPickerButton(selectedItems: $selectedItems, remainingCount: 9 - profile.validatedPhotos.count)
                                .aspectRatio(1, contentMode: .fit)
                                .transition(.scale.combined(with: .opacity))
                        }
                    }
                    .animation(.spring(response: 0.3, dampingFraction: 0.7), value: profile.validatedPhotos.count)
                    
                    // Photo limit note
                    Text(NSLocalizedString(CatProfileCreation.LocalizationKey.maxPhotos, comment: ""))
                        .font(.system(size: 14))
                        .foregroundColor(.secondary)
                        .padding(.top, 8)
                }
            }
        }
        .padding(.horizontal)
        .onChange(of: selectedItems) { oldItems, newItems in
            Task {
                var images: [UIImage] = []
                for item in newItems {
                    if let data = try? await item.loadTransferable(type: Data.self),
                       let image = UIImage(data: data) {
                        images.append(image)
                    }
                }
                
                // Capture current count before adding new photos
                let startIndex = profile.validatedPhotos.count

                // Add photos through ViewModel
                viewModel.addPhotos(images)

                // Mark new photos for animation
                await MainActor.run {
                    for i in startIndex..<(startIndex + images.count) {
                        newPhotoIndices.insert(i)
                    }
                    // Remove indices after a delay
                    Task {
                        // try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
                        await MainActor.run {
                            newPhotoIndices.removeAll()
                        }
                    }
                }
                selectedItems = []
            }
        }
    }
}

// MARK: - Custom Photo Picker Button
struct PhotoPickerButton: View {
    @Binding var selectedItems: [PhotosPickerItem]
    var remainingCount: Int
    
    var body: some View {
        PhotosPicker(
            selection: $selectedItems,
            maxSelectionCount: remainingCount,
            matching: .images,
            preferredItemEncoding: .automatic
        ) {
            VStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.1))
                    .aspectRatio(1, contentMode: .fit)
                    .overlay(
                        VStack(spacing: 8) {
                            Image(systemName: "plus")
                                .font(.system(size: 24, weight: .medium))
                                .foregroundColor(.gray)
                        }
                    )
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Avatar Selection Page
struct AvatarSelectionPage: View {
    @Binding var profile: CatProfileCreation
    @ObservedObject var viewModel: CatProfileCreationViewModel
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        VStack(spacing: 0) {
            // Heading
            Text("设置猫咪头像")
                .font(.system(size: 22, weight: .bold))
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.vertical, 12)
            
            ScrollView {
                VStack(spacing: 24) {
                    // 描述文本
                    Text("为您的猫咪选择一张头像照片，这将作为猫咪的主要展示图片。")
                        .font(.system(size: 17))
                        .foregroundColor(.secondary)
                        .lineSpacing(4)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(.top, 16)
                    
                    // 紧凑头像选择器
                    CompactAvatarSelector(
                        selectedAvatar: $viewModel.selectedAvatar,
                        avatarUrl: viewModel.originalCat?.avatarUrl,
                        onTap: {
                            viewModel.isShowingAvatarPicker = true
                        }
                    )
                    
                    // 跳过选项
                    VStack(spacing: 8) {
                        Text("您也可以稍后在猫咪档案中设置头像")
                            .font(.system(size: 14))
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.top, 20)
                    
                    Spacer(minLength: 50)
                }
            }
        }
        .padding(.horizontal)
        .sheet(isPresented: $viewModel.isShowingAvatarPicker) {
            AvatarManagementView(
                selectedAvatar: $viewModel.selectedAvatar,
                isShowingAvatarPicker: $viewModel.isShowingAvatarPicker,
                validatedPhotos: profile.validatedPhotos,
                onAvatarSelected: { image in
                    viewModel.setAvatar(image)
                    viewModel.isShowingAvatarPicker = false
                },
                onAvatarRemoved: {
                    viewModel.removeAvatar()
                },
                onSelectFromPhotos: { index in
                    viewModel.selectAvatarFromPhotos(at: index)
                    viewModel.isShowingAvatarPicker = false
                }
            )
        }
    }
}

// MARK: - Processing Page
struct ProcessingPage: View {
    let isProcessing: Bool
    
    var body: some View {
        VStack(spacing: 32) {
            Spacer()
            
            if isProcessing {
                ProgressView()
                    .scaleEffect(1.5)
                    .padding(.bottom, 16)
                
                Text(NSLocalizedString(CatProfileCreation.LocalizationKey.processingTitle, comment: ""))
                    .font(.system(size: 20, weight: .medium))
            } else {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.green)
                    .padding(.bottom, 16)
                
                Text(NSLocalizedString(CatProfileCreation.LocalizationKey.successTitle, comment: ""))
                    .font(.system(size: 24, weight: .bold))
                
                Text(NSLocalizedString(CatProfileCreation.LocalizationKey.successDescription, comment: ""))
                    .font(.system(size: 16))
                    .multilineTextAlignment(.center)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 24)
            }
            
            Spacer()
        }
        .padding()
    }
}



// MARK: - Supporting Views
private struct PhotoCell: View {
    let image: UIImage
    let isValid: Bool
    let validationStatus: CatProfileCreation.ValidationStatus
    let onDelete: () -> Void
    
    var body: some View {
        ZStack(alignment: .topLeading) {
            Image(uiImage: image)
                .resizable()
                .scaledToFill()
                // 添加淡入动画效果
                .opacity(validationStatus == .pending ? 0.7 : 1.0)
                .animation(.easeIn(duration: 0.3), value: validationStatus)
                // 其他样式保持不变
                .frame(minWidth: 0, maxWidth: .infinity, minHeight: 0, maxHeight: .infinity)
                .aspectRatio(1, contentMode: .fill)
                .clipped()
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(strokeColor, lineWidth: 2)
                )
                .contentShape(Rectangle())
            
            // Delete button (top left)
            Button(action: onDelete) {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 22, weight: .semibold))
                    .foregroundColor(.white)
                    .background(
                        Circle()
                            .fill(Color.black.opacity(0.5))
                            .frame(width: 24, height: 24)
                    )
            }
            .padding(4)
            
            // Validation status icon (top right)
            ValidationStatusIcon(status: validationStatus, isValid: isValid)
                .padding(5)
                .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topTrailing)
        }
    }
    
    private var strokeColor: Color {
        switch validationStatus {
        case .pending:
            return .gray.opacity(0.5)
        case .validating:
            return .gray.opacity(0.5)
        case .validated:
            return isValid ? .green : .red
        case .failed:
            return .red
        }
    }
}

private struct CircularLoadingView: View {
    @State private var isRotating = false
    
    var body: some View {
        Circle()
            .trim(from: 0, to: 0.75)
            .stroke(
                Color.blue,
                style: StrokeStyle(
                    lineWidth: 2.5,
                    lineCap: .round,
                    lineJoin: .round
                )
            )
            .frame(width: 20, height: 20)
            .rotationEffect(.degrees(isRotating ? 360 : 0))
            .onAppear {
                withAnimation(
                    .linear(duration: 0.7)
                    .repeatForever(autoreverses: false)
                ) {
                    isRotating = true
                }
            }
    }
}

private struct ValidationStatusIcon: View {
    let status: CatProfileCreation.ValidationStatus
    let isValid: Bool
    
    var body: some View {
        Group {
            switch status {
            case .pending:
                CircularLoadingView()
            case .validating:
                CircularLoadingView()
            case .validated:
                Image(systemName: isValid ? "checkmark.circle.fill" : "exclamationmark.circle.fill")
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(isValid ? .green : .red)
            case .failed:
                Image(systemName: "exclamationmark.circle.fill")
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.red)
            }
        }
        .background(
            Circle()
                .fill(Color.white.opacity(0.8))
                .frame(width: 24, height: 24)
        )
    }
}

// MARK: - Custom ViewModifiers (Consider moving to a separate file if more are added)
struct CenterCatModifier: ViewModifier {
    let catBoundingBox: CGRect?
    let imageSize: CGSize // Original size of the UIImage
    let viewSize: CGSize  // Size of the container view for the image

    func body(content: Content) -> some View {
        if let boundingBox = catBoundingBox,
           imageSize.width > 0, imageSize.height > 0,
           viewSize.width > 0, viewSize.height > 0 {

            let scaleX = viewSize.width / imageSize.width
            let scaleY = viewSize.height / imageSize.height
            let scale = max(scaleX, scaleY)

            let scaledImageWidth = imageSize.width * scale
            let scaledImageHeight = imageSize.height * scale

            let catCenterY_normalized_topLeft = 1.0 - (boundingBox.origin.y + boundingBox.height / 2.0)
            let catCenterX_normalized = boundingBox.origin.x + boundingBox.width / 2.0

            let catCenterX_scaled_pixels = catCenterX_normalized * scaledImageWidth
            let catCenterY_scaled_pixels = catCenterY_normalized_topLeft * scaledImageHeight
            
            let targetX_view = viewSize.width / 2.0
            let targetY_view = viewSize.height * 0.25

            var offsetX = targetX_view - catCenterX_scaled_pixels
            var offsetY = targetY_view - catCenterY_scaled_pixels

            if scaledImageWidth > viewSize.width {
                offsetX = min(0, max(offsetX, viewSize.width - scaledImageWidth))
            } else {
                // If image isn't wider, it's centered by scaledToFill logic or fits exactly.
                // The offsetX should ensure the (potentially smaller) image content's cat_center_X aligns with targetX_view.
                // This requires positioning the content frame correctly first.
                 offsetX = viewSize.width / 2.0 - catCenterX_scaled_pixels // Recalculate for centering logic
            }

            if scaledImageHeight > viewSize.height {
                offsetY = min(0, max(offsetY, viewSize.height - scaledImageHeight))
            } else {
                offsetY = viewSize.height / 2.0 - catCenterY_scaled_pixels // Recalculate for centering logic
            }
            
            // Apply to the original content, which is the Image view
            return content
                .frame(width: scaledImageWidth, height: scaledImageHeight) // Set the image's frame to its scaled size
                .position(x: scaledImageWidth / 2 + offsetX, y: scaledImageHeight / 2 + offsetY)
                // The ZStack in PreviewPage (sized to geometry) will clip this.
        } else {
            // Fallback: standard scaledToFill behavior, centered.
            // The .frame on content will be viewSize in this case.
             return content
                .frame(width: viewSize.width, height: viewSize.height) // Ensure it fills the ZStack slot
                .position(x: viewSize.width / 2, y: viewSize.height / 2)
        }
    }
}

// MARK: - Preview Page
struct PreviewPage: View {
    @Binding var profile: CatProfileCreation
    @Environment(\.colorScheme) var colorScheme
    @State private var isSubmitting = false
    @State private var showError = false
    @State private var errorMessage = ""
    
    // 设置圆角半径常量，方便统一管理和修改
    private let cornerRadius: CGFloat = 16
    private let innerCornerRadius: CGFloat = 12
    
    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                // Find the best photo: one that is valid and has a cat bounding box.
                // Fallback to the first valid photo if none have a bounding box.
                if let photoData = profile.validatedPhotos.first(where: { $0.isValid && $0.catBoundingBox != nil }) ?? profile.validatedPhotos.first(where: { $0.isValid }) {
                    let uiImage = photoData.image
                    let catBoundingBox = photoData.catBoundingBox

                    ZStack(alignment: .bottom) {
                        // Photo container ZStack - this will be clipped
                        ZStack {
                             Image(uiImage: uiImage)
                                .resizable()
                                // .scaledToFill() // Modifier will handle scaling and positioning effectively replacing scaledToFill here
                                .modifier(CenterCatModifier(catBoundingBox: catBoundingBox, imageSize: uiImage.size, viewSize: geometry.size))
                                // The CenterCatModifier sets the frame and position.
                                // The ZStack needs to be the size of the desired viewport.
                        }
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .clipped() // This clips the potentially larger/offset image from CenterCatModifier

                        // Info overlay VStack (remains the same)
                        VStack(spacing: 0) {
                            // Cat name with gradient background
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.black.opacity(0.0),
                                    Color.black.opacity(0.5)
                                ]),
                                startPoint: .top,
                                endPoint: .bottom
                            )
                            .frame(height: 60)
                            .overlay(
                                Text(profile.name)
                                    .font(.system(size: 24, weight: .bold))
                                    .foregroundColor(.white)
                                    .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 1)
                                    .frame(maxWidth: .infinity, alignment: .leading)
                                    .padding(.horizontal, 20)
                                    .padding(.bottom, 16)
                            )
                            
                            // Information section with blur background
                            VStack(spacing: 16) {
                                // Birth Date & Age
                                HStack(spacing: 12) {
                                    InfoCard(
                                        title: NSLocalizedString(CatProfileCreation.LocalizationKey.birthDateLabel, comment: ""),
                                        value: profile.birthDate.map { DateFormatter.localizedString(from: $0, dateStyle: .medium, timeStyle: .none) } ?? NSLocalizedString("cat_creation_unknown", comment: "")
                                    )
                                    
                                    InfoCard(
                                        title: NSLocalizedString(CatProfileCreation.LocalizationKey.ageLabel, comment: ""),
                                        value: profile.birthDate.map { calculateAge(from: $0) } ?? NSLocalizedString("cat_creation_unknown", comment: "")
                                    )
                                }
                                
                                // Gender & Weight
                                HStack(spacing: 12) {
                                    InfoCard(
                                        title: NSLocalizedString(CatProfileCreation.LocalizationKey.genderLabel, comment: ""),
                                        value: NSLocalizedString("cat_creation_\(profile.gender.rawValue)", comment: "")
                                    )
                                    
                                    InfoCard(
                                        title: NSLocalizedString(CatProfileCreation.LocalizationKey.weightLabel, comment: ""),
                                        value: profile.weight.map { String(format: "%.1f kg", $0) } ?? NSLocalizedString("cat_creation_unknown", comment: "")
                                    )
                                }
                                
                                // Neutered Status
                                InfoCard(
                                    title: NSLocalizedString(CatProfileCreation.LocalizationKey.neuteredLabel, comment: ""),
                                    value: NSLocalizedString("cat_creation_\(profile.neuteredStatus.rawValue)", comment: "")
                                )
                            }
                            .padding(.horizontal)
                            .padding(.vertical, 20)
                            .background(
                                Color.black.opacity(0.3)
                                    .background(.ultraThinMaterial)
                            )
                            .cornerRadius(innerCornerRadius)
                        }
                    }
                } else {
                    // Fallback if no valid photo is available
                    VStack {
                        Spacer()
                        Text("No valid photo available for preview.")
                            .foregroundColor(.secondary)
                        Spacer()
                    }
                    .frame(width: geometry.size.width, height: geometry.size.height)
                }
            }
            .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
            .overlay(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .stroke(Color.gray.opacity(0.2), lineWidth: 1)
            )
            .padding(.vertical, 8) // Existing padding for the entire card
        }
        .overlay {
            if isSubmitting {
                LoadingView(isLoading: true, style: .overlay)
            }
        }
        .alert(isPresented: $showError) {
            Alert(
                title: Text(NSLocalizedString("error_title", comment: "")),
                message: Text(errorMessage),
                dismissButton: .default(Text(NSLocalizedString("ok", comment: "")))
            )
        }
    }
    
    private func calculateAge(from date: Date) -> String {
        let calendar = Calendar.current
        let ageComponents = calendar.dateComponents([.year, .month], from: date, to: Date())
        
        if let years = ageComponents.year, years > 0 {
            return String(format: NSLocalizedString(CatProfileCreation.LocalizationKey.ageYear, comment: ""), years)
        } else if let months = ageComponents.month, months > 0 {
            return String(format: NSLocalizedString(CatProfileCreation.LocalizationKey.ageMonth, comment: ""), months)
        } else {
            return NSLocalizedString(CatProfileCreation.LocalizationKey.ageLessThanMonth, comment: "")
        }
    }
}

private struct InfoCard: View {
    let title: String
    let value: String
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(title)
                .font(.system(size: 14))
                .foregroundColor(.secondary)
            Text(value)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.primary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(.vertical, 12)
        .padding(.horizontal, 16)
        .background(.ultraThinMaterial)
        .cornerRadius(12)
    }
}

// MARK: - Custom Date Picker
private struct CustomBirthDatePicker: View {
    @Binding var selectedDate: Date?
    @Environment(\.colorScheme) var colorScheme
    @State private var tempDate: Date
    @State private var selectedComponent: DateComponent = .year
    
    private enum DateComponent {
        case year, month, day
    }
    
    init(selectedDate: Binding<Date?>) {
        self._selectedDate = selectedDate
        self._tempDate = State(initialValue: selectedDate.wrappedValue ?? Date())
    }
    
    var body: some View {
        VStack(spacing: 16) {
            // Component selector
            HStack {
                DateComponentButton(
                    title: String(Calendar.current.component(.year, from: tempDate)),
                    isSelected: selectedComponent == .year
                ) {
                    selectedComponent = .year
                }
                
                Text("-")
                
                DateComponentButton(
                    title: String(format: "%02d", Calendar.current.component(.month, from: tempDate)),
                    isSelected: selectedComponent == .month
                ) {
                    selectedComponent = .month
                }
                
                Text("-")
                
                DateComponentButton(
                    title: String(format: "%02d", Calendar.current.component(.day, from: tempDate)),
                    isSelected: selectedComponent == .day
                ) {
                    selectedComponent = .day
                }
                
                Button(action: {
                    selectedDate = tempDate
                    selectedComponent = .year // Reset to year for next use
                }) {
                    Text("确定")
                        .font(.system(size: 15))
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(AppTheme.primaryColor(for: colorScheme))
                        .foregroundColor(AppTheme.buttonTextColor(for: colorScheme))
                        .cornerRadius(8)
                }
            }
            .padding(.horizontal)
            
            // Picker
            switch selectedComponent {
            case .year:
                Picker("", selection: $tempDate) {
                    ForEach((1900...Calendar.current.component(.year, from: Date())).reversed(), id: \.self) { year in
                        Text(String(year)).tag(Calendar.current.date(from: DateComponents(
                            year: year,
                            month: Calendar.current.component(.month, from: tempDate),
                            day: Calendar.current.component(.day, from: tempDate)
                        )) ?? Date())
                    }
                }
                .pickerStyle(.wheel)
                
            case .month:
                Picker("", selection: $tempDate) {
                    ForEach(1...12, id: \.self) { month in
                        Text(DateFormatter().monthSymbols[month - 1]).tag(Calendar.current.date(from: DateComponents(
                            year: Calendar.current.component(.year, from: tempDate),
                            month: month,
                            day: Calendar.current.component(.day, from: tempDate)
                        )) ?? Date())
                    }
                }
                .pickerStyle(.wheel)
                
            case .day:
                Picker("", selection: $tempDate) {
                    ForEach(1...(Calendar.current.range(of: .day, in: .month, for: tempDate)?.count ?? 31), id: \.self) { day in
                        Text(String(format: "%02d", day)).tag(Calendar.current.date(from: DateComponents(
                            year: Calendar.current.component(.year, from: tempDate),
                            month: Calendar.current.component(.month, from: tempDate),
                            day: day
                        )) ?? Date())
                    }
                }
                .pickerStyle(.wheel)
            }
            
            Spacer()
        }
    }
}

private struct DateComponentButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(isSelected ? AppTheme.buttonTextColor(for: colorScheme) : .primary)
                .padding(.vertical, 8)
                .padding(.horizontal, 12)
                .background(isSelected ? AppTheme.buttonColor(for: colorScheme) : Color.clear)
                .cornerRadius(8)
        }
    }
}