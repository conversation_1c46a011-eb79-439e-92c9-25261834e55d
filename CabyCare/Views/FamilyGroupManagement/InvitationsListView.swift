import SwiftUI

/// 邀请列表视图
struct InvitationsListView: View {
    @Environment(\.presentationMode) var presentationMode
    @State private var errorMessage: String? = nil
    
    @ObservedObject var manager: DeviceAndGroupManager
    
    var body: some View {
        NavigationView {
            ZStack {
                if manager.isLoadingInvitations {
                    // 加载中视图
                    VStack {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                        Text("加载中...")
                            .foregroundColor(.gray)
                            .padding(.top, 8)
                    }
                } else if let error = errorMessage {
                    // 错误视图
                    VStack(spacing: 16) {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.largeTitle)
                            .foregroundColor(.orange)
                        
                        Text("加载失败")
                            .font(.headline)
                        
                        Text(error)
                            .foregroundColor(.gray)
                            .multilineTextAlignment(.center)
                        
                        Button(action: {
                            Task {
                                await manager.fetchInvitations()
                            }
                        }) {
                            Text("重试")
                                .foregroundColor(.white)
                                .padding(.horizontal, 24)
                                .padding(.vertical, 8)
                                .background(Color.blue)
                                .cornerRadius(8)
                        }
                    }
                    .padding()
                } else if manager.invitations.isEmpty {
                    // 空数据视图
                    VStack {
                        Image(systemName: "envelope.badge")
                            .font(.system(size: 60))
                            .foregroundColor(.gray)
                            .padding(.bottom, 8)
                        
                        Text("暂无邀请")
                            .font(.headline)
                            .foregroundColor(.gray)
                    }
                } else {
                    // 邀请列表视图
                    List {
                        ForEach(manager.invitations) { invitation in
                            VStack(alignment: .leading, spacing: 8) {
                                Text(invitation.groupName)
                                    .font(.headline)
                                
                                Text("邀请人: \(invitation.inviterName)")
                                    .font(.subheadline)
                                    .foregroundColor(.gray)
                                
                                // 显示角色
                                Text("角色: \(manager.roleText(for: invitation.role))")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                
                                // 显示状态
                                HStack {
                                    Circle()
                                        .fill(statusColor(for: invitation.status))
                                        .frame(width: 8, height: 8)
                                    
                                    Text("状态: \(manager.statusText(for: invitation.status))")
                                        .font(.caption)
                                        .foregroundColor(.gray)
                                }
                                
                                // 仅对待处理的邀请显示按钮
                                if invitation.status == 0 {
                                    HStack(spacing: 12) {
                                        Button(action: {
                                            acceptInvitation(invitation)
                                        }) {
                                            Text("接受")
                                                .foregroundColor(.white)
                                                .padding(.horizontal, 16)
                                                .padding(.vertical, 8)
                                                .background(Color.green)
                                                .cornerRadius(8)
                                        }
                                        
                                        Button(action: {
                                            rejectInvitation(invitation)
                                        }) {
                                            Text("拒绝")
                                                .foregroundColor(.white)
                                                .padding(.horizontal, 16)
                                                .padding(.vertical, 8)
                                                .background(Color.red)
                                                .cornerRadius(8)
                                        }
                                    }
                                    .padding(.top, 8)
                                }
                            }
                            .padding(.vertical, 8)
                        }
                    }
                    .listStyle(InsetGroupedListStyle())
                    .refreshable {
                        Task {
                            await manager.fetchInvitations()
                        }
                    }
                }
            }
            .navigationTitle("邀请管理")
            .navigationBarItems(
                leading: Button("关闭") {
                    presentationMode.wrappedValue.dismiss()
                },
                trailing: Button(action: {
                    Task {
                        await manager.fetchInvitations()
                    }
                }) {
                    Image(systemName: "arrow.clockwise")
                }
            )
            .onAppear {
                Task {
                    await manager.fetchInvitations()
                }
            }
        }
    }
    
    // 接受邀请
    private func acceptInvitation(_ invitation: InvitationResponse) {
        processInvitation(invitation, action: "accept")
    }
    
    // 拒绝邀请
    private func rejectInvitation(_ invitation: InvitationResponse) {
        processInvitation(invitation, action: "reject")
    }
    
    // 处理邀请（通用方法）
    private func processInvitation(_ invitation: InvitationResponse, action: String) {
        Task {
            do {
                try await manager.processInvitation(invitation, action: action)
                Log.info("✅ 成功\(action == "accept" ? "接受" : "拒绝")邀请")
            } catch {
                Log.error("❌ 处理邀请失败: \(error.localizedDescription)")
                await MainActor.run {
                    errorMessage = "处理邀请失败: \(error.localizedDescription)"
                }
            }
        }
    }
    
    // 将状态ID转换为颜色
    private func statusColor(for statusId: Int) -> Color {
        switch statusId {
        case 0:
            return .orange
        case 1:
            return .green
        case 2:
            return .red
        case 3:
            return .gray
        default:
            return .gray
        }
    }
} 