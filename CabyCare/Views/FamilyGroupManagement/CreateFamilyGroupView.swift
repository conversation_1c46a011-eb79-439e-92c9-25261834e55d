import SwiftUI

/// 创建家庭组视图
struct CreateFamilyGroupView: View {
    @Environment(\.presentationMode) var presentationMode
    @State private var groupName = ""
    @State private var description = ""
    @State private var isCreating = false
    @State private var errorMessage: String? = nil
    @State private var showSuccess = false
    
    @ObservedObject var manager: DeviceAndGroupManager
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("家庭组信息")) {
                    TextField("家庭组名称", text: $groupName)
                    TextField("描述", text: $description)
                }
                
                if let error = errorMessage {
                    Section {
                        Text(error)
                            .foregroundColor(.red)
                            .font(.footnote)
                    }
                }
                
                Section {
                    Button(action: createFamilyGroup) {
                        if isCreating {
                            HStack {
                                Spacer()
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                                Text("创建中...")
                                    .foregroundColor(.white)
                                Spacer()
                            }
                        } else {
                            Text("创建")
                                .bold()
                                .frame(maxWidth: .infinity)
                                .foregroundColor(.white)
                        }
                    }
                    .disabled(groupName.isEmpty || isCreating)
                    .padding()
                    .background(groupName.isEmpty ? Color.gray : Color.blue)
                    .cornerRadius(10)
                }
            }
            .navigationTitle("创建家庭组")
            .navigationBarItems(leading: Button("取消") {
                presentationMode.wrappedValue.dismiss()
            })
            .alert(isPresented: $showSuccess) {
                Alert(
                    title: Text("成功"),
                    message: Text("家庭组 \"\(groupName)\" 创建成功"),
                    dismissButton: .default(Text("确定")) {
                        presentationMode.wrappedValue.dismiss()
                    }
                )
            }
        }
    }
    
    private func createFamilyGroup() {
        Log.info("🏠 开始创建家庭组: \(groupName)")
        isCreating = true
        errorMessage = nil
        
        Task {
            do {
                try await manager.createFamilyGroup(groupName: groupName, description: description)
                
                // 更新UI必须在主线程执行
                await MainActor.run {
                    isCreating = false
                    showSuccess = true
                    // 重新获取家庭组列表
                    Task {
                        await manager.fetchFamilyGroups()
                    }
                }
                
            } catch {
                Log.error("❌ 创建家庭组失败: \(error.localizedDescription)")
                
                await MainActor.run {
                    isCreating = false
                    errorMessage = "创建家庭组失败: \(error.localizedDescription)"
                }
            }
        }
    }
} 