import SwiftUI

/// 家庭组详情视图
struct FamilyGroupDetailScreen: View {
    let group: FamilyGroup
    
    var body: some View {
        List {
            Section(header: Text("家庭组信息")) {
                HStack {
                    Text("名称")
                        .foregroundColor(.gray)
                    Spacer()
                    Text(group.name)
                        .fontWeight(.medium)
                }
                
                HStack {
                    Text("描述")
                        .foregroundColor(.gray)
                    Spacer()
                    Text(group.description.isEmpty ? "无描述" : group.description)
                        .fontWeight(.medium)
                }
                
                HStack {
                    Text("成员数量")
                        .foregroundColor(.gray)
                    Spacer()
                    Text("\(group.memberCount)")
                        .fontWeight(.medium)
                }
                
                HStack {
                    Text("设备数量")
                        .foregroundColor(.gray)
                    Spacer()
                    Text("\(group.deviceCount)")
                        .fontWeight(.medium)
                }
                
                HStack {
                    Text("我的角色")
                        .foregroundColor(.gray)
                    Spacer()
                    Text(roleName(for: group.role))
                        .fontWeight(.medium)
                        .foregroundColor(roleColor(for: group.role))
                }
            }
            
            if group.role == .owner || group.role == .admin {
                Section(header: Text("管理操作")) {
                    NavigationLink(destination: EmptyView()) {
                        Label("管理成员", systemImage: "person.2")
                    }
                    
                    NavigationLink(destination: EmptyView()) {
                        Label("管理设备", systemImage: "desktopcomputer")
                    }
                    
                    NavigationLink(destination: EmptyView()) {
                        Label("邀请成员", systemImage: "person.badge.plus")
                    }
                }
            }
            
            Section(header: Text("其他操作")) {
                Button(action: {}) {
                    Label("刷新信息", systemImage: "arrow.clockwise")
                }
                
                if group.role != .owner {
                    Button(action: {}) {
                        Label("退出家庭组", systemImage: "rectangle.portrait.and.arrow.right")
                            .foregroundColor(.red)
                    }
                }
            }
        }
        .navigationTitle("家庭组详情")
        .navigationBarTitleDisplayMode(.inline)
    }
    
    private func roleName(for role: FamilyGroupRole) -> String {
        switch role {
        case .owner: return "拥有者"
        case .admin: return "管理员"
        case .member: return "成员"
        }
    }
    
    private func roleColor(for role: FamilyGroupRole) -> Color {
        switch role {
        case .owner: return .red
        case .admin: return .orange
        case .member: return .blue
        }
    }
}

struct FamilyGroupDetailScreen_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            FamilyGroupDetailScreen(
                group: FamilyGroup(
                    id: "group-1",
                    name: "我的家庭",
                    description: "温馨的家庭环境",
                    memberCount: 3,
                    deviceCount: 2,
                    role: .owner
                )
            )
        }
    }
} 