import SwiftUI

struct StatisticsView: View {
    let stats: VideoPlayerViewModel.DailyStats?
    let accentColor: Color
    let secondaryColor: Color
    
    var body: some View {
        if let stats = stats {
            TabView {
                // 总体统计
                DailySummaryCard(
                    videoCount: stats.videoCount,
                    totalDuration: stats.totalDuration,
                    accentColor: accentColor,
                    secondaryColor: secondaryColor
                )
                
                // 每只猫的统计
                ForEach(stats.catStats) { catStat in
                    CatStatCard(
                        catStat: catStat,
                        accentColor: accentColor,
                        secondaryColor: secondaryColor
                    )
                }
            }
            .tabViewStyle(.page)
            .frame(height: 80)
        }
    }
} 