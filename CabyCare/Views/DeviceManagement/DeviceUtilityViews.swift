import SwiftUI

// MARK: - 设备历史数据视图
struct DeviceHistoryView: View {
    let device: DeviceViewModel
    
    var body: some View {
        VStack {
            Text(String(format: NSLocalizedString("device_history_title", comment: "%@的历史数据"), device.name))
                .font(.headline)
                .padding()
            
            Image(systemName: "chart.xyaxis.line")
                .font(.system(size: 60))
                .foregroundColor(.blue)
                .padding()
            
            Text(NSLocalizedString("device_history_development", comment: "功能开发中..."))
                .foregroundColor(.gray)
        }
        .navigationTitle(NSLocalizedString("device_history_nav_title", comment: "历史数据"))
    }
}

// MARK: - 设备共享设置视图
struct DeviceSharingView: View {
    let device: DeviceViewModel
    
    var body: some View {
        VStack {
            Text(String(format: NSLocalizedString("device_sharing_title", comment: "%@的共享设置"), device.name))
                .font(.headline)
                .padding()
            
            Image(systemName: "person.2.square.stack")
                .font(.system(size: 60))
                .foregroundColor(.blue)
                .padding()
            
            Text(NSLocalizedString("device_sharing_development", comment: "功能开发中..."))
                .foregroundColor(.gray)
        }
        .navigationTitle(NSLocalizedString("device_sharing_nav_title", comment: "共享设置"))
    }
}

// MARK: - 添加设备视图
struct AddDeviceView: View {
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        NavigationView {
            BluetoothView()
                .navigationBarHidden(false)
                .navigationTitle(NSLocalizedString("add_device", comment: "添加设备"))
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarLeading) {
                        Button(NSLocalizedString("cancel", comment: "取消")) {
                            presentationMode.wrappedValue.dismiss()
                        }
                    }
                }
        }
    }
} 