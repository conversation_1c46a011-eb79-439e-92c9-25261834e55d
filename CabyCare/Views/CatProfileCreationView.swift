import SwiftUI
import PhotosUI

struct CatProfileCreationView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) var colorScheme
    @StateObject private var viewModel: CatProfileCreationViewModel
    @State private var currentPage = 0
    @State private var showAlert = false
    @State private var alertMessage = ""
    @State private var showCancelConfirmation = false
    
    let editingCat: CatProfile?
    
    // MARK: - Initializers
    init(editingCat: CatProfile? = nil) {
        self.editingCat = editingCat
        if let cat = editingCat {
            self._viewModel = StateObject(wrappedValue: CatProfileCreationViewModel(editingCat: cat))
        } else {
            self._viewModel = StateObject(wrappedValue: CatProfileCreationViewModel())
        }
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Progress bar
            ProgressBar(
                currentStep: viewModel.isEditMode ? currentPage + 1 : currentPage, 
                totalSteps: viewModel.isEditMode ? Configuration.CatProfileCreation.totalSteps - 1 : Configuration.CatProfileCreation.totalSteps
            )
                .padding(.horizontal)
                .padding(.top)
            
            // Title and close button
            HStack {
                Text(viewModel.isEditMode ? NSLocalizedString("cat_edit_title", comment: "") : NSLocalizedString("cat_creation_title", comment: ""))
                    .font(.headline)
                    .foregroundColor(AppTheme.textColor(for: colorScheme))
                
                Spacer()
                
                Button(action: { showCancelConfirmation = true }) {
                    Image(systemName: "xmark")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.secondary)
                        .padding(8)
                        .background(Color.gray.opacity(0.1))
                        .clipShape(Circle())
                }
            }
            .padding()
            
            // Content area
            ZStack {
                if viewModel.isEditMode {
                    // Edit mode - skip intro page
                    switch currentPage {
                    case 0:
                        BasicInfoPage(profile: $viewModel.profile)
                    case 1:
                        PhysicalInfoPage(profile: $viewModel.profile)
                    case 2:
                        PhotosPage(profile: $viewModel.profile, viewModel: viewModel)
                    case 3:
                        AvatarSelectionPage(profile: $viewModel.profile, viewModel: viewModel)
                    case 4:
                        PreviewPage(profile: $viewModel.profile)
                    case 5:
                        ProcessingPage(isProcessing: viewModel.isSubmitting)
                    default:
                        EmptyView()
                    }
                } else {
                    // Creation mode - include intro page
                    switch currentPage {
                    case 0:
                        IntroPage()
                    case 1:
                        BasicInfoPage(profile: $viewModel.profile)
                    case 2:
                        PhysicalInfoPage(profile: $viewModel.profile)
                    case 3:
                        PhotosPage(profile: $viewModel.profile, viewModel: viewModel)
                    case 4:
                        AvatarSelectionPage(profile: $viewModel.profile, viewModel: viewModel)
                    case 5:
                        PreviewPage(profile: $viewModel.profile)
                    case 6:
                        ProcessingPage(isProcessing: viewModel.isSubmitting)
                    default:
                        EmptyView()
                    }
                }
            }
            .padding(.horizontal)
            
            Spacer()
            
            // Navigation buttons
            NavigationButtons(
                currentPage: currentPage,
                isEditMode: viewModel.isEditMode,
                isSubmitting: viewModel.isSubmitting,
                onPrevious: { currentPage -= 1 },
                onNext: {
                    if viewModel.isEditMode {
                        // Edit mode navigation (no intro page)
                        switch currentPage {
                        case 0:
                            // Basic info page - validate name
                            if !viewModel.profile.isNameValid {
                                showAlert = true
                                alertMessage = viewModel.profile.nameValidationError ?? NSLocalizedString("cat_creation_error_invalid_name", comment: "")
                            } else {
                                currentPage += 1
                            }
                        case 1:
                            // Physical info page - validate weight
                            if !viewModel.profile.isWeightValid {
                                showAlert = true
                                alertMessage = NSLocalizedString("cat_creation_error_invalid_weight", comment: "")
                            } else {
                                currentPage += 1
                            }
                        case 2:
                            // Photos page - validate photos (allow empty photos in edit mode)
                            currentPage += 1
                        case 3:
                            // Avatar selection page - optional, continue to preview
                            currentPage += 1
                        case 4:
                            // Preview page - submit profile
                            // 防止重复提交
                            guard !viewModel.isSubmitting else { return }
                            
                            Task {
                                let (success, status) = await viewModel.submitProfile()
                                
                                if success {
                                    currentPage += 1
                                } else {
                                    showAlert = true
                                    if status == .nameInvalid {
                                        alertMessage = NSLocalizedString("cat_creation_error_name_invalid", comment: "")
                                    } else {
                                        alertMessage = NSLocalizedString("cat_creation_error_submission", comment: "")
                                    }
                                }
                            }
                        case 5:
                            // Processing page - complete and dismiss
                            dismiss()
                        default:
                            break
                        }
                    } else {
                        // Creation mode navigation (with intro page)
                        switch currentPage {
                        case 0:
                            // Intro page - just move to next page
                            currentPage += 1
                        case 1:
                            // Basic info page - validate name
                            if !viewModel.profile.isNameValid {
                                showAlert = true
                                alertMessage = viewModel.profile.nameValidationError ?? NSLocalizedString("cat_creation_error_invalid_name", comment: "")
                            } else {
                                currentPage += 1
                            }
                        case 2:
                            // Physical info page - validate weight
                            if !viewModel.profile.isWeightValid {
                                showAlert = true
                                alertMessage = NSLocalizedString("cat_creation_error_invalid_weight", comment: "")
                            } else {
                                currentPage += 1
                            }
                        case 3:
                            // Photos page - validate photos
                            if viewModel.profile.validatedPhotos.isEmpty {
                                showAlert = true
                                alertMessage = NSLocalizedString("cat_creation_error_no_photos", comment: "")
                            } else if !viewModel.profile.validatedPhotos.contains(where: { $0.isValid }) {
                                showAlert = true
                                alertMessage = NSLocalizedString("cat_creation_error_no_valid_photos", comment: "")
                            } else {
                                currentPage += 1
                            }
                        case 4:
                            // Avatar selection page - optional, continue to preview
                            currentPage += 1
                        case 5:
                            // Preview page - submit profile
                            // 防止重复提交
                            guard !viewModel.isSubmitting else { return }
                            
                            Task {
                                let (success, status) = await viewModel.submitProfile()
                                
                                if success {
                                    currentPage += 1
                                } else {
                                    showAlert = true
                                    if status == .nameInvalid {
                                        alertMessage = NSLocalizedString("cat_creation_error_name_invalid", comment: "")
                                    } else if status == .noValidPhoto {
                                        alertMessage = NSLocalizedString("cat_creation_error_no_photos", comment: "")
                                    } else {
                                        alertMessage = NSLocalizedString("cat_creation_error_submission", comment: "")
                                    }
                                }
                            }
                        case 6:
                            // Processing page - complete and dismiss
                            dismiss()
                        default:
                            break
                        }
                    }
                },
                onCancel: { dismiss() }
            )
            .padding()
        }
        .background(AppTheme.backgroundColor(for: colorScheme))
        .alert(isPresented: $showAlert) {
            Alert(
                title: Text(NSLocalizedString("error_title", comment: "")),
                message: Text(alertMessage),
                dismissButton: .default(Text(NSLocalizedString("ok", comment: "")))
            )
        }
        .alert(
            NSLocalizedString("cat_creation_cancel_confirmation_title", comment: ""),
            isPresented: $showCancelConfirmation,
            actions: {
                Button(NSLocalizedString("cat_creation_confirm", comment: ""), role: .destructive) {
                    dismiss()
                }
                Button(NSLocalizedString("cat_creation_cancel", comment: ""), role: .cancel) {
                    showCancelConfirmation = false
                }
            },
            message: {
                Text(NSLocalizedString("cat_creation_cancel_confirmation_message", comment: ""))
            }
        )
    }
}

// MARK: - Progress Bar
struct ProgressBar: View {
    @Environment(\.colorScheme) var colorScheme
    let currentStep: Int
    let totalSteps: Int
    
    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                // Background track
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color.gray.opacity(0.2))
                    .frame(height: 8)
                
                // Progress indicator
                RoundedRectangle(cornerRadius: 4)
                    .fill(AppTheme.primaryColor(for: colorScheme))
                    .frame(width: getProgressWidth(geometry.size.width), height: 8)
            }
        }
        .frame(height: 8)
    }
    
    private func getProgressWidth(_ totalWidth: CGFloat) -> CGFloat {
        if totalSteps <= 1 { return totalWidth }
        let progress = CGFloat(currentStep) / CGFloat(totalSteps - 1)
        return totalWidth * min(1, max(0, progress))
    }
}

// MARK: - Navigation Buttons
private struct NavigationButtons: View {
    @Environment(\.colorScheme) var colorScheme
    let currentPage: Int
    let isEditMode: Bool
    let isSubmitting: Bool
    let onPrevious: () -> Void
    let onNext: () -> Void
    let onCancel: () -> Void
    
    var body: some View {
        HStack {
            if currentPage > 0 && currentPage < maxPage {
                Button(action: onPrevious) {
                    HStack(spacing: 8) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .semibold))
                        Text(NSLocalizedString(CatProfileCreation.LocalizationKey.previous, comment: ""))
                            .font(.system(size: 16, weight: .semibold))
                    }
                    .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                    .padding(.vertical, 12)
                    .padding(.horizontal, 16)
                    .background(AppTheme.primaryColor(for: colorScheme).opacity(0.1))
                    .cornerRadius(8)
                }
                .disabled(isSubmitting) // 提交时禁用返回按钮
            }
            
            Spacer()
            
            if currentPage < maxPage {
                SubmitButton(
                    title: buttonTitle,
                    isSubmitting: isSubmitting,
                    isSubmitStep: isSubmitStep,
                    onAction: onNext
                )
            } else {
                Button(action: onCancel) {
                    Text(NSLocalizedString(CatProfileCreation.LocalizationKey.finish, comment: ""))
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(AppTheme.buttonTextColor(for: colorScheme))
                        .padding(.vertical, 12)
                        .padding(.horizontal, 16)
                        .background(AppTheme.buttonColor(for: colorScheme))
                        .cornerRadius(8)
                }
            }
        }
    }
    
    private var maxPage: Int {
        return isEditMode ? 5 : 6
    }
    
    private var isSubmitStep: Bool {
        if isEditMode {
            return currentPage == 4  // 编辑模式的第4步是提交
        } else {
            return currentPage == 5  // 创建模式的第5步是提交
        }
    }
    
    private var buttonTitle: String {
        if isEditMode {
            switch currentPage {
            case 3:
                return NSLocalizedString(CatProfileCreation.LocalizationKey.preview, comment: "")
            case 4:
                return NSLocalizedString("cat_edit_save", comment: "")
            default:
                return NSLocalizedString(CatProfileCreation.LocalizationKey.next, comment: "")
            }
        } else {
            switch currentPage {
            case 4:
                return NSLocalizedString(CatProfileCreation.LocalizationKey.preview, comment: "")
            case 5:
                return NSLocalizedString(CatProfileCreation.LocalizationKey.submit, comment: "")
            default:
                return NSLocalizedString(CatProfileCreation.LocalizationKey.next, comment: "")
            }
        }
    }
}

// MARK: - Submit Button Component
private struct SubmitButton: View {
    @Environment(\.colorScheme) var colorScheme
    let title: String
    let isSubmitting: Bool
    let isSubmitStep: Bool
    let onAction: () -> Void
    
    var body: some View {
        Button(action: onAction) {
            HStack(spacing: 8) {
                if isSubmitting && isSubmitStep {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: AppTheme.buttonTextColor(for: colorScheme)))
                        .scaleEffect(0.8)
                }
                
                Text(isSubmitting && isSubmitStep ? submittingText : title)
                    .font(.system(size: 16, weight: .semibold))
                
                if !isSubmitStep && !isSubmitting {
                    Image(systemName: "chevron.right")
                        .font(.system(size: 16, weight: .semibold))
                }
            }
            .foregroundColor(buttonTextColor)
            .padding(.vertical, 12)
            .padding(.horizontal, 16)
            .background(buttonBackgroundColor)
            .cornerRadius(8)
            .animation(.easeInOut(duration: 0.2), value: isSubmitting)
        }
        .disabled(isSubmitting && isSubmitStep)
    }
    
    private var submittingText: String {
        return NSLocalizedString("cat_creation_submitting", comment: "")
    }
    
    private var buttonTextColor: Color {
        if isSubmitting && isSubmitStep {
            return AppTheme.buttonTextColor(for: colorScheme).opacity(0.8)
        }
        return AppTheme.buttonTextColor(for: colorScheme)
    }
    
    private var buttonBackgroundColor: Color {
        if isSubmitting && isSubmitStep {
            return AppTheme.buttonColor(for: colorScheme).opacity(0.8)
        }
        return AppTheme.buttonColor(for: colorScheme)
    }
}

// MARK: - Preview
#Preview {
    CatProfileCreationView()
} 
