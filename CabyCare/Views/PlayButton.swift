import SwiftUI

struct PlayButton: View {
    let isPlaying: Bool
    let isLoading: Bool
    let action: () -> Void
    
    // 动画状态
    @State private var rotation: Double = 0
    @State private var scale: CGFloat = 1.0
    
    var body: some View {
        Button(action: {
            if !isLoading {
                withAnimation(.easeInOut(duration: 0.2)) {
                    action()
                }
            }
        }) {
            ZStack {
                // 背景圆圈 - 44x44pt 符合 Apple 设计指南的最小点击区域
                Circle()
                    .fill(!isPlaying ? Color(.systemGray6) : Color.accentColor.opacity(0.9))
                    .overlay(
                        Circle()
                            .strokeBorder(!isPlaying ? Color.accentColor.opacity(0.2) : Color.white.opacity(0.2), lineWidth: 1)
                    )
                    .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                    .frame(width: 44, height: 44)
                
                if isLoading {
                    // 加载动画
                    Circle()
                        .trim(from: 0, to: 0.7)
                        .stroke(Color.accentColor, lineWidth: 2)
                        .frame(width: 32, height: 32)
                        .rotationEffect(Angle(degrees: rotation))
                        .onAppear {
                            withAnimation(
                                Animation
                                    .linear(duration: 1)
                                    .repeatForever(autoreverses: false)
                            ) {
                                rotation = 360
                            }
                        }
                } else {
                    // 播放/暂停图标 - 使用系统图标确保一致性
                    Group {
                        if !isPlaying {
                            // 暂停状态 - 显示播放三角形
                            Image(systemName: "play.fill")
                                .font(.system(size: 20, weight: .semibold))
                                .foregroundColor(.accentColor)
                                .offset(x: 1) // 轻微偏移以视觉居中
                        } else {
                            // 播放状态 - 显示暂停竖线
                            HStack(spacing: 5) {
                                RoundedRectangle(cornerRadius: 2)
                                    .fill(Color.white)
                                    .frame(width: 3, height: 15)
                                RoundedRectangle(cornerRadius: 2)
                                    .fill(Color.white)
                                    .frame(width: 3, height: 15)
                            }
                        }
                    }
                    .frame(width: 44, height: 44)
                    .contentShape(Rectangle())
                    .scaleEffect(scale)
                }
            }
            .accessibilityLabel(!isPlaying ? "Play" : "Pause")
            .accessibilityHint(!isPlaying ? "Start playback" : "Pause playback")
        }
        .buttonStyle(ScaledButtonStyle())
        // 添加触觉反馈
        .sensoryFeedback(.selection, trigger: isPlaying)
        // 确保按钮在加载时显示为不可用状态
        .opacity(isLoading ? 0.6 : 1.0)
        // 添加按下效果
        .onLongPressGesture(minimumDuration: .infinity, maximumDistance: .infinity,
            pressing: { pressing in
                withAnimation(.easeInOut(duration: 0.2)) {
                    scale = pressing ? 0.9 : 1.0
                }
            },
            perform: { }
        )
    }
}

// 自定义按钮样式
struct ScaledButtonStyle: ButtonStyle {
    func makeBody(configuration: ButtonStyle.Configuration) -> some View {
        configuration.label
            .contentShape(Circle())
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: configuration.isPressed)
    }
}

// 预览
struct PlayButton_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            PlayButton(isPlaying: false, isLoading: false) {}
            PlayButton(isPlaying: true, isLoading: false) {}
            PlayButton(isPlaying: false, isLoading: true) {}
        }
        .padding()
        .previewLayout(.sizeThatFits)
    }
} 