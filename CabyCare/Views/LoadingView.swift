import SwiftUI

struct LoadingView: View {
    let isLoading: Bool
    let style: Style
    
    enum Style {
        case fullscreen
        case inline
        case overlay
    }
    
    var body: some View {
        if isLoading {
            switch style {
            case .fullscreen:
                fullscreenLoader
            case .inline:
                inlineLoader
            case .overlay:
                overlayLoader
            }
        }
    }
    
    private var fullscreenLoader: some View {
        ZStack {
            Color(.systemBackground)
                .edgesIgnoringSafeArea(.all)
            ProgressView()
                .scaleEffect(1.5)
        }
    }
    
    private var inlineLoader: some View {
        ProgressView()
            .frame(maxWidth: .infinity)
            .padding()
    }
    
    private var overlayLoader: some View {
        ZStack {
            Color.black.opacity(0.4)
            ProgressView()
                .tint(.white)
        }
    }
} 