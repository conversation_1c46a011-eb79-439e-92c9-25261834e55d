import SwiftUI

struct PlaybackVolumeControlView: View {
    @Binding var volume: Double
    @Binding var isMuted: Bool
    @State private var isExpanded = false
    
    var volumeIconName: String {
        if isMuted {
            return "speaker.slash.fill"
        }
        switch volume {
        case 0:
            return "speaker.fill"
        case 0..<0.33:
            return "speaker.wave.1.fill"
        case 0.33..<0.66:
            return "speaker.wave.2.fill"
        default:
            return "speaker.wave.3.fill"
        }
    }
    
    var body: some View {
        HStack(spacing: 16) {
            // 音量/静音按钮
            Button(action: {
                withAnimation(.easeInOut(duration: 0.2)) {
                    if isMuted {
                        isMuted = false
                        isExpanded = true
                    } else {
                        isExpanded.toggle()
                    }
                }
            }) {
                Image(systemName: volumeIconName)
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 44, height: 44)
                    .contentShape(Rectangle())
            }
            .accessibilityLabel(isMuted ? "取消静音" : "音量控制")
            
            // 音量滑块 - 仅在展开时显示
            if isExpanded {
                HStack(spacing: 12) {
                    // 静音按钮
                    Button(action: {
                        isMuted.toggle()
                    }) {
                        Image(systemName: "speaker.slash.fill")
                            .font(.title2)
                            .foregroundColor(.white)
                            .frame(width: 44, height: 44)
                            .contentShape(Rectangle())
                    }
                    .accessibilityLabel(isMuted ? "取消静音" : "静音")
                    
                    // 音量滑块
                    Slider(value: $volume, in: 0...1)
                        .frame(width: 120)
                        .accentColor(.white)
                }
                .transition(.move(edge: .trailing).combined(with: .opacity))
            }
        }
    }
} 