import SwiftUI

struct DeveloperMenuView: View {
    @State private var showingResult = false
    @State private var resultMessage = ""
    @StateObject private var authManager = AuthManager.shared
    
    var body: some View {
        List {
            Section("认证测试") {
                // 新增：令牌测试工具导航
                NavigationLink(destination: TokenTestingView()) {
                    HStack {
                        Image(systemName: "testtube.2")
                            .foregroundColor(.blue)
                        VStack(alignment: .leading) {
                            Text("令牌测试工具")
                                .font(.headline)
                            Text("完整的令牌过期和刷新测试功能")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                // 保留原有的快速测试按钮
                Button("快速测试令牌过期") {
                    TokenExpiryTester.forceTokenExpiry()
                    resultMessage = "令牌已被修改，下次API请求将触发401错误"
                    showingResult = true
                }
                
                Button("模拟令牌过期通知") {
                    Log.info("🧪 手动触发认证过期通知（测试）")
                    NotificationCenter.default.post(
                        name: .authenticationExpired, 
                        object: nil,
                        userInfo: ["reason": "开发者菜单手动测试"]
                    )
                    resultMessage = "已发送模拟的令牌过期通知"
                    showingResult = true
                }
                
                Button("模拟授权失败") {
                    NotificationCenter.default.post(name: .authorizationFailed, object: nil)
                    resultMessage = "已发送模拟的授权失败通知"
                    showingResult = true
                }
            }
            
            Section("用户数据") {
                Button("打印当前令牌信息") {
                    UserDefaultsManager.shared.printUserData()
                    resultMessage = "令牌信息已打印到控制台"
                    showingResult = true
                }
                
                Button("测试视频缓存清理") {
                    Task {
                        await NotificationManager.shared.cleanupVideoCache()
                        resultMessage = "视频缓存清理测试完成，请查看控制台日志"
                        showingResult = true
                    }
                }
                
                Button("测试动物界面缓存清理") {
                    Task {
                        await NotificationManager.shared.cleanupAnimalCache()
                        resultMessage = "动物界面缓存清理测试完成，请查看控制台日志"
                        showingResult = true
                    }
                }
                
                Button("测试客户端清理") {
                    Task {
                        await NotificationManager.shared.cleanupClientData()
                        resultMessage = "客户端数据清理测试完成，请查看控制台日志"
                        showingResult = true
                    }
                }
                
                Button("清除用户数据") {
                    AuthManager.shared.logout()
                    resultMessage = "已清除所有用户数据"
                    showingResult = true
                }
            }
            
            Section("调试工具") {
                Button(action: {
                    Log.exportLogFiles().forEach { url in
                        print("📄 导出的日志文件路径: \(url.path)")
                    }
                    resultMessage = "日志文件已导出"
                    showingResult = true
                }) {
                    Label("导出日志文件", systemImage: "doc.text")
                }
            }
        }
        .navigationTitle("开发者菜单")
        .alert("操作结果", isPresented: $showingResult) {
            Button("确定", role: .cancel) {}
        } message: {
            Text(resultMessage)
        }
    }
}
