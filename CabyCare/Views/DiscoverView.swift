import SwiftUI

// 将 Article 移到顶部并简化
struct Article: Identifiable {
    let id = UUID()
    let title: String
    let description: String
    let author: String
    let category: String
    let imageName: String
    let likeCount: Int
    let commentCount: Int
    let shareCount: Int
}

// 将文章数据移到单独的扩展中
extension Article {
    static let sampleArticles = [
        Article(
            title: "机器人与猫咪的奇妙相遇",
            description: "探索人工智能如何与我们的毛茸茸朋友互动，一起见证科技与萌宠的完美融合...",
            author: "科技观察员",
            category: "科技萌宠",
            imageName: "robot.cat.1",
            likeCount: 3100,
            commentCount: 220,
            shareCount: 274
        ),
        Article(
            title: "好奇猫咪的探索之旅",
            description: "当猫咪遇到机器人，会产生怎样有趣的互动？一起来看看这段奇妙的邂逅...",
            author: "萌宠摄影师",
            category: "互动探索",
            imageName: "robot.cat.2",
            likeCount: 2800,
            commentCount: 185,
            shareCount: 156
        ),
        Article(
            title: "科技与萌宠的完美融合",
            description: "现代科技如何改变我们与宠物的互动方式？一起探索未来的养宠新方向...",
            author: "宠物科技专家",
            category: "前沿科技",
            imageName: "robot.cat.3",
            likeCount: 3500,
            commentCount: 280,
            shareCount: 312
        )
    ]
}

struct DiscoverView: View {
    @Environment(\.colorScheme) var colorScheme
    @State private var articles: [Article] = Article.sampleArticles

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    ForEach(articles) { article in
                        ArticleCard(article: article)
                    }
                }
                .padding()
            }
            .background(AppTheme.backgroundColor(for: colorScheme))
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .principal) {
                    HStack {
                        Text("发现")
                            .font(.headline)
                            .foregroundColor(AppTheme.textColor(for: colorScheme))
                        Spacer()
                    }
                }
            }
        }
    }
}

struct ArticleCard: View {
    let article: Article
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Image
            Image(article.imageName)
                .resizable()
                .aspectRatio(contentMode: .fill)
                .frame(height: 200)
                .clipped()
                .cornerRadius(16)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(AppTheme.primaryColor(for: colorScheme).opacity(0.2), lineWidth: 1)
                )

            // Content
            VStack(alignment: .leading, spacing: 8) {
                // Title
                Text(article.title)
                    .font(.title3)
                    .bold()
                    .foregroundColor(AppTheme.textColor(for: colorScheme))

                // Description
                Text(article.description)
                    .font(.subheadline)
                    .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                    .lineLimit(2)

                HStack {
                    // Author and Category
                    HStack(spacing: 4) {
                        Text(article.author)
                            .font(.caption)
                        Text("·")
                        Text(article.category)
                            .font(.caption)
                    }
                    .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))

                    Spacer()

                    // Interaction Buttons
                    HStack(spacing: 16) {
                        InteractionButton(icon: "heart", count: article.likeCount)
                        InteractionButton(icon: "bubble.right", count: article.commentCount)
                        InteractionButton(icon: "square.and.arrow.up", count: article.shareCount)
                    }
                }
            }
            .padding(.horizontal, 4)
        }
        .padding()
        .background(AppTheme.secondaryBackgroundColor(for: colorScheme))
        .cornerRadius(16)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(AppTheme.primaryColor(for: colorScheme).opacity(0.2), lineWidth: 1)
        )
    }
}

struct InteractionButton: View {
    let icon: String
    let count: Int
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        Button(action: {}) {
            HStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.system(size: 12))
                Text(formatCount(count))
                    .font(.caption)
            }
            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
        }
    }

    private func formatCount(_ count: Int) -> String {
        if count >= 10000 {
            return String(format: "%.1fw", Double(count) / 10000.0)
        } else if count >= 1000 {
            return String(format: "%.1fk", Double(count) / 1000.0)
        }
        return "\(count)"
    }
}

#Preview {
    DiscoverView()
}
