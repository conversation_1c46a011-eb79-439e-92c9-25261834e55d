import SwiftUI
import AVKit  // 添加这行以支持 VideoPlayer

struct VideoListView: View {
    @ObservedObject var viewModel: VideoPlayerViewModel
    @State private var scrollProxy: ScrollViewProxy?
    @State private var isCalendarExpanded = false

    var body: some View {
        ZStack {
            BackgroundView()

            VStack(spacing: 0) {
                // 主内容区域
                mainContent
                    .padding(.top, 8)
                    .background(
                        Color(.systemBackground)
                            .shadow(color: .black.opacity(0.05), radius: 8, y: 2)
                    )

                // 视频列表
                ScrollableContent(viewModel: viewModel, scrollProxy: $scrollProxy)
            }

            // 加载状态
            LoadingOverlay(viewModel: viewModel)
        }
        .alert("错误", isPresented: .init(
            get: { viewModel.error != nil },
            set: { if !$0 {
                Task { @MainActor in
                    viewModel.error = nil
                }
            }}
        )) {
            Button("确定", role: .cancel) {
                Task { @MainActor in
                    viewModel.error = nil
                }
            }
        } message: {
            if let error = viewModel.error {
                Text(error)
            }
        }
        .onDisappear {
            viewModel.stopPlayback()
        }
    }

    // MARK: - 主要内容区域
    private var mainContent: some View {
        VStack(spacing: 16) {
            // 顶部工具栏
            dateAndStatsBar

            // 调试信息视图 - 临时注释掉，测试修复效果
            // debugInfoView

            // 日历选择器（可折叠）- 带遮罩容器
            calendarContainer
                .frame(height: isCalendarExpanded ? nil : 0)
                .opacity(isCalendarExpanded ? 1 : 0)
                .clipped() // 裁剪溢出内容，防止穿模

            // 视频播放器
            videoPlayer
        }
    }

    // 日历容器，确保动画在边界内
    private var calendarContainer: some View {
        ZStack {
            CalendarPickerView(
                selectedDate: $viewModel.selectedDate,
                availableDates: viewModel.availableDates,
                accentColor: .accentColor,
                deviceTimezone: viewModel.getDeviceTimezone(),
                onDateSelected: {
                    withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                        isCalendarExpanded = false
                    }
                }
            )
            .onAppear {
                Log.info("📅 VideoListView: CalendarPickerView 出现，可用日期数量: \(viewModel.availableDates.count)")
                // 记录前几个可用日期
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM-dd"
                for (index, date) in viewModel.availableDates.prefix(3).enumerated() {
                    Log.info("📅 VideoListView: 可用日期[\(index)]: \(formatter.string(from: date))")
                }
            }
        }
        .transition(.opacity) // 纯淡入淡出，避免位移穿模
    }

    // MARK: - 组件
    private var dateAndStatsBar: some View {
        HStack(alignment: .center) {
            // 日期选择按钮
            datePickerButton

            Spacer()

            // 统计信息
            if let stats = viewModel.dailyStats {
                statsView(stats)
            }
        }
        .padding(.horizontal)
    }

    // 调试信息视图 - 临时添加，用于诊断问题
    private var debugInfoView: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("调试信息:")
                .font(.caption2)
                .foregroundColor(.secondary)
            Text("可用日期数量: \(viewModel.availableDates.count)")
                .font(.caption2)
                .foregroundColor(.secondary)
            Text("刷新状态: \(viewModel.isRefreshing ? "刷新中" : "空闲")")
                .font(.caption2)
                .foregroundColor(.secondary)
            if let selectedDate = viewModel.selectedDate {
                Text("选中日期: \(formatDate(selectedDate))")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            // 显示前3个可用日期
            if !viewModel.availableDates.isEmpty {
                VStack(alignment: .leading, spacing: 2) {
                    Text("前3个可用日期:")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    ForEach(Array(viewModel.availableDates.prefix(3).enumerated()), id: \.offset) { index, date in
                        Text("\(index + 1). \(formatDate(date))")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color.yellow.opacity(0.1))
        .cornerRadius(8)
        .padding(.horizontal)
    }

    private var datePickerButton: some View {
        Button(action: {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                isCalendarExpanded.toggle()
            }
        }) {
            HStack {
                Image(systemName: "calendar")
                    .foregroundColor(.accentColor)
                Text(formatDate(viewModel.selectedDate ?? Date()))
                    .foregroundColor(.primary)
                Image(systemName: isCalendarExpanded ? "chevron.up" : "chevron.down")
                    .foregroundColor(.secondary)
                    .font(.caption)
            }
            .padding(8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(.systemGray6))
            )
        }
    }

    private var videoPlayer: some View {
        ZStack { // Removed alignment for the button
            // 根据是否全屏显示不同大小的播放器
            Group {
                if viewModel.isFullscreen {
                    // When isFullscreen is true, FullscreenVideoPlayerView is presented via .fullScreenCover.
                    // This area will be obscured by the cover.
                    VideoPlayer(player: viewModel.player)
                        .edgesIgnoringSafeArea(.all)
                } else {
                    VideoPlayer(player: viewModel.player)
                        .frame(height: 220)
                        .clipShape(RoundedRectangle(cornerRadius: 12))
                        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
                        .padding(.horizontal)
                    
                    // Removed the explicit fullscreen toggle Button
                }
            }
        }
        .fullScreenCover(isPresented: $viewModel.isFullscreen, onDismiss: {
            // Logic is handled by setFullscreen(false) in ViewModel
        }) {
            FullscreenVideoPlayerView(viewModel: viewModel)
        }
        .onReceive(NotificationCenter.default.publisher(for: UIDevice.orientationDidChangeNotification)) { _ in
            handleDeviceOrientationChange()
        }
    }

    private func handleDeviceOrientationChange() {
        let orientation = UIDevice.current.orientation
        guard orientation.isValidInterfaceOrientation else { return }

        // Trigger fullscreen when device rotates to landscape, if not already fullscreen and a video is selected.
        if !viewModel.isFullscreen && viewModel.currentSegment != nil && orientation.isLandscape {
            // Ensure this runs on the main thread if it involves UI updates directly
            // or if viewModel.setFullscreen itself isn't fully main-actor isolated for all its effects.
            // viewModel.setFullscreen is @MainActor, so direct call is fine.
            viewModel.setFullscreen(true)
        }
        // Exiting fullscreen when rotating to portrait (while fullscreen) is handled by VideoPlayerViewModel.handleOrientationChange
    }

    private func statsView(_ stats: VideoPlayerViewModel.DailyStats) -> some View {
        HStack(spacing: 16) {
            StatItem(
                icon: "video.fill",
                value: "\(stats.videoCount)",
                label: "Videos",
                color: .accentColor
            )
            StatItem(
                icon: "clock.fill",
                value: formatDuration(stats.totalDuration),
                label: "Duration",
                color: .accentColor
            )
        }
    }

    // MARK: - 辅助方法
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMM d, yyyy"
        return formatter.string(from: date)
    }

    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration / 60)
        return String(format: NSLocalizedString("summary_duration_minutes_only", comment: "Duration with minutes only"), minutes)
    }
}

// 背景视图
private struct BackgroundView: View {
    private let gradient = LinearGradient(
        colors: [
            Color.themePrimary.opacity(0.05),
            Color.themeSecondary.opacity(0.08)
        ],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )

    var body: some View {
        ZStack {
            Color(.systemBackground)
                .ignoresSafeArea()
            gradient
                .ignoresSafeArea()
        }
    }
}

// 可滚动内容
private struct ScrollableContent: View {
    @ObservedObject var viewModel: VideoPlayerViewModel
    @Binding var scrollProxy: ScrollViewProxy?

    var body: some View {
        ScrollViewReader { proxy in
            RefreshableScrollView(
                showsIndicators: false,
                onRefresh: { done in
                    Task {
                        // 使用新的 refreshLatestData 方法来自动选择最新日期
                        await viewModel.refreshLatestData()
                        done()
                    }
                }
            ) {
                LazyVStack(spacing: 12) {
                    if viewModel.shouldShowRefreshHint {
                        RefreshHintView()
                            .padding(.top, 8)
                    }

                    ForEach(viewModel.dailyStats?.segments ?? []) { segment in
                        VideoSegmentRow(
                            segment: segment,
                            isSelected: viewModel.currentSegment?.id == segment.id,
                            accentColor: .accentColor,
                            viewModel: viewModel,
                            action: { viewModel.togglePlayback(segment) }
                        )
                        .task {
                            await viewModel.handleSegmentAppear(segment)
                        }
                    }
                }
                .padding(.horizontal)
                .padding(.vertical, 12)
            }
            .onAppear { scrollProxy = proxy }
        }
    }
}

// 加载状态覆盖层
private struct LoadingOverlay: View {
    @ObservedObject var viewModel: VideoPlayerViewModel

    var body: some View {
        ZStack {
            if viewModel.loadingStates.contains(.videoList) {
                LoadingView(isLoading: true, style: .inline)
            }
            if viewModel.loadingStates.contains(.initial) {
                LoadingView(isLoading: true, style: .fullscreen)
            }
        }
    }
}
