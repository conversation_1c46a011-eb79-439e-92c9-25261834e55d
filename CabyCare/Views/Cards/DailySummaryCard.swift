import SwiftUI

struct DailySummaryCard: View {
    let videoCount: Int
    let totalDuration: TimeInterval
    let accentColor: Color
    let secondaryColor: Color

    var body: some View {
        HStack(spacing: 20) {
            // 视频数量统计
            StatItem(
                icon: "video.fill",
                value: "\(videoCount)",
                label: "Videos",
                color: accentColor
            )

            // 总时长统计
            StatItem(
                icon: "clock.fill",
                value: formatDuration(totalDuration),
                label: "Duration",
                color: secondaryColor
            )
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 8)
        )
    }

    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = (Int(duration) % 3600) / 60

        if hours > 0 {
            return String(format: NSLocalizedString("summary_duration_hours_minutes", comment: "Duration with hours and minutes"), hours, minutes)
        } else {
            return String(format: NSLocalizedString("summary_duration_minutes_only", comment: "Duration with minutes only"), minutes)
        }
    }
}
