import SwiftUI

struct CatStatCard: View {
    let catStat: VideoPlayerViewModel.CatDailyStat
    let accentColor: Color
    let secondaryColor: Color
    
    var body: some View {
        HStack(spacing: 20) {
            // 猫咪图标
            catStat.cat.styledIcon()
                .frame(width: 40, height: 40)
            
            VStack(alignment: .leading, spacing: 4) {
                // 猫咪名字
                Text(catStat.cat.rawValue)
                    .font(.headline)
                
                // 统计数据
                HStack(spacing: 16) {
                    // 排泄次数
                    StatLabel(
                        icon: "number",
                        value: "\(catStat.excretionCount)",
                        color: accentColor
                    )
                    
                    // 总时长
                    StatLabel(
                        icon: "clock.fill",
                        value: formatDuration(catStat.totalDuration),
                        color: secondaryColor
                    )
                }
            }
            
            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 8)
        )
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = (Int(duration) % 3600) / 60
        
        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
}

// 统计标签组件
private struct StatLabel: View {
    let icon: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: icon)
                .font(.system(size: 12))
                .foregroundColor(color)
            Text(value)
                .font(.system(.subheadline, design: .rounded))
                .foregroundColor(.primary)
        }
    }
} 