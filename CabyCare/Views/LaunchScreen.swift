import SwiftUI

struct LaunchScreen: View {
    @State private var isAnimating = false
    @State private var xOffset: CGFloat = -200
    @State private var yOffset: CGFloat = 0
    @State private var rotation: Double = 15

    var onLaunchComplete: () -> Void

    var body: some View {
        ZStack {
            // 背景
            Color.themePrimary
                .edgesIgnoringSafeArea(.all)

            VStack(spacing: 30) {
                Image(systemName: "moon.fill")
                    .resizable()
                    .scaledToFit()
                    .frame(width: 120, height: 120)
                    .foregroundColor(.themeSecondary)
                    .rotationEffect(.degrees(rotation))
                    .offset(x: xOffset, y: yOffset)
                    .opacity(isAnimating ? 1 : 0)

                // Loading indicator
                ProgressView()
                    .scaleEffect(1.2)
                    .tint(.themeSecondary)
                    .opacity(isAnimating ? 1 : 0)
                    .offset(x: 0, y: 200)
            }
        }
        .onAppear {
            // 淡入动画
            withAnimation(.easeOut(duration: 1.5)) {
                isAnimating = true
                yOffset = -50
            }

            // 奔跑动画
            withAnimation(
                .easeInOut(duration: 1.5)
            ) {
                xOffset = 0
                yOffset = -100
                rotation = 0
            }

            // 2秒后完成启动
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                onLaunchComplete()
            }
        }
    }
}

#Preview {
    LaunchScreen(onLaunchComplete: {})
}
