import SwiftUI
import AVKit

struct FullscreenVideoPlayerView: View {
    @ObservedObject var viewModel: VideoPlayerViewModel

    var body: some View {
        ZStack {
            // 黑色背景
            Color.black.edgesIgnoringSafeArea(.all)

            GeometryReader { geometry in
                // 视频播放器
                VideoPlayer(player: viewModel.player)
                    // Explicitly set the frame to the geometry provided by the container.
                    // This should ensure the VideoPlayer gets the correct dimensions
                    // once the FullscreenVideoPlayerView itself has the correct (e.g., landscape) geometry.
                    .frame(width: geometry.size.width, height: geometry.size.height)
            }
            .edgesIgnoringSafeArea(.all) // Ensure GeometryReader also ignores safe area
        }
        .onAppear {
            // Log when the view appears and what the viewModel's landscape state is.
            print("[FullscreenVideoPlayerView] Appeared. viewModel.isLandscape: \(viewModel.isLandscape)")
        }
    }
}
