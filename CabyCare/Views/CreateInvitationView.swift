import SwiftUI

struct CreateInvitationView: View {
    @Environment(\.presentationMode) var presentationMode
    
    // 邀请表单数据
    @State private var selectedGroupId: String = ""
    @State private var inviteeEmail: String = ""
    @State private var role: Int = 3 // 默认为普通成员角色
    @State private var message: String = ""
    
    // UI 状态
    @State private var isLoading = false
    @State private var errorMessage: String? = nil
    @State private var showSuccess = false
    
    // 从 DeviceAndGroupManagementView 传入的家庭组列表
    let groups: [FamilyGroup]
    
    // 创建Manager实例
    @StateObject private var manager = DeviceAndGroupManager()
    
    // 可用角色选项
    private let roleOptions = [
        (id: 2, name: "管理员"),
        (id: 3, name: "成员")
    ]
    
    var body: some View {
        NavigationView {
            Form {
                // 选择家庭组
                Section(header: Text("选择家庭组")) {
                    if groups.isEmpty {
                        Text("没有可用的家庭组")
                            .foregroundColor(.gray)
                    } else {
                        Picker("家庭组", selection: $selectedGroupId) {
                            Text("请选择").tag("")
                            ForEach(groups) { group in
                                if group.role == .owner || group.role == .admin {
                                    Text(group.name).tag(group.id)
                                }
                            }
                        }
                        .pickerStyle(MenuPickerStyle())
                    }
                }
                
                // 邀请信息
                Section(header: Text("邀请用户")) {
                    TextField("电子邮件", text: $inviteeEmail)
                        .keyboardType(.emailAddress)
                        .autocapitalization(.none)
                        .autocorrectionDisabled(true)
                    
                    Picker("用户角色", selection: $role) {
                        ForEach(roleOptions, id: \.id) { roleOption in
                            Text(roleOption.name).tag(roleOption.id)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    
                    TextEditor(text: $message)
                        .frame(height: 100)
                        .overlay(
                            RoundedRectangle(cornerRadius: 5)
                                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                        )
                        .overlay(
                            Group {
                                if message.isEmpty {
                                    Text("可选消息")
                                        .foregroundColor(.gray)
                                        .padding(.leading, 5)
                                        .padding(.top, 8)
                                        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
                                }
                            }
                        )
                }
                
                // 错误消息
                if let error = errorMessage {
                    Section {
                        Text(error)
                            .foregroundColor(.red)
                            .font(.footnote)
                    }
                }
                
                // 发送按钮
                Section {
                    Button(action: sendInvitation) {
                        if isLoading {
                            HStack {
                                Spacer()
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                                Text("发送中...")
                                    .foregroundColor(.white)
                                Spacer()
                            }
                        } else {
                            Text("发送邀请")
                                .bold()
                                .frame(maxWidth: .infinity)
                                .foregroundColor(.white)
                        }
                    }
                    .padding()
                    .background(isFormValid ? Color.blue : Color.gray)
                    .cornerRadius(10)
                    .disabled(!isFormValid || isLoading)
                }
            }
            .navigationTitle("邀请成员")
            .navigationBarItems(
                leading: Button("取消") {
                    presentationMode.wrappedValue.dismiss()
                }
            )
            .alert(isPresented: $showSuccess) {
                Alert(
                    title: Text("邀请已发送"),
                    message: Text("已成功向 \(inviteeEmail) 发送邀请"),
                    dismissButton: .default(Text("确定")) {
                        presentationMode.wrappedValue.dismiss()
                    }
                )
            }
        }
    }
    
    // 表单验证
    private var isFormValid: Bool {
        !selectedGroupId.isEmpty && 
        !inviteeEmail.isEmpty && 
        inviteeEmail.contains("@") &&
        inviteeEmail.contains(".")
    }
    
    // 发送邀请 - 重构为使用Manager
    private func sendInvitation() {
        guard isFormValid else { return }
        
        isLoading = true
        errorMessage = nil
        
        Task {
            do {
                // 使用Manager发送邀请
                try await manager.createInvitation(
                    groupId: selectedGroupId,
                    inviteeEmail: inviteeEmail,
                    role: role,
                    message: message
                )
                
                await MainActor.run {
                    self.isLoading = false
                    self.showSuccess = true
                }
                
            } catch {
                Log.error("❌ 创建邀请失败: \(error.localizedDescription)")
                
                await MainActor.run {
                    self.isLoading = false
                    self.errorMessage = "发送邀请失败: \(error.localizedDescription)"
                }
            }
        }
    }
}

struct CreateInvitationView_Previews: PreviewProvider {
    static var previews: some View {
        // 创建示例家庭组数据用于预览
        let sampleGroups = [
            FamilyGroup(id: "group1", name: "家庭组 1", description: "示例家庭组", memberCount: 3, deviceCount: 2, role: .owner),
            FamilyGroup(id: "group2", name: "家庭组 2", description: "示例家庭组 2", memberCount: 2, deviceCount: 1, role: .member)
        ]
        
        return CreateInvitationView(groups: sampleGroups)
    }
}
