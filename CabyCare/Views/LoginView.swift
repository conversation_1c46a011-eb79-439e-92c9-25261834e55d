import SwiftUI

struct LoginView: View {
    @StateObject private var authManager = AuthManager.shared
    @State private var isLoading = false
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        ZStack {
            // Background Image with full screen coverage
            backgroundImage
                .resizable()
                .aspectRatio(contentMode: .fill)
                .ignoresSafeArea(.all) // Cover entire screen including safe areas
            
            // Subtle gradient overlay for better text readability
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.black.opacity(0.2),
                    Color.black.opacity(0.1),
                    Color.black.opacity(0.3)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea(.all)
            
            // Main content - positioned at bottom
            VStack(spacing: 0) {
                Spacer()
                
                // Content without card container
                VStack(spacing: 40) {
                    // App branding section
                    brandingSection
                    
                    // Login section
                    loginSection
                    
                    // Error section
                    if let error = authManager.error {
                        errorSection(error: error)
                    }
                }
                .padding(.horizontal, 32)
                .padding(.bottom, 60) // Bottom padding for safe area
            }
        }
    }
    
    // MARK: - Background Image
    private var backgroundImage: Image {
        colorScheme == .dark ? Image("login.ebony") : Image("login.coconut")
    }
    
    // MARK: - Branding Section
    private var brandingSection: some View {
        VStack(spacing: 16) {
            // App title with enhanced readability
            VStack(spacing: 12) {
                Text(NSLocalizedString("cabycare_title", comment: ""))        
                    .font(Font.custom("Chillax-Medium", size: 36))
                    .foregroundColor(.white)
                    .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
                
                Text(NSLocalizedString("cabycare_slogan", comment: ""))
                    .font(Font.custom("Chillax-Light", size: 18))
                    .foregroundColor(.white.opacity(0.9))
                    .multilineTextAlignment(.center)
                    .shadow(color: .black.opacity(0.3), radius: 1, x: 0, y: 1)
            }
        }
    }
    
    // MARK: - Login Section
    private var loginSection: some View {
        VStack(spacing: 24) {
            if authManager.isLoading || isLoading {
                // Loading state with enhanced visibility
                VStack(spacing: 16) {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.3)
                    
                    Text(NSLocalizedString("login_loading", comment: ""))
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                        .shadow(color: .black.opacity(0.3), radius: 1, x: 0, y: 1)
                }
                .frame(height: 56)
            } else {
                // Login button with enhanced design
                Button(action: handleLoginTap) {
                    HStack(spacing: 12) {                        
                        Text(NSLocalizedString("login_button", comment: ""))
                            .font(.system(size: 18, weight: .semibold))
                    }
                    .foregroundColor(.white)
                    .frame(width: 340)
                    .frame(height: 56)
                    .background(
                        RoundedRectangle(cornerRadius: 14)
                            .fill(
                                LinearGradient(
                                    colors: [
                                        AppTheme.buttonColor(for: colorScheme),
                                        AppTheme.buttonColor(for: colorScheme)
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .shadow(color: .black.opacity(0.3), radius: 10, x: 0, y: 5)
                    )
                }
                .scaleEffect(1.0)
                .animation(.easeInOut(duration: 0.1), value: isLoading)
                .disabled(authManager.isLoading)
            }
        }
    }
    
    // MARK: - Error Section
    private func errorSection(error: String) -> some View {
        VStack(spacing: 16) {
            HStack(spacing: 12) {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(.red)
                    .shadow(color: .black.opacity(0.3), radius: 1, x: 0, y: 1)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(NSLocalizedString("login_error", comment: ""))
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                        .shadow(color: .black.opacity(0.5), radius: 1, x: 0, y: 1)
                    
                    Text(error)
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.leading)
                        .shadow(color: .black.opacity(0.3), radius: 1, x: 0, y: 1)
                }
                
                Spacer()
            }
            
            Button(action: {
                authManager.error = nil
            }) {
                Text(NSLocalizedString("login_retry", comment: ""))
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.white.opacity(0.2))
                            .overlay(
                                RoundedRectangle(cornerRadius: 16)
                                    .stroke(Color.white.opacity(0.3), lineWidth: 1)
                            )
                    )
                    .shadow(color: .black.opacity(0.2), radius: 2, x: 0, y: 1)
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.black.opacity(0.3))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.red.opacity(0.5), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Actions
    private func handleLoginTap() {
        Log.info("🔒 用户点击登录按钮 - 使用原生Logto SDK")
        isLoading = true
        
        Task {
            await authManager.signInNative()
            
            await MainActor.run {
                isLoading = false
                if authManager.isAuthenticated {
                    Log.info("🎉 原生登录成功")
                }
            }
        }
    }
}

// MARK: - Preview
#Preview {
    LoginView()
        .preferredColorScheme(.light)
}

#Preview("Dark Mode") {
    LoginView()
        .preferredColorScheme(.dark)
} 