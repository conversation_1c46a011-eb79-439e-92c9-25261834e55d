import SwiftUI

struct VideoSegmentRow: View {
    let segment: VideoSegment
    let isSelected: Bool
    let accentColor: Color
    let viewModel: VideoPlayerViewModel
    let action: () -> Void
    
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 12) {
                // Top row with cat info and time
                HStack(spacing: 12) {
                    // Cat avatar and name section
                    HStack(spacing: 8) {
                        // Cat avatar
                        CatAvatarView(
                            avatarUrl: viewModel.getCatAvatarUrl(for: segment.animal_id),
                            size: 40,
                            cornerRadius: 20
                        )
                        
                        VStack(alignment: .leading, spacing: 2) {
                            // Cat name
                            Text(viewModel.getCatName(for: segment.animal_id))
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(isSelected ? accentColor : .primary)
                                .lineLimit(1)
                            
                            // Time
                            Text(formattedTimeWithDeviceTimezone)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 2) {
                        // Duration with enhanced visual design
                        HStack(spacing: 4) {
                            Image(systemName: "clock.fill")
                                .font(.caption2)
                                .foregroundColor(isSelected ? accentColor.opacity(0.8) : .secondary)
                            
                            Text(segment.formattedDuration)
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(isSelected ? accentColor : .primary)
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(isSelected ? accentColor.opacity(0.1) : Color(.systemGray6))
                        )
                        
                        // Device name
                        if let deviceName = segment.deviceName {
                            Text(deviceName)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .lineLimit(1)
                        }
                    }
                }
                
                // Weight information section - Re-designed per Apple Human Interface Guidelines
                Divider()
                    .padding(.horizontal, 16)
                    .padding(.top, 4)

                HStack(spacing: 0) {
                    weightItem(value: segment.weight_cat,
                               icon: "cat",
                               label: "Cat",
                               unit: "kg",
                               highlighted: true)
                        .frame(maxWidth: .infinity)

                    weightItem(value: segment.weight_litter,
                               icon: "scalemass",
                               label: "Litter",
                               unit: "kg",
                               highlighted: false)
                        .frame(maxWidth: .infinity)
                }
                .padding(.horizontal, 16)
                .padding(.bottom, 4)
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(backgroundColor)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(isSelected ? accentColor : Color.clear, lineWidth: 2)
            )
            .shadow(
                color: AppTheme.cardShadowColor(for: colorScheme),
                radius: isSelected ? 8 : 4,
                x: 0,
                y: isSelected ? 4 : 2
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // 使用设备时区格式化时间
    private var formattedTimeWithDeviceTimezone: String {
        let deviceTimezone = viewModel.getDeviceTimezone()
        return DateFormatterUtil.formatTimeWithDeviceTimezone(segment.start, timezone: deviceTimezone)
    }
    
    private func weightItem(value: Double, icon: String, label: String, unit: String, highlighted: Bool) -> some View {
        // Only display valid weight values
        let isValidWeight = value > -10 // Assuming negative values are invalid placeholders
        
        return VStack(spacing: 2) {
            // Icon
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(highlighted ? accentColor : .secondary)
                .frame(height: 14)
            
            // Value and unit
            if isValidWeight {
                HStack(spacing: 2) {
                    if unit == "g" {
                        Text(formatWasteWeight(value))
                            .font(highlighted ? .body : .caption)
                            .fontWeight(highlighted ? .bold : .medium)
                            .foregroundColor(highlighted ? accentColor : .primary)
                    } else {
                        Text(formatWeight(value))
                            .font(highlighted ? .body : .caption)
                            .fontWeight(highlighted ? .bold : .medium)
                            .foregroundColor(highlighted ? accentColor : .primary)
                    }
                    
                    Text(unit)
                        .font(highlighted ? .caption : .caption2)
                        .foregroundColor(highlighted ? accentColor : .primary)
                }
            } else {
                Text("-- \(unit)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .fontWeight(.medium)
            }
            
            // Label
            Text(label)
                .font(highlighted ? .caption : .caption2)
                .foregroundColor(highlighted ? accentColor : .secondary)
        }
        .frame(maxWidth: .infinity)
    }
    
    // Format regular weight (kg) with 2 decimal places
    private func formatWeight(_ value: Double) -> String {
        return String(format: "%.2f", value)
    }
    
    // Convert kg to g and format with 0 decimal places
    private func formatWasteWeight(_ valueInKg: Double) -> String {
        let valueInGrams = valueInKg * 1000
        return String(format: "%.0f", valueInGrams)
    }
    
    private var backgroundColor: Color {
        if isSelected {
            return accentColor.opacity(0.15)
        } else {
            return AppTheme.cardBackgroundColor(for: colorScheme)
        }
    }
}

// Preview
struct VideoSegmentRow_Previews: PreviewProvider {
    static var previews: some View {
        // Create a sample segment with animal_id
        let segment = VideoSegment(
            start: Date(),
            duration: 120,
            url: "https://example.com/video.mp4",
            weightLitter: 8.5,
            weightCat: 4.2,
            weightWaste: 0.8,
            index: 0,
            deviceName: "Kitchen Monitor",
            animalId: "sample_cat_id"
        )
        
        let viewModel = VideoPlayerViewModel()
        
        return Group {
            VideoSegmentRow(
                segment: segment,
                isSelected: false,
                accentColor: .blue,
                viewModel: viewModel,
                action: {}
            )
            .padding()
            .previewDisplayName("Normal")
            
            VideoSegmentRow(
                segment: segment,
                isSelected: true,
                accentColor: .blue,
                viewModel: viewModel,
                action: {}
            )
            .padding()
            .previewDisplayName("Selected")
        }
        .previewLayout(.sizeThatFits)
    }
}
