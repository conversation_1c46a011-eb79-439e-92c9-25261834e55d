import SwiftUI
import CoreBluetooth

struct BluetoothView: View {
    @StateObject private var bluetoothManager = BluetoothManager()
    @State private var ssid = ""
    @State private var password = ""
    @State private var showWiFiConfirmAlert = false
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 顶部状态卡片
                    StatusCardView(bluetoothManager: bluetoothManager)
                    
                    // 蓝牙权限/状态提示
                    if !bluetoothManager.isBluetoothReady {
                        BluetoothPermissionView(bluetoothManager: bluetoothManager)
                    }
                    
                    // 设备扫描区域
                    if bluetoothManager.isBluetoothReady {
                        DeviceScanSectionView(bluetoothManager: bluetoothManager)
                    }
                    
                    // 设备配置区域（当设备就绪时显示）
                    if bluetoothManager.connectionStatus == .ready {
                        // 设备激活区域（放到前面）
                        DeviceActivationSectionView(bluetoothManager: bluetoothManager)
                        
                        // WiFi 配置区域（移到后面）
                        WiFiConfigSectionView(
                            bluetoothManager: bluetoothManager,
                            ssid: $ssid,
                            password: $password,
                            showConfirmAlert: $showWiFiConfirmAlert
                        )
                    }
                }
                .padding()
            }
            .navigationTitle("蓝牙设备配置")
            .navigationBarTitleDisplayMode(.large)
            .onAppear {
                bluetoothManager.requestScanningIfNeeded()
            }
            .onDisappear {
                bluetoothManager.stopScanning()
            }
            .alert("错误", isPresented: .constant(bluetoothManager.errorMessage != nil)) {
                Button("确定") {
                    bluetoothManager.errorMessage = nil
                }
            } message: {
                if let errorMessage = bluetoothManager.errorMessage {
                    Text(errorMessage)
                }
            }
            .alert("发送成功", isPresented: $bluetoothManager.showSuccessAlert) {
                Button("确定", role: .cancel) { }
            } message: {
                Text(bluetoothManager.lastSuccessMessage)
            }
            .alert("确认发送WiFi配置", isPresented: $showWiFiConfirmAlert) {
                Button("取消", role: .cancel) { }
                Button("确认发送") {
                    bluetoothManager.sendWiFiConfig(ssid: ssid, password: password)
                }
            } message: {
                Text("即将发送以下WiFi配置到设备：\n\n网络名称：\(ssid)\n密码：\(password.isEmpty ? "无密码" : password)")
            }
        }
    }
}

// MARK: - 状态卡片视图
struct StatusCardView: View {
    @ObservedObject var bluetoothManager: BluetoothManager
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: bluetoothIcon)
                    .foregroundColor(statusColor)
                    .font(.title2)
                
                VStack(alignment: .leading) {
                    Text("蓝牙状态")
                        .font(.headline)
                    Text(bluetoothManager.bluetoothStatusMessage)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text(connectionStatusText)
                        .font(.subheadline)
                        .foregroundColor(statusColor)
                        .fontWeight(.medium)
                    
                    if bluetoothManager.isBluetoothReady && bluetoothManager.isScanning {
                        HStack {
                            ProgressView()
                                .scaleEffect(0.8)
                            Text("扫描中")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
            
            if let device = bluetoothManager.connectedDevice {
                Divider()
                HStack {
                    Text("已连接设备:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(device.name ?? "未知设备")
                        .font(.caption)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    Button("断开连接") {
                        bluetoothManager.disconnectDevice()
                    }
                    .font(.caption)
                    .foregroundColor(.red)
                }
            }
        }
        .padding()
        .background(Color(UIColor.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    private var bluetoothIcon: String {
        if !bluetoothManager.isBluetoothReady {
            switch bluetoothManager.bluetoothState {
            case .poweredOff:
                return "bluetooth.slash"
            case .unauthorized:
                return "bluetooth.exclamationmark"
            case .unsupported:
                return "bluetooth.disabled"
            default:
                return "bluetooth"
            }
        }
        
        switch bluetoothManager.connectionStatus {
        case .disconnected:
            return "bluetooth"
        case .connecting, .discovering:
            return "bluetooth.connecting"
        case .connected, .ready:
            return "bluetooth.connected"
        }
    }
    
    private var connectionStatusText: String {
        if !bluetoothManager.isBluetoothReady {
            switch bluetoothManager.bluetoothState {
            case .poweredOn:
                return "蓝牙就绪"
            case .poweredOff:
                return "蓝牙已关闭"
            case .unauthorized:
                return "权限未授权"
            case .unsupported:
                return "不支持蓝牙"
            case .resetting:
                return "蓝牙重置中"
            default:
                return "初始化中"
            }
        }
        
        switch bluetoothManager.connectionStatus {
        case .disconnected:
            return bluetoothManager.isScanning ? "扫描设备中" : "未连接"
        case .connecting:
            return "连接中"
        case .connected:
            return "已连接"
        case .discovering:
            return "发现服务中"
        case .ready:
            return "就绪"
        }
    }
    
    private var statusColor: Color {
        if !bluetoothManager.isBluetoothReady {
            switch bluetoothManager.bluetoothState {
            case .poweredOn:
                return .blue
            case .poweredOff, .unauthorized, .unsupported:
                return .red
            default:
                return .orange
            }
        }
        
        switch bluetoothManager.connectionStatus {
        case .disconnected:
            return bluetoothManager.isScanning ? .blue : .gray
        case .connecting, .discovering:
            return .orange
        case .connected, .ready:
            return .green
        }
    }
}

// MARK: - 设备扫描区域
struct DeviceScanSectionView: View {
    @ObservedObject var bluetoothManager: BluetoothManager
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            // 标题和扫描按钮
            HStack {
                HStack(spacing: 12) {
                    Image(systemName: "antenna.radiowaves.left.and.right")
                        .font(.title2)
                        .foregroundColor(.blue)
                        .symbolRenderingMode(.hierarchical)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("AbyBox 设备")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        Text("发现 \(bluetoothManager.discoveredDevices.count) 台设备")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                Button(action: {
                    if bluetoothManager.isScanning {
                        bluetoothManager.stopScanning()
                    } else {
                        bluetoothManager.startScanning()
                    }
                }) {
                    HStack(spacing: 8) {
                        if bluetoothManager.isScanning {
                            ProgressView()
                                .scaleEffect(0.8)
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            Text("停止")
                        } else {
                            Image(systemName: "magnifyingglass")
                                .font(.system(size: 14, weight: .semibold))
                            Text("扫描")
                        }
                    }
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .fill(bluetoothManager.isScanning ? 
                                LinearGradient(colors: [.red, .orange], startPoint: .leading, endPoint: .trailing) :
                                LinearGradient(colors: [.blue, .purple], startPoint: .leading, endPoint: .trailing)
                            )
                    )
                    .shadow(color: (bluetoothManager.isScanning ? Color.red : Color.blue).opacity(0.3), radius: 8, x: 0, y: 4)
                }
            }
            
            // 设备列表
            if bluetoothManager.discoveredDevices.isEmpty {
                EmptyDeviceListView()
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(bluetoothManager.discoveredDevices, id: \.identifier) { device in
                        EnhancedDeviceRowView(device: device, bluetoothManager: bluetoothManager)
                    }
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(UIColor.systemBackground))
                .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 4)
        )
    }
}

// MARK: - 空设备列表视图
struct EmptyDeviceListView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "antenna.radiowaves.left.and.right.slash")
                .font(.system(size: 48))
                .foregroundColor(.gray.opacity(0.6))
                .symbolRenderingMode(.hierarchical)
            
            VStack(spacing: 8) {
                Text("未发现设备")
                    .font(.headline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text("请确保 AbyBox 设备已开启\n并处于可发现模式")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineSpacing(2)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 32)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(UIColor.secondarySystemBackground).opacity(0.5))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

// MARK: - 增强设备行视图
struct EnhancedDeviceRowView: View {
    let device: CBPeripheral
    @ObservedObject var bluetoothManager: BluetoothManager
    @Environment(\.colorScheme) var colorScheme
    @State private var isPressed = false
    
    var body: some View {
        HStack(spacing: 16) {
            // 设备图标
            deviceIconView
            
            // 设备信息
            deviceInfoView
            
            Spacer()
            
            // 连接按钮
            connectButtonView
        }
        .padding(16)
        .background(deviceRowBackground)
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
    
    private var deviceIconView: some View {
        ZStack {
            Circle()
                .fill(iconGradient)
                .frame(width: 50, height: 50)
            
            Image(systemName: deviceIcon)
                .font(.system(size: 24, weight: .medium))
                .foregroundColor(.blue)
                .symbolRenderingMode(.hierarchical)
        }
    }
    
    private var iconGradient: LinearGradient {
        LinearGradient(
            colors: [.blue.opacity(0.2), .purple.opacity(0.2)], 
            startPoint: .topLeading, 
            endPoint: .bottomTrailing
        )
    }
    
    private var deviceInfoView: some View {
        VStack(alignment: .leading, spacing: 6) {
            HStack {
                Text(device.name ?? "未知设备")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Spacer()
                
                // 连接状态指示器
                if isConnected {
                    connectionStatusView
                }
            }
            
            deviceIdView
            signalStrengthView
        }
    }
    
    private var connectionStatusView: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(Color.green)
                .frame(width: 8, height: 8)
            Text("已连接")
                .font(.caption)
                .foregroundColor(.green)
                .fontWeight(.medium)
        }
    }
    
    private var deviceIdView: some View {
        HStack(spacing: 8) {
            Image(systemName: "number")
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(shortDeviceId)
                .font(.system(.caption, design: .monospaced))
                .foregroundColor(.secondary)
        }
    }
    
    private var signalStrengthView: some View {
        HStack(spacing: 8) {
            Image(systemName: "antenna.radiowaves.left.and.right")
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text("信号强度: 良好")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    private var connectButtonView: some View {
        Button(action: {
            bluetoothManager.connectToDevice(device)
        }) {
            HStack(spacing: 6) {
                buttonIconView
                
                Text(buttonTitle)
                    .font(.caption)
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(buttonBackgroundView)
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .disabled(!canConnect)
        .onTapGesture {
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = true
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = false
                }
            }
        }
    }
    
    private var buttonIconView: some View {
        Group {
            if isConnecting {
                ProgressView()
                    .scaleEffect(0.8)
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
            } else {
                Image(systemName: isConnected ? "checkmark" : "link")
                    .font(.system(size: 12, weight: .semibold))
            }
        }
    }
    
    private var buttonBackgroundView: some View {
        RoundedRectangle(cornerRadius: 8)
            .fill(buttonGradient)
    }
    
    private var deviceRowBackground: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(Color(UIColor.secondarySystemBackground))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isConnected ? Color.green.opacity(0.3) : Color.clear, lineWidth: 2)
            )
    }
    
    private var deviceIcon: String {
        return "cube.box"
    }
    
    private var isConnected: Bool {
        bluetoothManager.connectedDevice?.identifier == device.identifier
    }
    
    private var isConnecting: Bool {
        bluetoothManager.connectionStatus == .connecting && 
        bluetoothManager.connectedDevice?.identifier == device.identifier
    }
    
    private var canConnect: Bool {
        bluetoothManager.connectionStatus == .disconnected || isConnected
    }
    
    private var buttonTitle: String {
        if isConnecting {
            return "连接中"
        } else if isConnected {
            return "已连接"
        } else {
            return "连接"
        }
    }
    
    private var buttonGradient: LinearGradient {
        if isConnected {
            return LinearGradient(
                colors: [.green, .green.opacity(0.8)], 
                startPoint: .leading, 
                endPoint: .trailing
            )
        } else if canConnect {
            return LinearGradient(
                colors: [.blue, .purple], 
                startPoint: .leading, 
                endPoint: .trailing
            )
        } else {
            return LinearGradient(
                colors: [.gray.opacity(0.6), .gray.opacity(0.4)], 
                startPoint: .leading, 
                endPoint: .trailing
            )
        }
    }
    
    private var shortDeviceId: String {
        let fullId = device.identifier.uuidString
        return String(fullId.prefix(8)) + "..." + String(fullId.suffix(4))
    }
}

// MARK: - 美化的发送按钮组件
struct ModernSendButton: View {
    let title: String
    let icon: String
    let gradientColors: [Color]
    let sendStatus: BluetoothManager.SendStatus
    let isEnabled: Bool
    let action: () -> Void
    
    @State private var isPressed = false
    @State private var scale: CGFloat = 1.0
    
    var body: some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = true
                scale = 0.95
            }
            
            action()
            
            withAnimation(.easeInOut(duration: 0.1).delay(0.1)) {
                isPressed = false
                scale = 1.0
            }
        }) {
            HStack(spacing: 12) {
                if sendStatus == .sending {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                } else {
                    Image(systemName: sendStatus == .success ? "checkmark.circle.fill" : icon)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                }
                
                Text(buttonText)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(
                Group {
                    if isEnabled && sendStatus != .failed {
                        LinearGradient(
                            gradient: Gradient(colors: sendStatus == .success ? [.green, .green.opacity(0.8)] : gradientColors),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    } else {
                        LinearGradient(
                            gradient: Gradient(colors: [.gray.opacity(0.6), .gray.opacity(0.4)]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    }
                }
            )
            .cornerRadius(12)
            .shadow(
                color: isEnabled ? gradientColors.first?.opacity(0.3) ?? .clear : .clear,
                radius: isPressed ? 2 : 8,
                x: 0,
                y: isPressed ? 1 : 4
            )
            .scaleEffect(scale)
            .disabled(!isEnabled || sendStatus == .sending)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var buttonText: String {
        switch sendStatus {
        case .sending:
            return "发送中..."
        case .success:
            return "发送成功!"
        case .failed:
            return "发送失败，重试"
        default:
            return title
        }
    }
}

// MARK: - WiFi 配置区域
struct WiFiConfigSectionView: View {
    @ObservedObject var bluetoothManager: BluetoothManager
    @Binding var ssid: String
    @Binding var password: String
    @Binding var showConfirmAlert: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "wifi")
                    .foregroundColor(.blue)
                Text("WiFi 配置")
                    .font(.headline)
            }
            
            VStack(spacing: 12) {
                VStack(alignment: .leading, spacing: 6) {
                    Text("网络名称 (SSID)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    TextField("请输入 WiFi 名称", text: $ssid)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }
                
                VStack(alignment: .leading, spacing: 6) {
                    Text("密码")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    TextField("请输入 WiFi 密码", text: $password)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }
            }
            
            ModernSendButton(
                title: "发送 WiFi 配置",
                icon: "wifi.circle.fill",
                gradientColors: [.blue, .purple],
                sendStatus: bluetoothManager.wifiSendStatus,
                isEnabled: canSendWiFi
            ) {
                showConfirmAlert = true
            }
        }
        .padding()
        .background(Color(UIColor.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    private var canSendWiFi: Bool {
        bluetoothManager.connectionStatus == .ready && 
        bluetoothManager.wifiCharacteristic != nil &&
        !ssid.isEmpty
    }
}

// MARK: - 设备激活区域
struct DeviceActivationSectionView: View {
    @ObservedObject var bluetoothManager: BluetoothManager
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "checkmark.circle")
                    .foregroundColor(.green)
                Text("设备激活")
                    .font(.headline)
            }
            
            VStack(alignment: .leading, spacing: 12) {
                Text("点击下方按钮激活设备，激活后可进行WiFi配置")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                // 激活按钮
                Button(action: {
                    Task {
                        await bluetoothManager.activateDevice()
                    }
                }) {
                    HStack(spacing: 12) {
                        if bluetoothManager.isActivating || bluetoothManager.isCheckingRegistration {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.8)
                            Text(loadingButtonText)
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.white)
                        } else {
                            Image(systemName: activationIcon)
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.white)
                            Text(activationButtonText)
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.white)
                        }
                    }
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: activationGradientColors),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(12)
                    .shadow(
                        color: shadowColor,
                        radius: 8,
                        x: 0,
                        y: 4
                    )
                }
                .disabled(!isActivationEnabled)
                
                // 状态消息
                if !bluetoothManager.activationMessage.isEmpty {
                    Text(bluetoothManager.activationMessage)
                        .font(.caption)
                        .foregroundColor(activationMessageColor)
                        .padding(.top, 4)
                }
            }
        }
        .padding()
        .background(Color(UIColor.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    private var isActivationEnabled: Bool {
        // 当设备已注册时，禁用激活按钮
        guard bluetoothManager.connectionStatus == .ready else { return false }
        guard !bluetoothManager.isActivating && !bluetoothManager.isCheckingRegistration else { return false }
        
        // 如果设备已注册，禁用按钮
        switch bluetoothManager.activationStatus {
        case .registered:
            return false
        default:
            return true
        }
    }
    
    private var activationIcon: String {
        switch bluetoothManager.activationStatus {
        case .checking:
            return "magnifyingglass"
        case .registered:
            return "checkmark.circle.fill"
        case .success:
            return "checkmark.circle.fill"
        case .failed:
            return "exclamationmark.triangle.fill"
        default:
            return "checkmark.circle.fill"
        }
    }
    
    private var activationButtonText: String {
        switch bluetoothManager.activationStatus {
        case .checking:
            return "检查注册状态"
        case .registered:
            return "设备已激活"
        case .success:
            return "激活成功"
        case .failed:
            return "重新激活"
        default:
            return "激活设备"
        }
    }
    
    private var activationGradientColors: [Color] {
        if !isActivationEnabled {
            return [.gray.opacity(0.6), .gray.opacity(0.4)]
        }
        
        switch bluetoothManager.activationStatus {
        case .checking:
            return [.orange, .orange.opacity(0.8)]
        case .registered:
            return [.gray.opacity(0.6), .gray.opacity(0.4)]
        case .success:
            return [.green, .green.opacity(0.8)]
        case .failed:
            return [.red, .red.opacity(0.8)]
        default:
            return [.green, .blue]
        }
    }
    
    private var activationMessageColor: Color {
        switch bluetoothManager.activationStatus {
        case .checking:
            return .orange
        case .registered:
            return .blue
        case .success:
            return .green
        case .failed:
            return .red
        default:
            return .secondary
        }
    }
    
    private var loadingButtonText: String {
        switch bluetoothManager.activationStatus {
        case .checking:
            return "检查注册中..."
        default:
            return "激活中..."
        }
    }
    
    private var shadowColor: Color {
        if !isActivationEnabled {
            return .clear
        }
        
        switch bluetoothManager.activationStatus {
        case .success:
            return .green.opacity(0.3)
        case .registered:
            return .gray.opacity(0.3)
        case .failed:
            return .red.opacity(0.3)
        default:
            return .green.opacity(0.3)
        }
    }
}

// MARK: - 蓝牙权限/状态视图
struct BluetoothPermissionView: View {
    @ObservedObject var bluetoothManager: BluetoothManager
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: bluetoothIcon)
                    .foregroundColor(bluetoothIconColor)
                    .font(.title2)
                
                Text("蓝牙状态")
                    .font(.headline)
            }
            
            VStack(spacing: 12) {
                Text(bluetoothManager.bluetoothStatusMessage)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                if bluetoothManager.bluetoothState == .unauthorized || bluetoothManager.bluetoothState == .poweredOff {
                    Button(action: {
                        openSettings()
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: "gear")
                                .font(.footnote)
                            Text("打开设置")
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 10)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(AppTheme.primaryColor(for: colorScheme))
                        )
                    }
                } else if bluetoothManager.bluetoothState == .unknown || bluetoothManager.bluetoothState == .resetting {
                    ProgressView()
                        .scaleEffect(0.8)
                        .padding(.top, 8)
                }
            }
        }
        .padding()
        .background(Color(UIColor.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    private var bluetoothIcon: String {
        switch bluetoothManager.bluetoothState {
        case .poweredOn:
            return "bluetooth"
        case .poweredOff:
            return "bluetooth.slash"
        case .unauthorized:
            return "bluetooth.exclamationmark"
        case .unsupported:
            return "bluetooth.disabled"
        default:
            return "bluetooth"
        }
    }
    
    private var bluetoothIconColor: Color {
        switch bluetoothManager.bluetoothState {
        case .poweredOn:
            return .blue
        case .poweredOff, .unauthorized, .unsupported:
            return .red
        default:
            return .orange
        }
    }
    
    private func openSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }
}

#Preview {
    BluetoothView()
} 