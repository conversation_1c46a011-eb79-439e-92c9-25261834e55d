import SwiftUI
import AVKit

struct ContentView: View {
    @StateObject private var viewModel = VideoPlayerViewModel.shared
    @State private var selectedTab = 0
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        TabView(selection: $selectedTab) {
            HomeView()
                .tabItem {
                    Image(systemName: selectedTab == 0 ? "house.fill" : "house")
                        .environment(\.symbolRenderingMode, .hierarchical)
                }
                .tag(0)

            CareView(viewModel: viewModel)
                .tabItem {
                    Image(systemName: selectedTab == 1 ? "heart.circle.fill" : "heart.circle")
                        .environment(\.symbolRenderingMode, .hierarchical)
                }
                .tag(1)

            AnimalsView()
                .tabItem {
                    Image(systemName: selectedTab == 2 ? "pawprint.circle.fill" : "pawprint.circle")
                        .environment(\.symbolRenderingMode, .hierarchical)
                }
                .tag(2)
                
            DeviceAndGroupManagementView()
                .tabItem {
                    Image(systemName: selectedTab == 3 ? "gearshape.2.fill" : "gearshape.2")
                        .environment(\.symbolRenderingMode, .hierarchical)
                }
                .tag(3)
        }
        .tint(AppTheme.primaryColor(for: colorScheme))
        .task {
            // 确保在应用启动时加载初始数据
            await viewModel.ensureInitialDataLoaded()
        }
    }
}

#Preview {
    ContentView()
}
