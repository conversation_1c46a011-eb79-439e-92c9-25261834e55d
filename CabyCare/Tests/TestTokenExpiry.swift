import SwiftUI

/// 用于测试令牌过期逻辑的工具类
@MainActor
class TokenExpiryTester: ObservableObject {
    @Published var lastTestResult: String = ""
    @Published var currentTokenStatus: String = ""
    
    static let shared = TokenExpiryTester()
    
    private init() {
        updateTokenStatus()
    }
    
    // MARK: - 令牌状态检查
    
    /// 更新当前令牌状态
    func updateTokenStatus() {
        let authManager = AuthManager.shared
        let storage = UserDefaultsManager.shared
        
        var status = "📊 当前令牌状态:\n"
        
        // 访问令牌状态
        if let accessToken = storage.accessToken {
            status += "✅ 访问令牌: \(String(accessToken.prefix(10)))...\n"
            
            if let expiresAt = authManager.accessTokenExpiresAt {
                let now = Date()
                let timeUntilExpiry = expiresAt.timeIntervalSince(now)
                
                if timeUntilExpiry > 0 {
                    status += "⏰ 过期时间: 还剩\(Int(timeUntilExpiry))秒\n"
                } else {
                    status += "⚠️ 访问令牌已过期 \(Int(-timeUntilExpiry))秒\n"
                }
            } else {
                status += "❓ 无法获取过期时间\n"
            }
        } else {
            status += "❌ 无访问令牌\n"
        }
        
        // 刷新令牌状态
        if let refreshToken = storage.refreshToken {
            if refreshToken == "native_sdk_managed" {
                status += "🔄 刷新令牌: 原生SDK管理\n"
            } else {
                status += "🔄 刷新令牌: \(String(refreshToken.prefix(10)))...\n"
            }
        } else {
            status += "❌ 无刷新令牌\n"
        }
        
        status += "🔐 认证状态: \(authManager.isAuthenticated ? "已认证" : "未认证")"
        
        self.currentTokenStatus = status
    }
    
    // MARK: - 令牌过期测试方法
    
    /// 手动设置访问令牌过期（通过修改过期时间）
    func forceAccessTokenExpiry() {
        let authManager = AuthManager.shared
        
        // 设置访问令牌在1秒前过期
        let expiredTime = Date().addingTimeInterval(-1)
        authManager.accessTokenExpiresAt = expiredTime
        
        lastTestResult = "✅ 访问令牌已设置为过期状态"
        updateTokenStatus()
        
        Log.info("🧪 测试：访问令牌已手动设置为过期")
    }
    
    /// 手动设置访问令牌即将过期（5分钟内）
    func forceAccessTokenNearExpiry() {
        let authManager = AuthManager.shared
        
        // 设置访问令牌在2分钟后过期（小于5分钟阈值）
        let nearExpiryTime = Date().addingTimeInterval(2 * 60)
        authManager.accessTokenExpiresAt = nearExpiryTime
        
        lastTestResult = "✅ 访问令牌已设置为即将过期状态（2分钟后过期）"
        updateTokenStatus()
        
        Log.info("🧪 测试：访问令牌已设置为即将过期（2分钟后）")
    }
    
    /// 损坏访问令牌（使其格式无效）
    func corruptAccessToken() {
        guard let originalToken = UserDefaultsManager.shared.accessToken else {
            lastTestResult = "❌ 未找到访问令牌"
            return
        }
        
        // 修改令牌末尾，使其在服务端验证失败
        let corruptedToken = originalToken.prefix(originalToken.count - 10) + "CORRUPTED"
        UserDefaultsManager.shared.accessToken = String(corruptedToken)
        
        lastTestResult = "✅ 访问令牌已损坏，下次API请求将触发401错误"
        updateTokenStatus()
        
        Log.info("🧪 测试：访问令牌已被损坏")
    }
    
    /// 损坏刷新令牌（使其格式无效）
    func corruptRefreshToken() {
        guard let originalToken = UserDefaultsManager.shared.refreshToken,
              originalToken != "native_sdk_managed" else {
            lastTestResult = "❌ 未找到可修改的刷新令牌（原生SDK管理）"
            return
        }
        
        // 修改令牌末尾，使其在服务端验证失败
        let corruptedToken = originalToken.prefix(originalToken.count - 10) + "CORRUPTED"
        UserDefaultsManager.shared.refreshToken = String(corruptedToken)
        
        lastTestResult = "✅ 刷新令牌已损坏，令牌刷新将失败"
        updateTokenStatus()
        
        Log.info("🧪 测试：刷新令牌已被损坏")
    }
    
    /// 同时损坏访问令牌和刷新令牌
    func corruptBothTokens() {
        corruptAccessToken()
        corruptRefreshToken()
        
        lastTestResult = "✅ 访问令牌和刷新令牌都已损坏，将导致重新登录"
        updateTokenStatus()
        
        Log.info("🧪 测试：访问令牌和刷新令牌都已被损坏")
    }
    
    /// 清除访问令牌过期时间信息
    func clearTokenExpiryInfo() {
        AuthManager.shared.accessTokenExpiresAt = nil
        
        lastTestResult = "✅ 访问令牌过期时间信息已清除"
        updateTokenStatus()
        
        Log.info("🧪 测试：访问令牌过期时间信息已清除")
    }
    
    // MARK: - 测试刷新机制
    
    /// 手动触发令牌刷新
    func triggerTokenRefresh() async {
        Log.info("🧪 测试：手动触发令牌刷新")
        
        do {
            let newToken = try await AuthManager.shared.refreshToken()
            lastTestResult = "✅ 令牌刷新成功: \(String(newToken.prefix(10)))..."
            updateTokenStatus()
        } catch {
            lastTestResult = "❌ 令牌刷新失败: \(error.localizedDescription)"
            updateTokenStatus()
        }
    }
    
    /// 测试静默刷新机制
    func testSilentRefresh() {
        // 模拟应用进入前台时的静默刷新
        NotificationCenter.default.post(name: NSNotification.Name("TestSilentRefresh"), object: nil)
        
        lastTestResult = "✅ 已触发静默刷新测试"
        Log.info("🧪 测试：触发静默刷新")
    }
    
    // MARK: - 高级测试场景
    
    /// 模拟网络错误的令牌刷新
    func simulateNetworkErrorDuringRefresh() {
        // 先损坏访问令牌，再设置无网络连接场景
        corruptAccessToken()
        
        // 这里可以通过修改网络配置来模拟网络错误
        // 实际测试时需要断开网络连接
        lastTestResult = "✅ 访问令牌已损坏，请断开网络连接后测试刷新失败场景"
        updateTokenStatus()
        
        Log.info("🧪 测试：模拟网络错误的令牌刷新场景")
    }
    
    /// 恢复令牌到正常状态（重新登录）
    func resetTokensToNormalState() async {
        Log.info("🧪 测试：重置令牌状态")
        
        // 通过重新登录来恢复正常状态
        AuthManager.shared.logout()
        
        lastTestResult = "✅ 已登出，令牌已清除。请重新登录以恢复正常状态"
        updateTokenStatus()
    }
    
    // MARK: - 简化的原有方法（保持兼容性）
    
    /// 手动使令牌过期的方法 - 简化版，只修改访问令牌使其无效
    static func forceTokenExpiry() {
        Task { @MainActor in
            TokenExpiryTester.shared.corruptAccessToken()
        }
    }
}
