import Foundation
import UIKit
import Logto
import LogtoClient
import AuthenticationServices

@MainActor
class NativeLogtoManager: NSObject, ObservableObject {
    static let shared = NativeLogtoManager()
    
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private var logtoClient: LogtoClient?
    private let storage = UserDefaultsManager.shared
    
    // MARK: - Custom OIDC Configuration struct with proper field mapping
    private struct CustomOidcConfigResponse: Codable, Equatable {
        let authorizationEndpoint: String
        let tokenEndpoint: String
        let endSessionEndpoint: String
        let revocationEndpoint: String
        let userinfoEndpoint: String
        let jwksUri: String
        let issuer: String
        
        enum CodingKeys: String, CodingKey {
            case authorizationEndpoint = "authorization_endpoint"
            case tokenEndpoint = "token_endpoint"
            case endSessionEndpoint = "end_session_endpoint"
            case revocationEndpoint = "revocation_endpoint"
            case userinfoEndpoint = "userinfo_endpoint"
            case jwksUri = "jwks_uri"
            case issuer = "issuer"
        }
    }
    
    var isLoggedIn: Bool {
            let hasToken = storage.accessToken != nil
            Log.debug("🔐 检查登录状态: \(hasToken ? "已登录" : "未登录")")
            return hasToken
    }
    
    private override init() {
        super.init()
        self.initializeLogtoClient()
    }
    
    private func initializeLogtoClient() {
        do {
            let config = try LogtoConfig(
                endpoint: Configuration.API.Logto.logtoBase,
                appId: Configuration.API.Logto.clientId,
                scopes: ["openid", "profile", "email", "offline_access"],
                usingPersistStorage: true
            )
            
            logtoClient = LogtoClient(useConfig: config)
            Log.info("✅ Logto客户端初始化成功")
        } catch {
            Log.error("❌ Logto客户端初始化失败: \(error.localizedDescription)")
            self.errorMessage = "初始化失败: \(error.localizedDescription)"
        }
    }
    
    var isAuthenticated: Bool {
        return logtoClient?.isAuthenticated ?? false
    }
    
    var accessToken: String? {
        get async {
            return try? await logtoClient?.getAccessToken(for: nil)
        }
    }
    
    func signIn() async throws {
        Log.info("🔐 开始登录流程...")
        Log.info("🎯 确保授权码直接用于后端用户注册")
        Log.info("🎯 调试：signIn() 方法被调用")
        
        isLoading = true
        errorMessage = nil
        
        do {
            Log.info("🎯 调试：准备调用 performManualOIDCFlow()")
            // 不使用原生SDK的自动令牌交换，改为手动获取授权码
            try await performManualOIDCFlow()
            
            isLoading = false
            Log.info("🎉 登录流程完成")
        } catch let logtoError as LogtoClientErrors.SignIn {
            isLoading = false
            Log.info("🔍 登录错误: \(logtoError)")
            await handleNetworkError(logtoError)
        } catch let nsError as NSError {
            isLoading = false
            Log.error("❌ 登录过程中发生系统错误: \(nsError)")
            if nsError.domain == NSURLErrorDomain {
                await handleURLError(nsError)
            } else {
                errorMessage = "登录失败: \(nsError.localizedDescription)"
            }
        } catch {
            Log.error("❌ 登录过程中发生未知错误: \(error)")
            errorMessage = "登录失败: \(error.localizedDescription)"
            isLoading = false
        }        
    }
    
    /// 执行手动OIDC流程，确保授权码用于后端用户注册
    private func performManualOIDCFlow() async throws {
        Log.info("🔄 开始手动OIDC流程，授权码将用于后端用户注册...")
        Log.info("🎯 调试：performManualOIDCFlow() 方法开始执行")
        
        do {
            Log.info("🎯 调试：步骤1 - 获取OIDC配置")
            // 1. 获取OIDC配置
            let oidcConfig = try await fetchOIDCConfiguration()
            Log.info("🎯 调试：OIDC配置获取成功")
            
            Log.info("🎯 调试：步骤2 - 生成PKCE参数")
            // 2. 生成PKCE参数
            let state = LogtoUtilities.generateState()
            let codeVerifier = LogtoUtilities.generateCodeVerifier()
            let codeChallenge = LogtoUtilities.generateCodeChallenge(codeVerifier: codeVerifier)
            Log.info("🎯 调试：PKCE参数生成完成 - state: \(String(state.prefix(10)))...")
            
            Log.info("🎯 调试：步骤3 - 保存参数")
            // 3. 保存参数用于后续验证
            storage.authState = state
            storage.setString(key: "code_verifier", value: codeVerifier)
            Log.info("🎯 调试：参数已保存到storage")
            
            Log.info("🎯 调试：步骤4 - 构建授权URL")
            // 4. 构建授权URL
            let redirectUri = URL(string: Configuration.API.Logto.appCallback)!
            var components = URLComponents(string: oidcConfig.authorizationEndpoint)!
            components.queryItems = [
                URLQueryItem(name: "client_id", value: Configuration.API.Logto.clientId),
                URLQueryItem(name: "redirect_uri", value: redirectUri.absoluteString),
                URLQueryItem(name: "response_type", value: "code"),
                URLQueryItem(name: "scope", value: "openid profile email offline_access"),
                URLQueryItem(name: "state", value: state),
                URLQueryItem(name: "code_challenge", value: codeChallenge),
                URLQueryItem(name: "code_challenge_method", value: "S256"),
                URLQueryItem(name: "prompt", value: "consent")
            ]
            
            guard let authUrl = components.url else {
                Log.error("🎯 调试：构建授权URL失败")
                throw NSError(domain: "LogtoError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to build authorization URL"])
            }
            
            Log.info("🌐 启动浏览器认证流程...")
            Log.info("🔗 授权URL: \(authUrl.absoluteString)")
            Log.info("🎯 调试：步骤5 - 开始浏览器认证")
            
            // 5. 使用ASWebAuthenticationSession进行浏览器认证
            let authCode = try await performBrowserAuthentication(authUrl: authUrl, redirectUri: redirectUri)
            Log.info("🎯 调试：浏览器认证完成，获得授权码: \(String(authCode.prefix(10)))...")
            
            Log.info("🎯 调试：步骤6 - 调用后端callback")
            // 6. 直接将授权码发送给后端
            try await callBackendCallback(authCode: authCode, state: state, codeVerifier: codeVerifier)
            Log.info("🎯 调试：后端callback调用完成")
            
            Log.info("✅ 手动OIDC流程完成，用户注册/验证成功")
            
        } catch {
            Log.error("❌ 手动OIDC流程失败: \(error.localizedDescription)")
            Log.error("🎯 调试：异常详情 - \(error)")
            throw error
        }
    }
    
    /// 执行浏览器认证获取授权码
    private func performBrowserAuthentication(authUrl: URL, redirectUri: URL) async throws -> String {
        return try await withCheckedThrowingContinuation { continuation in
            let authSession = ASWebAuthenticationSession(
                url: authUrl,
                callbackURLScheme: redirectUri.scheme
            ) { callbackURL, error in
                if let error = error {
                    if let authError = error as? ASWebAuthenticationSessionError {
                        switch authError.code {
                        case .canceledLogin:
                            Log.info("ℹ️ 用户取消了登录")
                            continuation.resume(throwing: CancellationError())
                            return
                        default:
                            Log.error("❌ 认证会话错误: \(authError.localizedDescription)")
                        }
                    }
                    continuation.resume(throwing: error)
                    return
                }
                
                guard let callbackURL = callbackURL else {
                    continuation.resume(throwing: NSError(domain: "LogtoError", code: -1, userInfo: [NSLocalizedDescriptionKey: "No callback URL received"]))
                    return
                }
                
                Log.info("✅ 收到浏览器回调: \(callbackURL.absoluteString)")
                
                // 解析授权码
                guard let components = URLComponents(url: callbackURL, resolvingAgainstBaseURL: false),
                      let queryItems = components.queryItems else {
                    continuation.resume(throwing: NSError(domain: "LogtoError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to parse callback URL"]))
                    return
                }
                
                var authCode: String?
                var receivedState: String?
                
                for item in queryItems {
                    switch item.name {
                    case "code":
                        authCode = item.value
                    case "state":
                        receivedState = item.value
                    case "error":
                        let errorDescription = queryItems.first { $0.name == "error_description" }?.value ?? "Unknown error"
                        continuation.resume(throwing: NSError(domain: "LogtoError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Authorization error: \(errorDescription)"]))
                        return
                    default:
                        break
                    }
                }
                
                // 验证state参数
                guard let authCode = authCode,
                      let receivedState = receivedState,
                      receivedState == self.storage.authState else {
                    continuation.resume(throwing: NSError(domain: "LogtoError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid authorization response"]))
                    return
                }
                
                Log.info("✅ 成功获取授权码，准备发送给后端")
                continuation.resume(returning: authCode)
            }
            
            authSession.presentationContextProvider = self
            authSession.prefersEphemeralWebBrowserSession = true
            authSession.start()
        }
    }
    
    /// 将授权码发送给后端callback
    private func callBackendCallback(authCode: String, state: String, codeVerifier: String) async throws {
        Log.info("🎯 调试：callBackendCallback() 方法开始")
        let callbackUrl = "\(Configuration.API.cabyBaseUrl)/api/callback"
        Log.info("🎯 调试：准备调用 \(callbackUrl)")
        
        guard let url = URL(string: callbackUrl) else {
            Log.error("🎯 调试：无效的callback URL")
            throw NSError(domain: "LogtoError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid callback URL"])
        }
        
        var components = URLComponents(url: url, resolvingAgainstBaseURL: false)!
        components.queryItems = [
            URLQueryItem(name: "code", value: authCode),
            URLQueryItem(name: "state", value: state),
            URLQueryItem(name: "code_verifier", value: codeVerifier)
        ]
        
        guard let finalUrl = components.url else {
            Log.error("🎯 调试：构建callback URL失败")
            throw NSError(domain: "LogtoError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to build callback URL"])
        }
        
        Log.info("📡 调用后端callback: \(finalUrl.absoluteString)")
        Log.info("🎯 调试：发送授权码: \(String(authCode.prefix(10)))...")
        
        do {
            var request = URLRequest(url: finalUrl)
            request.httpMethod = "GET"
            request.setValue("application/json", forHTTPHeaderField: "Accept")
            
            Log.info("🎯 调试：发送网络请求...")
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                Log.error("🎯 调试：响应类型无效")
                throw NSError(domain: "LogtoError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response type"])
            }
            
            Log.info("📡 后端callback响应状态: \(httpResponse.statusCode)")
            Log.info("🎯 调试：响应数据长度: \(data.count) bytes")
            
            if httpResponse.statusCode == 200 {
                Log.info("🎯 调试：开始解析JSON响应...")
                // 解析后端返回的用户信息和令牌
                guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] else {
                    Log.error("🎯 调试：JSON解析失败")
                    throw NSError(domain: "LogtoError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid JSON response"])
                }
                
                Log.info("✅ 后端callback成功")
                Log.info("📋 响应数据: \(json)")
                Log.info("🎯 调试：准备保存后端认证响应...")
                
                // 保存后端返回的认证信息
                await saveBackendAuthResponse(json)
                Log.info("🎯 调试：保存后端认证响应完成")
                
            } else {
                let errorString = String(data: data, encoding: .utf8) ?? "Unknown error"
                Log.error("❌ 后端callback失败: \(errorString)")
                Log.error("🎯 调试：HTTP状态码 \(httpResponse.statusCode)，错误信息: \(errorString)")
                throw NSError(domain: "LogtoError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "Backend callback failed: \(errorString)"])
            }
            
        } catch {
            Log.error("❌ 调用后端callback失败: \(error.localizedDescription)")
            Log.error("🎯 调试：网络请求异常 - \(error)")
            throw error
        }
    }
    
    /// 保存后端认证响应
    private func saveBackendAuthResponse(_ json: [String: Any]) async {
        Log.info("💾 保存后端认证响应...")
        Log.info("🎯 调试：saveBackendAuthResponse() 方法开始")
        Log.info("🔍 完整的后端响应: \(json)")
        
        // 🎯 重要调试：详细检查每个字段
        Log.info("🎯 调试：检查响应中的各个字段...")
        for (key, value) in json {
            Log.info("🎯 调试：字段 '\(key)' = \(value) (类型: \(type(of: value)))")
        }
        
        // 检查refresh_token的状态
        if let refreshTokenField = json["refresh_token"] {
            Log.info("🎯 调试：发现 refresh_token 字段")
            if let refreshTokenString = refreshTokenField as? String {
                if refreshTokenString.isEmpty {
                    Log.error("❌ 后端返回了空的刷新令牌字符串")
                    Log.error("🎯 调试：refresh_token 字段存在但为空字符串")
                } else {
                    Log.info("✅ 后端返回了有效的刷新令牌: \(String(refreshTokenString.prefix(10)))...")
                    Log.info("🎯 调试：refresh_token 长度: \(refreshTokenString.count)")
                }
            } else {
                Log.error("❌ 后端的refresh_token字段不是字符串类型: \(type(of: refreshTokenField))")
                Log.error("🎯 调试：refresh_token 字段值: \(refreshTokenField)")
            }
        } else {
            Log.error("❌ 后端响应中完全没有refresh_token字段")
            Log.error("🎯 调试：可用字段列表: \(Array(json.keys))")
        }
        
        // 构建用户数据
        var userData: [String: Any] = [:]
        Log.info("🎯 调试：开始构建userData...")
        
        // 从后端响应中提取令牌信息
        if let accessToken = json["access_token"] as? String {
            userData["access_token"] = accessToken
            Log.info("✅ 已保存访问令牌")
            Log.info("🎯 调试：访问令牌长度: \(accessToken.count)")
        } else {
            Log.error("🎯 调试：访问令牌缺失或类型错误")
        }
        
        var refreshTokenValue: String? = nil
        if let refreshToken = json["refresh_token"] as? String {
            userData["refresh_token"] = refreshToken
            refreshTokenValue = refreshToken
            Log.info("✅ 已保存刷新令牌")
            Log.info("🎯 调试：刷新令牌已添加到userData")
        } else {
            Log.error("🎯 调试：刷新令牌缺失，不会添加到userData")
        }
        
        // 🎯 重要修复：正确计算和保存令牌过期时间
        if let expiresIn = json["expires_in"] as? TimeInterval {
            let expiresAt = Date().addingTimeInterval(expiresIn)
            // 保存过期时间戳到AuthManager
            await MainActor.run {
                AuthManager.shared.accessTokenExpiresAt = expiresAt
            }
            Log.info("🕰️ 令牌过期时间已保存: \(DateFormatter.localizedString(from: expiresAt, dateStyle: .short, timeStyle: .medium))")
            Log.info("🎯 调试：expires_in = \(expiresIn) 秒")
        } else {
            Log.warning("⚠️ 后端响应中未包含expires_in字段")
            Log.warning("🎯 调试：expires_in 字段缺失或类型错误")
        }
        
        // 从后端响应中提取用户信息
        if let userId = json["user_id"] as? String {
            userData["user_id"] = userId
            Log.info("✅ 已保存用户ID: \(userId)")
        } else {
            Log.warning("🎯 调试：用户ID缺失")
        }
        
        if let logtoId = json["logto_id"] as? String {
            userData["logto_id"] = logtoId
            Log.info("✅ 已保存Logto ID: \(logtoId)")
        } else {
            Log.warning("🎯 调试：Logto ID缺失")
        }
        
        if let email = json["email"] as? String {
            userData["email"] = email
            Log.info("🎯 调试：已保存邮箱: \(email)")
        }
        
        if let username = json["username"] as? String {
            userData["username"] = username
            Log.info("🎯 调试：已保存用户名: \(username)")
        }
        
        if let nickname = json["nickname"] as? String {
            userData["nickname"] = nickname
            Log.info("🎯 调试：已保存昵称: \(nickname)")
        }
        
        Log.info("🎯 调试：最终userData内容: \(userData)")
        Log.info("🎯 调试：准备调用 storage.saveAuthData()...")
        
        // 保存所有数据到UserDefaults
        storage.saveAuthData(tokens: userData)
        Log.info("🎯 调试：storage.saveAuthData() 调用完成")
        
        // 🎯 注意：刷新令牌已保存到UserDefaults中，AuthManager可以使用它进行令牌刷新
        if refreshTokenValue != nil {
            Log.info("🔑 刷新令牌已保存到UserDefaults，AuthManager可以使用它进行自动刷新")
            Log.info("🎯 调试：验证保存结果...")
            
            // 立即验证是否保存成功
            let savedRefreshToken = storage.refreshToken
            if let savedToken = savedRefreshToken {
                Log.info("🎯 调试：验证成功 - UserDefaults中的刷新令牌: \(String(savedToken.prefix(10)))...")
            } else {
                Log.error("🎯 调试：验证失败 - UserDefaults中没有刷新令牌!")
            }
        } else {
            Log.error("🎯 调试：没有刷新令牌可保存")
        }
        
        // 清除临时状态
        storage.authState = nil
        storage.remove(key: "code_verifier")
        Log.info("🎯 调试：已清除临时认证状态")
        
        // 清除错误信息
        await MainActor.run {
            self.errorMessage = nil
        }
        
        Log.info("✅ 用户认证信息保存完成")
        Log.info("🎯 调试：saveBackendAuthResponse() 方法完成")
    }
    
    /// 检查是否有原生SDK管理的刷新令牌
    public var hasNativeRefreshToken: Bool {
        return logtoClient?.refreshToken != nil
    }
    
    /// 处理网络相关的Logto错误
    private func handleNetworkError(_ logtoError: LogtoClientErrors.SignIn) async {
        // 检查是否为网络连接问题
        if case .unableToFetchToken = logtoError.type {
            Log.error("❌ 无法获取令牌，可能是网络连接问题")
            
            await MainActor.run {
                self.errorMessage = """
                网络连接问题
                
                无法连接到认证服务器，请检查：
                • 网络连接是否正常
                • 是否可以访问 login.caby.care
                
                请稍后重试或联系技术支持。
                """
            }
        } else if case .unknownError = logtoError.type {
            Log.error("❌ 未知登录错误")
            
            // 检查内部错误是否为网络相关
            if let innerError = logtoError.innerError as? LogtoClientErrors.SignIn,
               case .unableToFetchToken = innerError.type {
                
                await MainActor.run {
                    self.errorMessage = """
                    令牌获取失败
                    
                    认证过程中网络连接中断。
                    
                    建议操作：
                    • 检查网络连接
                    • 重新尝试登录
                    • 如问题持续，请联系技术支持
                    """
                }
            } else {
                await MainActor.run {
                    self.errorMessage = """
                    登录过程中发生错误
                    
                    请重试登录，如问题持续请联系技术支持。
                    """
                }
            }
        } else {
            // 其他Logto错误
            await MainActor.run {
                self.errorMessage = "认证服务错误，请重试"
            }
        }
    }
    
    /// 处理URL相关的系统错误
    private func handleURLError(_ error: NSError) async {
        let errorCode = error.code
        let urlString = error.userInfo[NSURLErrorFailingURLStringErrorKey] as? String ?? "未知URL"
        
        Log.error("❌ URL错误 (\(errorCode)): \(urlString)")
        
        await MainActor.run {
            switch errorCode {
            case NSURLErrorTimedOut:
                self.errorMessage = """
                连接超时
                
                网络响应缓慢，请检查网络连接后重试。
                """
                
            case NSURLErrorNetworkConnectionLost:
                self.errorMessage = """
                网络连接中断
                
                登录过程中网络连接丢失。
                
                建议操作：
                • 检查网络连接稳定性
                • 重新尝试登录
                • 尝试切换网络（如切换到Wi-Fi或移动数据）
                """
                
            case NSURLErrorNotConnectedToInternet:
                self.errorMessage = """
                无网络连接
                
                设备未连接到互联网，请检查网络设置。
                """
                
            case NSURLErrorCannotConnectToHost:
                self.errorMessage = """
                无法连接服务器
                
                无法连接到认证服务器，请稍后重试。
                """
                
            case NSURLErrorDNSLookupFailed:
                self.errorMessage = """
                DNS解析失败
                
                无法解析服务器地址，请检查网络设置。
                """
                
            default:
                self.errorMessage = """
                网络错误 (代码: \(errorCode))
                
                请检查网络连接后重试，如问题持续请联系技术支持。
                """
            }
        }
    }
    
    func signOut() async {
        guard let client = logtoClient else { return }
        
        isLoading = true
        
        // 1. 先删除客户端令牌和绑定关系
        await NotificationManager.shared.cleanupClientData()
        
        // 2. 执行Logto登出
        await client.signOut()
        
        // 3. 清除本地存储
        storage.clearAuthCredentials()
        
        // 4. 发送通知
        NotificationCenter.default.post(name: .userLoggedOut, object: nil)
        
        isLoading = false
        Log.info("🚪 用户已登出")
    }
    
    /// 获取OIDC配置
    private func fetchOIDCConfiguration() async throws -> CustomOidcConfigResponse {
        let oidcConfigUrlString = "\(Configuration.API.Logto.oidcBase)/.well-known/openid-configuration"
        
        guard let oidcConfigUrl = URL(string: oidcConfigUrlString) else {
            throw NSError(domain: "LogtoError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid OIDC configuration URL"])
        }
        
        Log.info("🔍 获取OIDC配置: \(oidcConfigUrlString)")
        
        let (data, response) = try await URLSession.shared.data(from: oidcConfigUrl)
        
        guard let httpResponse = response as? HTTPURLResponse, 
              httpResponse.statusCode == 200 else {
            throw NSError(domain: "LogtoError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to fetch OIDC configuration"])
        }
        
        let oidcConfig = try JSONDecoder().decode(CustomOidcConfigResponse.self, from: data)
        Log.info("✅ OIDC配置获取成功")
        
        return oidcConfig
    }
    
    // MARK: - 测试和调试方法
    
    /// 测试OIDC配置获取
    func testOIDCConfiguration() async {
        Log.info("🧪 开始测试OIDC配置获取...")
        
        let oidcConfigUrlString = "\(Configuration.API.Logto.oidcBase)/.well-known/openid-configuration"
        Log.info("🔍 测试URL: \(oidcConfigUrlString)")
        
        guard let oidcConfigUrl = URL(string: oidcConfigUrlString) else {
            Log.error("❌ 无效的OIDC配置URL")
            return
        }
        
        do {
            let (configData, configResponse) = try await URLSession.shared.data(from: oidcConfigUrl)
            
            if let httpResponse = configResponse as? HTTPURLResponse {
                Log.info("📡 响应状态: \(httpResponse.statusCode)")
            }
            
            if let configString = String(data: configData, encoding: .utf8) {
                Log.info("📋 配置内容: \(configString)")
            }
            
            let oidcConfig = try JSONDecoder().decode(CustomOidcConfigResponse.self, from: configData)
            Log.info("✅ OIDC配置解析成功")
            Log.info("🔗 授权端点: \(oidcConfig.authorizationEndpoint)")
            Log.info("🔗 令牌端点: \(oidcConfig.tokenEndpoint)")
            Log.info("🔗 用户信息端点: \(oidcConfig.userinfoEndpoint)")
            
        } catch {
            Log.error("❌ OIDC配置测试失败: \(error)")
        }
    }
    
    /// 测试用户信息获取功能
    func testUserInfoRetrieval() async {
        guard let client = logtoClient else {
            Log.error("❌ Logto客户端未初始化")
            return
        }
        
        guard client.isAuthenticated else {
            Log.warning("⚠️ 用户未登录，无法测试用户信息获取")
                return
        }
        
        Log.info("🧪 开始测试用户信息获取...")
        // 测试方法已简化，不再调用已删除的方法
        Log.info("🧪 用户信息获取测试完成")
    }
}

// MARK: - ASWebAuthenticationPresentationContextProviding
extension NativeLogtoManager: ASWebAuthenticationPresentationContextProviding {
    func presentationAnchor(for session: ASWebAuthenticationSession) -> ASPresentationAnchor {
        return ASPresentationAnchor()
    }
}

// MARK: - 错误定义
enum LogtoError: LocalizedError {
    case clientNotInitialized
    
    var errorDescription: String? {
        switch self {
        case .clientNotInitialized:
            return "Logto客户端未初始化"
        }
    }
} 
