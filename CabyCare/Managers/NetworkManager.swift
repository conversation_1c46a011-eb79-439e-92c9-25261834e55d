import Foundation
import UIKit
import Network
import os  // 添加这个导入来支持 OSAllocatedUnfairLock

enum NetworkError: Error, Equatable, LocalizedError {
    case invalidURL
    case invalidResponse
    case serverError(Int)
    case decodingError(Error)
    case noData
    case connectivityError
    case timeoutError
    case authError
    case unauthorized
    case tokenRefreshFailed(Error)  // 添加刷新令牌失败错误
    case cloudflareTimeout
    case requestCancelled

    // LocalizedError协议实现
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的URL地址"
        case .invalidResponse:
            return "服务器响应格式无效"
        case .serverError(let code):
            return "服务器错误 (代码: \(code))"
        case .decodingError:
            return "数据解析失败"
        case .noData:
            return "服务器未返回数据"
        case .connectivityError:
            return "网络连接不可用"
        case .timeoutError:
            return "请求超时"
        case .authError:
            return "认证失败"
        case .unauthorized:
            return "未授权访问"
        case .tokenRefreshFailed:
            return "令牌刷新失败"
        case .cloudflareTimeout:
            return "CDN连接超时，请稍后重试"
        case .requestCancelled:
            return "网络请求被取消"
        }
    }
    
    var failureReason: String? {
        switch self {
        case .cloudflareTimeout:
            return "Cloudflare CDN连接超时，这可能是由于网络延迟或服务器负载过高导致的。"
        case .connectivityError:
            return "设备无法连接到互联网。"
        case .serverError(let code):
            return "服务器返回了错误状态码 \(code)。"
        default:
            return nil
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .cloudflareTimeout:
            return "请检查网络连接并稍后重试。如果问题持续，请联系技术支持。"
        case .connectivityError:
            return "请检查网络设置，确保设备已连接到有效的网络。"
        case .unauthorized:
            return "请重新登录以获取新的访问权限。"
        default:
            return "请稍后重试，如果问题持续请联系技术支持。"
        }
    }

    // 实现 Equatable 协议
    static func == (lhs: NetworkError, rhs: NetworkError) -> Bool {
        switch (lhs, rhs) {
        case (.invalidURL, .invalidURL),
             (.invalidResponse, .invalidResponse),
             (.noData, .noData),
             (.connectivityError, .connectivityError),
             (.timeoutError, .timeoutError),
             (.authError, .authError),
             (.unauthorized, .unauthorized):
            return true
        case (.serverError(let lCode), .serverError(let rCode)):
            return lCode == rCode
        case (.decodingError(let lError), .decodingError(let rError)):
            return lError.localizedDescription == rError.localizedDescription
        case (.tokenRefreshFailed(let lError), .tokenRefreshFailed(let rError)):
            return lError.localizedDescription == rError.localizedDescription
        case (.cloudflareTimeout, .cloudflareTimeout):
            return true
        case (.requestCancelled, .requestCancelled):
            return true
        default:
            return false
        }
    }
}

// 添加 HTTPMethod 枚举
enum HTTPMethod: String {
    case get = "GET"
    case post = "POST"
    case put = "PUT"
    case delete = "DELETE"
    case patch = "PATCH"
}

// 设备信息提供者 - 线程安全的设备信息获取
@MainActor
final class DeviceInfoProvider {
    static let shared = DeviceInfoProvider()
    
    private init() {}
    
    var userAgent: String {
        "CabyCare/1.0 iOS/\(UIDevice.current.systemVersion)"
    }
    
    var deviceModel: String {
        UIDevice.current.modelName
    }
    
    var appVersion: String {
        Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "unknown"
    }
    
    var preferredLanguage: String? {
        Locale.preferredLanguages.first
    }
}

final class NetworkManager: Sendable {
    static let shared = NetworkManager()
    private let session: URLSession
    private let networkMonitor = NWPathMonitor()
    private let isConnected = OSAllocatedUnfairLock(initialState: true)
    private let isExpensiveConnection = OSAllocatedUnfairLock(initialState: false)
    private let isLowDataMode = OSAllocatedUnfairLock(initialState: false)

    // Performance tracking
    private let timingsQueue = DispatchQueue(label: "com.aby.networktimings")
    private let requestTimings = OSAllocatedUnfairLock(initialState: [URL: Date]())

    private func setRequestTiming(for url: URL, date: Date) {
        requestTimings.withLock { timings in
            timings[url] = date
        }
    }

    private func removeRequestTiming(for url: URL) {
        requestTimings.withLock { timings in
            _ = timings.removeValue(forKey: url)
        }
    }

    private init() {
        let config = URLSessionConfiguration.default

        // Base configuration
        config.timeoutIntervalForRequest = 30
        config.timeoutIntervalForResource = 60
        config.httpShouldUsePipelining = true
        config.requestCachePolicy = .reloadIgnoringLocalCacheData

        // Optimize connection pooling (these properties improve HTTP/2 connection management)
        config.httpMaximumConnectionsPerHost = 8
        config.multipathServiceType = .handover // For seamless WiFi to cellular transitions
        config.waitsForConnectivity = true

        // Set priority for streaming content
        config.networkServiceType = .video

        // Create custom session with redirect handling
        let delegate = RedirectHandlingDelegate()
        session = URLSession(configuration: config, delegate: delegate, delegateQueue: nil)

        // Setup network monitoring
        setupNetworkMonitoring()
    }

    private func setupNetworkMonitoring() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            guard let self = self else { return }
            self.isConnected.withLock { $0 = path.status == .satisfied }
            self.isExpensiveConnection.withLock { $0 = path.isExpensive }

            // Adjust session behavior based on connection quality
            if path.isConstrained {
                self.isLowDataMode.withLock { $0 = true }
                // Lower quality for constrained connections can be set here
            } else {
                self.isLowDataMode.withLock { $0 = false }
            }

            // Log connection type for debugging
            let connectionTypes: [String] = path.availableInterfaces.map { interface -> String in
                switch interface.type {
                case .wifi: return "WiFi"
                case .cellular: return "Cellular"
                case .wiredEthernet: return "Wired Ethernet"
                case .loopback: return "Loopback"
                default: return "Other"
                }
            }
            print("Network connection established: \(connectionTypes.joined(separator: ", "))")
        }

        // Start monitoring on background queue
        networkMonitor.start(queue: DispatchQueue.global(qos: .background))
    }

    func fetch<T: Decodable>(_ url: URL, contentType: ContentType = .json) async throws -> T {
        guard isConnected.withLock({ $0 }) else {
            throw NetworkError.connectivityError
        }

        var request = URLRequest(url: url)
        await configureRequestHeadersAsync(&request, contentType: contentType)

        // Track request start time for performance monitoring
        setRequestTiming(for: url, date: Date())

        // Adaptive timeout based on connection quality
        if isExpensiveConnection.withLock({ $0 }) || isLowDataMode.withLock({ $0 }) {
            request.timeoutInterval = 45  // Extended timeout for poor connections
        }

        do {
            let result: T = try await performRequest(request)

            // Log performance metrics
            // if let startTime = requestTimings.withLock({ $0[url] }) {
            //     let elapsed = Date().timeIntervalSince(startTime)
            //     print("Request to \(url.host ?? "unknown") completed in \(String(format: "%.3f", elapsed)) seconds")
            //     requestTimings.withLock({ $0.removeValue(forKey: url) })
            // }

            return result
        } catch {
            removeRequestTiming(for: url)
            throw error
        }
    }

    // Helper method to log detailed decoding errors
    private func logDecodingError<T>(_ error: DecodingError, data: Data, for type: T.Type) {
        print("⚠️ JSON Decoding Error for type \(String(describing: type)):")

        switch error {
        case .keyNotFound(let key, let context):
            print("  - Missing key: \(key) at path: \(context.codingPath)")
            // Try to extract the actual field that's present instead
            if let jsonObject = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                print("  - Available keys in JSON: \(Array(jsonObject.keys))")
            } else if let jsonArray = try? JSONSerialization.jsonObject(with: data) as? [[String: Any]],
                      !jsonArray.isEmpty {
                print("  - Available keys in first array item: \(Array(jsonArray[0].keys))")
            }

        case .typeMismatch(let type, let context):
            print("  - Type mismatch: Expected \(type) at path: \(context.codingPath)")

        case .valueNotFound(let type, let context):
            print("  - Value not found: Expected \(type) at path: \(context.codingPath)")

        case .dataCorrupted(let context):
            print("  - Data corrupted at path: \(context.codingPath), \(context.debugDescription)")

        @unknown default:
            print("  - Unknown error: \(error)")
        }
    }

    enum ContentType {
        case json
        case video
        case audio
        case image
        case both  // JSON + video

        var acceptHeader: String {
            switch self {
            case .json:
                return "application/json"
            case .video:
                return "video/MP2T, video/mp4, application/x-mpegURL"
            case .audio:
                return "audio/mpeg, audio/aac"
            case .image:
                return "image/jpeg, image/png, image/webp"
            case .both:
                return "application/json, video/MP2T, video/mp4, application/x-mpegURL"
            }
        }
    }

    // 同步配置请求头 - 用于外部调用（不包含设备信息）
    public func configureRequestHeaders(_ request: inout URLRequest, contentType: ContentType = .json, authToken: String? = nil) {
        // 基础请求头
        var headers = [
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "X-Client-Platform": "iOS"
        ]
        
        // 设置 Accept 头
        headers["Accept"] = contentType.acceptHeader

        // 添加低数据模式标志
        if isLowDataMode.withLock({ $0 }) {
            headers["X-Low-Data-Mode"] = "true"
        }

        // 应用所有请求头
        headers.forEach { request.setValue($0.value, forHTTPHeaderField: $0.key) }

        // 使用系统语言设置
        if let languages = Locale.preferredLanguages.first {
            request.setValue(languages, forHTTPHeaderField: "Accept-Language")
        }

        // 添加授权头，除非是认证请求
        if let url = request.url, !isAuthRequest(url) {
            if let token = authToken {
                request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            } else {
                Log.warning("⚠️ 请求 \(url.absoluteString) 不包含授权头，可能导致API访问受限")
            }
        }
    }

    // 异步配置请求头 - 线程安全的方式（包含设备信息）
    private func configureRequestHeadersAsync(_ request: inout URLRequest, contentType: ContentType = .json) async {
        // 基础请求头
        var headers = [
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "X-Client-Platform": "iOS"
        ]
        
        // 异步获取设备信息
        let deviceInfo = await DeviceInfoProvider.shared.userAgent
        let deviceModel = await DeviceInfoProvider.shared.deviceModel
        let appVersion = await DeviceInfoProvider.shared.appVersion
        let preferredLanguage = await DeviceInfoProvider.shared.preferredLanguage
        
        headers["User-Agent"] = deviceInfo
        headers["X-Device-Model"] = deviceModel
        headers["X-Client-Version"] = appVersion
        
        // 设置 Accept 头
        headers["Accept"] = contentType.acceptHeader

        // 添加低数据模式标志
        if isLowDataMode.withLock({ $0 }) {
            headers["X-Low-Data-Mode"] = "true"
        }

        // 应用所有请求头
        headers.forEach { request.setValue($0.value, forHTTPHeaderField: $0.key) }

        // 使用系统语言设置
        if let languages = preferredLanguage {
            request.setValue(languages, forHTTPHeaderField: "Accept-Language")
        }

        // 添加授权头，除非是认证请求
        if let url = request.url, !isAuthRequest(url) {
            if let authHeader = await getAuthorizationHeader() {
                request.setValue(authHeader, forHTTPHeaderField: "Authorization")
            } else {
                Log.warning("⚠️ 请求 \(url.absoluteString) 不包含授权头，可能导致API访问受限")
            }
        }
    }

    // 添加一个函数来检查是否是认证相关的请求
    private func isAuthRequest(_ url: URL) -> Bool {
        // 检查URL是否包含认证相关的路径（这些请求不需要Authorization header）
        let urlString = url.absoluteString.lowercased()
        return urlString.contains("/api/callback") ||
               urlString.contains("/auth") ||
               urlString.contains("/api/refresh") ||
               urlString.contains("/login") ||
               urlString.contains("/oidc") ||
               urlString.contains("/oauth")
        // 注意：/api/clients/register 需要认证，所以不包含在这里
    }

    // 异步获取授权头的方法
    private func getAuthorizationHeader() async -> String? {
        let accessToken = await AuthManager.shared.accessToken
        
        if let token = accessToken {
            return "Bearer \(token)"
        } else {
            Log.error("❌ 尝试访问受保护的API，但未找到访问令牌")
            return nil
        }
    }

    // 通用请求函数
    @discardableResult
    public func fetch<T: Decodable>(_ url: URL) async throws -> T {
        var request = URLRequest(url: url)
        await configureRequestHeadersAsync(&request)

        return try await performRequest(request)
    }

    // 另一个带有contentType参数的fetch方法
    func fetch<T: Decodable>(url: URL, contentType: ContentType = .json) async throws -> T {
        var request = URLRequest(url: url)
        await configureRequestHeadersAsync(&request, contentType: contentType)

        return try await performRequest(request)
    }

    // 发送数据的方法
    @discardableResult
    public func send<T: Decodable, U: Encodable>(_ url: URL, method: String = "POST", body: U) async throws -> T {
        var request = URLRequest(url: url)
        request.httpMethod = method
        await configureRequestHeadersAsync(&request)

        // 编码请求体
        request.httpBody = try? JSONEncoder().encode(body)

        return try await performRequest(request)
    }

    // 带重试机制的请求处理方法
    private func performRequest<T: Decodable>(_ request: URLRequest, retryCount: Int = 0) async throws -> T {
        guard let url = request.url else {
            throw NetworkError.invalidURL
        }

        // 记录请求开始时间
        setRequestTiming(for: url, date: Date())

        let session = URLSession.shared

        // 为耗时的请求设置更长的超时
        var request = request
        if url.absoluteString.contains("/list") || url.absoluteString.contains("/get") {
            request.timeoutInterval = 45  // Extended timeout for poor connections
        }

        do {
            let (data, response) = try await session.data(for: request)

            // 检查HTTP响应
            guard let httpResponse = response as? HTTPURLResponse else {
                throw NetworkError.invalidResponse
            }

            // 检查Cloudflare 522错误并进行重试
            if httpResponse.statusCode == 522 {
                Log.warning("⚠️ Cloudflare 522错误 (连接超时)，尝试重试...")
                
                if retryCount < 3 {
                    // 指数退避策略：1秒、2秒、4秒
                    let delay = pow(2.0, Double(retryCount))
                    Log.info("🔄 \(delay)秒后重试第\(retryCount + 1)次...")
                    
                    try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                    return try await performRequest(request, retryCount: retryCount + 1)
                } else {
                    Log.error("❌ 重试3次后仍然失败，Cloudflare连接超时")
                    throw NetworkError.cloudflareTimeout
                }
            }

            // 检查授权错误
            if httpResponse.statusCode == 401 {
                Log.error("🔒 授权失败详细信息:")
                Log.error("   - 请求URL: \(request.url?.absoluteString ?? "未知")")
                Log.error("   - 请求方法: \(request.httpMethod ?? "未知")")
                Log.error("   - 状态码: \(httpResponse.statusCode)")
                
                // 打印Authorization header（遮盖敏感信息）
                if let authHeader = request.allHTTPHeaderFields?["Authorization"] {
                    let maskedHeader = String(authHeader.prefix(20)) + "..."
                    Log.error("   - Authorization Header: \(maskedHeader)")
                } else {
                    Log.error("   - Authorization Header: 缺失")
                }
                
                // 打印响应体（如果有错误信息）
                if !data.isEmpty {
                    if let responseString = String(data: data, encoding: .utf8) {
                        Log.error("   - 后端错误响应: \(responseString)")
                    } else {
                        Log.error("   - 响应体: 无法解码为字符串（\(data.count) bytes）")
                    }
                } else {
                    Log.error("   - 响应体: 空")
                }
                
                // 打印关键响应头
                if let contentType = httpResponse.allHeaderFields["Content-Type"] as? String {
                    Log.error("   - Content-Type: \(contentType)")
                }
                if let serverHeader = httpResponse.allHeaderFields["Server"] as? String {
                    Log.error("   - Server: \(serverHeader)")
                }
                
                Log.error("🔒 授权失败: 请求返回401未授权状态码")
                throw NetworkError.unauthorized
            }

            // 检查成功状态码 - 添加对206（Partial Content）的支持
            guard (200...299).contains(httpResponse.statusCode) else {
                Log.error("❌ 服务器错误: 状态码 \(httpResponse.statusCode)")
                if let errorJson = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let errorMessage = errorJson["message"] as? String {
                    Log.error("服务器错误信息: \(errorMessage)")
                }
                throw NetworkError.serverError(httpResponse.statusCode)
            }

            // 处理空响应 - 改进检测逻辑
            if data.isEmpty {
                Log.warning("⚠️ 服务器返回空响应体")
                throw NetworkError.noData
            }
            
            // 检查是否为null响应
            if let jsonString = String(data: data, encoding: .utf8) {
                let trimmedJson = jsonString.trimmingCharacters(in: .whitespacesAndNewlines)
                
                // 添加调试日志来查看实际响应内容
                // Log.debug("📥 收到的响应数据 (\(data.count) bytes): \(trimmedJson)")
                
                // 检查各种空响应格式
                if trimmedJson == "null" || 
                   trimmedJson == "[]" || 
                   trimmedJson == "{}" ||
                   trimmedJson.isEmpty {
                    Log.info("ℹ️ 服务器返回空数据响应: \(trimmedJson)")
                    Log.info("ℹ️ 这通常表示请求的日期范围内没有数据，这是正常情况")
                    throw NetworkError.noData
                }
            }

            do {
                let decoder = JSONDecoder()

                // 配置解码器
                if url.absoluteString.contains("api.caby.care") {
                    decoder.keyDecodingStrategy = .useDefaultKeys
                } else {
                    decoder.keyDecodingStrategy = .useDefaultKeys
                }

                // 移除自定义日期解码策略，使用默认的 ISO8601 解码器
                let formatter = ISO8601DateFormatter()
                formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
                decoder.dateDecodingStrategy = .iso8601

                // 在解码之前打印原始数据以进行调试
                // if let jsonString = String(data: data, encoding: .utf8) {
                //     Log.debug("📥 收到的 JSON 数据: \(jsonString)")
                // }

                return try decoder.decode(T.self, from: data)
            } catch {
                Log.error("❌ 解码错误: \(error.localizedDescription)")

                // 增强错误日志
                if let decodingError = error as? DecodingError {
                    logDecodingError(decodingError, data: data, for: T.self)
                }

                throw NetworkError.decodingError(error)
            }
        } catch let urlError as URLError {
            if urlError.code == .timedOut {
                Log.error("⏱️ 请求超时: \(url.absoluteString)")
                throw NetworkError.timeoutError
            } else if urlError.code == .cancelled {
                Log.warning("⚠️ 网络请求被取消: \(url.absoluteString)")
                Log.warning("⚠️ 这通常由重复刷新或应用状态变化引起")
                // 对于取消的请求，我们抛出一个特殊的错误，让调用者决定如何处理
                throw NetworkError.requestCancelled
            }
            Log.error("🌐 网络错误: \(urlError.localizedDescription)")
            throw urlError
        }
    }

    // 在 NetworkManager 类中添加令牌刷新和重试机制
    private func fetchWithTokenRefresh<T: Decodable>(
        _ url: URL,
        method: HTTPMethod = .get,
        body: Data? = nil,
        contentType: ContentType = .json,
        retryCount: Int = 3  // 改为默认重试3次
    ) async throws -> T {
        Log.debug("📡 开始网络请求: \(method.rawValue) \(url.absoluteString)")
        
        do {
            // 使用现有的 fetch 方法
            var request = URLRequest(url: url)
            await configureRequestHeadersAsync(&request, contentType: contentType)

            if method != .get {
                request.httpMethod = method.rawValue
                request.httpBody = body
            }

            let result: T = try await performRequest(request)
            Log.debug("✅ 网络请求成功: \(url.absoluteString)")
            return result
        } catch let error as NetworkError {
            Log.debug("🔍 网络请求遇到错误: \(error.localizedDescription)")
            
            // 对于 requestCancelled 错误，进行特殊处理
            if error == .requestCancelled {
                Log.warning("⚠️ 网络请求被取消，可能由于重复刷新或应用状态变化")
                // 对于取消的请求，直接抛出，不进行令牌刷新重试
                throw error
            }
            
            // 对于noData错误，直接抛出，不进行令牌刷新
            if error == .noData {
                Log.debug("ℹ️ 服务器返回空数据，这是正常情况（该时间段无视频数据）")
                throw error
            }
            
            // 🎯 使用新的hasRefreshToken方法检查刷新令牌可用性
            let hasRefreshToken = await MainActor.run {
                return AuthManager.shared.hasRefreshToken
            }

            if error == .unauthorized {
                Log.warning("🔒 检测到授权失败 (401)，开始静默令牌刷新流程...")
                Log.debug("🔍 刷新令牌可用性: \(hasRefreshToken ? "有" : "无")")
                Log.debug("🔍 剩余重试次数: \(retryCount)")
                
                if hasRefreshToken && retryCount > 0 {
                    // 🔄 开始静默令牌刷新重试逻辑
                    return try await performTokenRefreshWithRetry(
                        url: url,
                        method: method,
                        body: body,
                        contentType: contentType,
                        retryCount: retryCount
                    )
                } else {
                    // 🚫 没有刷新令牌或重试次数用完，但不直接发送通知
                    // 让TokenRefreshManager来决定如何处理这种情况
                    let reason = !hasRefreshToken ? "无可用的刷新令牌" : "静默刷新令牌3次失败"
                    Log.warning("⚠️ 授权失败且\(reason)，但不直接提示用户")
                    Log.warning("🔄 将错误抛出给TokenRefreshManager处理")
                    
                    // 🔄 不发送通知，让上层TokenRefreshManager根据错误类型智能处理
                    throw NetworkError.unauthorized
                }
            }
            throw error
        }
    }
    
    // 🔄 新增：静默令牌刷新重试逻辑
    private func performTokenRefreshWithRetry<T: Decodable>(
        url: URL,
        method: HTTPMethod,
        body: Data?,
        contentType: ContentType,
        retryCount: Int
    ) async throws -> T {
        Log.info("🔄 开始静默令牌刷新重试 (剩余尝试次数: \(retryCount))")
        
        do {
            // 🔄 尝试刷新令牌
            Log.debug("🔄 调用 AuthManager.refreshToken()...")
            let newToken = try await AuthManager.shared.refreshToken()
            Log.info("✅ 令牌刷新成功，新令牌前缀: \(String(newToken.prefix(10)))...")
            
            // 等待一小段时间确保令牌更新完成
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
            
            Log.info("🔄 使用新令牌重新发送原始请求...")

            // 🔄 直接重新发送原始请求，避免双重重试机制
            var request = URLRequest(url: url)
            await configureRequestHeadersAsync(&request, contentType: contentType)
            
            if method != .get {
                request.httpMethod = method.rawValue
                request.httpBody = body
            }
            
            let result: T = try await performRequest(request)
            Log.debug("✅ 使用新令牌的网络请求成功: \(url.absoluteString)")
            return result
            
        } catch let refreshError {
            Log.error("❌ 令牌刷新失败 (尝试 \(4 - retryCount)/3): \(refreshError.localizedDescription)")
            
            // 🔍 检查是否还有重试机会
            if retryCount > 1 {
                Log.info("🔄 令牌刷新失败，进行下一次静默重试...")
                
                // 🕐 实现指数退避延迟策略
                let delay = pow(2.0, Double(3 - retryCount)) // 2秒、4秒
                Log.debug("⏱️ 等待 \(delay) 秒后进行下一次重试...")
                
                try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                
                // 🔄 递归重试
                return try await performTokenRefreshWithRetry(
                    url: url,
                    method: method,
                    body: body,
                    contentType: contentType,
                    retryCount: retryCount - 1
                )
            } else {
                // 🚫 所有重试都失败了
                Log.error("❌ 静默令牌刷新3次全部失败，触发重新登录")
                Log.error("❌ 最终失败原因: \(refreshError.localizedDescription)")
                
                // 🔍 检查刷新失败的原因
                if let networkError = refreshError as? NetworkError,
                   case .serverError(let code) = networkError,
                   [400, 401].contains(code) {
                    Log.warning("⚠️ 刷新令牌被服务器拒绝 (HTTP \(code))，触发重新登录")
                    await sendAuthenticationExpiredNotification(reason: "刷新令牌被服务器拒绝，3次尝试后失败")
                    throw NetworkError.unauthorized
                } else {
                    Log.warning("⚠️ 令牌刷新失败（其他错误），3次尝试后失败")
                    await sendAuthenticationExpiredNotification(reason: "令牌刷新失败，3次尝试后失败")
                    throw NetworkError.tokenRefreshFailed(refreshError)
                }
            }
        }
    }
    
    /// 安全地发送认证过期通知，包含原因信息
    private func sendAuthenticationExpiredNotification(reason: String) async {
        Log.warning("📢 发送认证过期通知，原因: \(reason)")
        
        // 📢 静默重试3次失败后，发送认证过期通知
        Log.warning("📢 静默令牌刷新3次失败，发送认证过期通知")
        
        // 🔍 提供详细的错误原因给用户
        let userFriendlyReason: String
        switch reason {
        case let r where r.contains("刷新令牌被服务器拒绝"):
            userFriendlyReason = "您的会话已过期，请重新登录"
        case let r where r.contains("无可用的刷新令牌"):
            userFriendlyReason = "登录状态异常，请重新登录"
        case let r where r.contains("静默刷新令牌3次失败"):
            userFriendlyReason = "网络连接或服务器异常，请重新登录"
        default:
            userFriendlyReason = "会话已过期，请重新登录"
        }
        
        Task { @MainActor in
            NotificationCenter.default.post(
                name: .authenticationExpired, 
                object: nil, 
                userInfo: [
                    "reason": reason,
                    "userFriendlyReason": userFriendlyReason,
                    "retryCount": 3, // 告诉UI已经重试了3次
                    "silentRetryCompleted": true // 标记静默重试已完成
                ]
            )
        }
    }

    // MARK: - 专门的令牌刷新方法（避免循环依赖）
    
    /// 专门用于令牌刷新的网络请求方法，支持静默重试3次，但不会触发令牌刷新
    func refreshTokenRequest(
        _ url: URL,
        body: Data,
        retryCount: Int = 3
    ) async throws -> (Data, HTTPURLResponse) {
        Log.debug("🔄 开始令牌刷新网络请求（支持静默重试\(retryCount)次）: \(url.absoluteString)")
        
        do {
            // 构建请求
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            request.setValue("application/json", forHTTPHeaderField: "Accept")
            request.httpBody = body
            
            // 添加基础请求头，但不添加Authorization（因为这是刷新请求）
            request.setValue("gzip, deflate, br", forHTTPHeaderField: "Accept-Encoding")
            request.setValue("keep-alive", forHTTPHeaderField: "Connection")
            request.setValue("iOS", forHTTPHeaderField: "X-Client-Platform")
            
            // 异步获取设备信息
            let deviceInfo = await DeviceInfoProvider.shared.userAgent
            let deviceModel = await DeviceInfoProvider.shared.deviceModel
            let appVersion = await DeviceInfoProvider.shared.appVersion
            let preferredLanguage = await DeviceInfoProvider.shared.preferredLanguage
            
            request.setValue(deviceInfo, forHTTPHeaderField: "User-Agent")
            request.setValue(deviceModel, forHTTPHeaderField: "X-Device-Model")
            request.setValue(appVersion, forHTTPHeaderField: "X-Client-Version")
            
            if let language = preferredLanguage {
                request.setValue(language, forHTTPHeaderField: "Accept-Language")
            }
            
            // 发送请求
            let session = URLSession.shared
            let (data, response) = try await session.data(for: request)
            
            // 检查HTTP响应
            guard let httpResponse = response as? HTTPURLResponse else {
                throw NetworkError.invalidResponse
            }
            
            Log.debug("📡 令牌刷新响应状态码: \(httpResponse.statusCode)")
            
            // 检查成功状态码
            guard 200...299 ~= httpResponse.statusCode else {
                Log.error("❌ 令牌刷新请求失败: HTTP \(httpResponse.statusCode)")
                throw NetworkError.serverError(httpResponse.statusCode)
            }
            
            Log.debug("✅ 令牌刷新网络请求成功")
            return (data, httpResponse)
            
        } catch let error as NetworkError {
            // 🔄 对于特定错误进行静默重试
            if (error == .serverError(401) || error == .serverError(400) || error == .serverError(500) || error == .timeoutError || error == .connectivityError) && retryCount > 0 {
                Log.info("🔄 令牌刷新请求失败，开始静默重试 (剩余尝试次数: \(retryCount))")
                
                // 🕐 实现指数退避延迟策略
                let delay = pow(2.0, Double(3 - retryCount)) // 2秒、4秒
                Log.debug("⏱️ 等待 \(delay) 秒后进行下一次重试...")
                
                try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                
                // 🔄 递归重试
                return try await refreshTokenRequest(url, body: body, retryCount: retryCount - 1)
            } else {
                // 🚫 所有重试都失败了或者是不应该重试的错误
                Log.error("❌ 令牌刷新请求最终失败: \(error.localizedDescription)")
                throw error
            }
        } catch let urlError as URLError {
            // 🔄 对于网络错误进行静默重试
            if (urlError.code == .timedOut || urlError.code == .networkConnectionLost || urlError.code == .notConnectedToInternet) && retryCount > 0 {
                Log.info("🔄 令牌刷新网络错误，开始静默重试 (剩余尝试次数: \(retryCount))")
                
                // 🕐 实现指数退避延迟策略
                let delay = pow(2.0, Double(3 - retryCount)) // 2秒、4秒
                Log.debug("⏱️ 等待 \(delay) 秒后进行下一次重试...")
                
                try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                
                // 🔄 递归重试
                return try await refreshTokenRequest(url, body: body, retryCount: retryCount - 1)
            } else {
                Log.error("❌ 令牌刷新网络错误: \(urlError.localizedDescription)")
                
                if urlError.code == .timedOut {
                    throw NetworkError.timeoutError
                } else {
                    throw NetworkError.connectivityError
                }
            }
        }
    }

    // MARK: - 主要的公共API方法（所有方法都支持令牌刷新）
    
    /// 通用的HTTP请求方法，支持令牌刷新和重试
    func request<T: Decodable>(
        _ url: URL,
        method: HTTPMethod = .get,
        body: Data? = nil,
        contentType: ContentType = .json
    ) async throws -> T {
        return try await fetchWithTokenRefresh(url, method: method, body: body, contentType: contentType)
    }
    
    /// GET请求的便捷方法
    func get<T: Decodable>(_ url: URL, type: T.Type = T.self, contentType: ContentType = .json) async throws -> T {
        return try await fetchWithTokenRefresh(url, method: .get, contentType: contentType)
    }
    
    /// POST请求的便捷方法
    func post<T: Decodable>(_ url: URL, body: Data? = nil, type: T.Type = T.self, contentType: ContentType = .json) async throws -> T {
        return try await fetchWithTokenRefresh(url, method: .post, body: body, contentType: contentType)
    }
    
    /// PUT请求的便捷方法
    func put<T: Decodable>(_ url: URL, body: Data? = nil, type: T.Type = T.self, contentType: ContentType = .json) async throws -> T {
        return try await fetchWithTokenRefresh(url, method: .put, body: body, contentType: contentType)
    }
    
    /// DELETE请求的便捷方法
    func delete<T: Decodable>(_ url: URL, type: T.Type = T.self, contentType: ContentType = .json) async throws -> T {
        return try await fetchWithTokenRefresh(url, method: .delete, contentType: contentType)
    }
    
    /// PATCH请求的便捷方法
    func patch<T: Decodable>(_ url: URL, body: Data? = nil, type: T.Type = T.self, contentType: ContentType = .json) async throws -> T {
        return try await fetchWithTokenRefresh(url, method: .patch, body: body, contentType: contentType)
    }
    
    // MARK: - 原始数据请求方法
    
    /// 获取原始Data的方法，支持认证和令牌刷新
    func fetchData(
        _ url: URL,
        method: HTTPMethod = .get,
        body: Data? = nil,
        contentType: ContentType = .image,
        retryCount: Int = 3  // 改为默认重试3次
    ) async throws -> Data {
        Log.debug("📡 开始原始数据请求: \(url.absoluteString)")
        
        do {
            var request = URLRequest(url: url)
            await configureRequestHeadersAsync(&request, contentType: contentType)
            
            if method != .get {
                request.httpMethod = method.rawValue
                request.httpBody = body
            }
            
            let session = URLSession.shared
            let (data, response) = try await session.data(for: request)
            
            // 检查HTTP响应
            guard let httpResponse = response as? HTTPURLResponse else {
                throw NetworkError.invalidResponse
            }
            
            // 检查授权错误
            if httpResponse.statusCode == 401 {
                Log.error("🔒 原始数据请求授权失败: 请求返回401未授权状态码")
                throw NetworkError.unauthorized
            }
            
            // 检查成功状态码
            guard 200...299 ~= httpResponse.statusCode else {
                Log.error("❌ 原始数据请求失败: HTTP \(httpResponse.statusCode)")
                throw NetworkError.serverError(httpResponse.statusCode)
            }
            
            Log.debug("✅ 原始数据请求成功: \(data.count) bytes")
            return data
            
        } catch let error as NetworkError {
            // 🎯 使用新的hasRefreshToken方法检查刷新令牌可用性
            let hasRefreshToken = await MainActor.run {
                return AuthManager.shared.hasRefreshToken
            }

            if error == .unauthorized {
                Log.warning("🔒 原始数据请求检测到授权失败 (401)，开始静默令牌刷新流程...")
                
                if hasRefreshToken && retryCount > 0 {
                    // 🔄 开始静默令牌刷新重试逻辑
                    return try await performDataRefreshWithRetry(
                        url: url,
                        method: method,
                        body: body,
                        contentType: contentType,
                        retryCount: retryCount
                    )
                } else {
                    // 🚫 没有刷新令牌或重试次数用完，但不直接发送通知
                    // 让TokenRefreshManager来决定如何处理这种情况
                    let reason = !hasRefreshToken ? "无可用的刷新令牌" : "静默刷新令牌3次失败"
                    Log.warning("⚠️ 原始数据请求授权失败且\(reason)，但不直接提示用户")
                    Log.warning("🔄 将错误抛出给TokenRefreshManager处理")
                    
                    // 🔄 不发送通知，让上层TokenRefreshManager根据错误类型智能处理
                    throw NetworkError.unauthorized
                }
            }
            throw error
        } catch let urlError as URLError {
            if urlError.code == .timedOut {
                Log.error("⏱️ 原始数据请求超时: \(url.absoluteString)")
                throw NetworkError.timeoutError
            }
            Log.error("🌐 原始数据请求网络错误: \(urlError.localizedDescription)")
            throw urlError
        }
    }
    
    // 🔄 新增：原始数据请求的静默令牌刷新重试逻辑
    private func performDataRefreshWithRetry(
        url: URL,
        method: HTTPMethod,
        body: Data?,
        contentType: ContentType,
        retryCount: Int
    ) async throws -> Data {
        Log.info("🔄 开始原始数据请求的静默令牌刷新重试 (剩余尝试次数: \(retryCount))")
        
        do {
            // 🔄 尝试刷新令牌
            Log.debug("🔄 调用 AuthManager.refreshToken()...")
            let newToken = try await AuthManager.shared.refreshToken()
            Log.info("✅ 令牌刷新成功，新令牌前缀: \(String(newToken.prefix(10)))...")
            
            // 等待一小段时间确保令牌更新完成
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
            
            Log.info("🔄 使用新令牌重新发送原始数据请求...")

            // 🔄 直接重新发送原始数据请求，避免双重重试机制
            var request = URLRequest(url: url)
            await configureRequestHeadersAsync(&request, contentType: contentType)
            
            if method != .get {
                request.httpMethod = method.rawValue
                request.httpBody = body
            }
            
            let session = URLSession.shared
            let (data, response) = try await session.data(for: request)
            
            // 检查HTTP响应
            guard let httpResponse = response as? HTTPURLResponse else {
                throw NetworkError.invalidResponse
            }
            
            // 检查成功状态码
            guard 200...299 ~= httpResponse.statusCode else {
                Log.error("❌ 原始数据请求失败: HTTP \(httpResponse.statusCode)")
                throw NetworkError.serverError(httpResponse.statusCode)
            }
            
            Log.debug("✅ 使用新令牌的原始数据请求成功: \(data.count) bytes")
            return data
            
        } catch let refreshError {
            Log.error("❌ 原始数据请求令牌刷新失败 (尝试 \(4 - retryCount)/3): \(refreshError.localizedDescription)")
            
            // 🔍 检查是否还有重试机会
            if retryCount > 1 {
                Log.info("🔄 原始数据请求令牌刷新失败，进行下一次静默重试...")
                
                // 🕐 实现指数退避延迟策略
                let delay = pow(2.0, Double(3 - retryCount)) // 2秒、4秒
                Log.debug("⏱️ 等待 \(delay) 秒后进行下一次重试...")
                
                try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                
                // 🔄 递归重试
                return try await performDataRefreshWithRetry(
                    url: url,
                    method: method,
                    body: body,
                    contentType: contentType,
                    retryCount: retryCount - 1
                )
            } else {
                // 🚫 所有重试都失败了
                Log.error("❌ 原始数据请求静默令牌刷新3次全部失败，触发重新登录")
                Log.error("❌ 最终失败原因: \(refreshError.localizedDescription)")
                
                // 🔍 检查刷新失败的原因
                if let networkError = refreshError as? NetworkError,
                   case .serverError(let code) = networkError,
                   [400, 401].contains(code) {
                    Log.warning("⚠️ 刷新令牌被服务器拒绝 (HTTP \(code))，触发重新登录")
                    await sendAuthenticationExpiredNotification(reason: "刷新令牌被服务器拒绝，3次尝试后失败")
                    throw NetworkError.unauthorized
                } else {
                    Log.warning("⚠️ 令牌刷新失败（其他错误），3次尝试后失败")
                    await sendAuthenticationExpiredNotification(reason: "令牌刷新失败，3次尝试后失败")
                    throw NetworkError.tokenRefreshFailed(refreshError)
                }
            }
        }
    }

    // MARK: - 向后兼容的方法（已弃用，建议使用上面的新方法）
    
    @available(*, deprecated, message: "请使用 request(_:method:body:contentType:) 方法")
    func fetch<T: Decodable>(
        _ url: URL,
        method: HTTPMethod = .get,
        body: Data? = nil,
        contentType: ContentType = .json
    ) async throws -> T {
        // 为了向后兼容，这里也使用带令牌刷新的版本，默认3次重试
        return try await fetchWithTokenRefresh(url, method: method, body: body, contentType: contentType, retryCount: 3)
    }

    @available(*, deprecated, message: "请使用 request(_:method:body:contentType:) 方法")
    func fetchWithRefresh<T: Decodable>(
        _ url: URL,
        method: HTTPMethod = .get,
        body: Data? = nil,
        contentType: ContentType = .json
    ) async throws -> T {
        // 使用新的静默重试机制，默认3次重试
        return try await fetchWithTokenRefresh(url, method: method, body: body, contentType: contentType, retryCount: 3)
    }

    // 添加专门处理Range请求的方法，返回原始Data而不是解码对象
    func fetchRangeData(
        _ url: URL,
        rangeHeader: String,
        contentType: ContentType = .video
    ) async throws -> Data {
        Log.debug("📡 开始Range请求: \(url.absoluteString)")
        Log.debug("📍 Range: \(rangeHeader)")
        
        var request = URLRequest(url: url)
        request.setValue(rangeHeader, forHTTPHeaderField: "Range")
        await configureRequestHeadersAsync(&request, contentType: contentType)

        let session = URLSession.shared

        do {
            let (data, response) = try await session.data(for: request)

            // 检查HTTP响应
            guard let httpResponse = response as? HTTPURLResponse else {
                throw NetworkError.invalidResponse
            }

            // 检查授权错误
            if httpResponse.statusCode == 401 {
                Log.error("🔒 授权失败: 请求返回401未授权状态码")
                throw NetworkError.unauthorized
            }

            // 对于Range请求，期望206 Partial Content响应
            guard httpResponse.statusCode == 206 else {
                Log.error("❌ Range请求错误: 期望206状态码，收到 \(httpResponse.statusCode)")
                throw NetworkError.serverError(httpResponse.statusCode)
            }

            Log.debug("✅ Range请求成功: \(data.count) bytes")
            return data

        } catch let urlError as URLError {
            if urlError.code == .timedOut {
                Log.error("⏱️ Range请求超时: \(url.absoluteString)")
                throw NetworkError.timeoutError
            }
            Log.error("🌐 Range请求网络错误: \(urlError.localizedDescription)")
            throw urlError
        }
    }

    // 添加支持Range请求的带令牌刷新版本
    func fetchRangeDataWithRefresh(
        _ url: URL,
        rangeHeader: String,
        contentType: ContentType = .video,
        retryCount: Int = 3  // 改为默认重试3次
    ) async throws -> Data {
        Log.debug("📡 开始Range请求（带令牌刷新）: \(url.absoluteString)")
        
        do {
            return try await fetchRangeData(url, rangeHeader: rangeHeader, contentType: contentType)
        } catch let error as NetworkError {
            // 获取刷新令牌状态
            let hasRefreshToken = await MainActor.run {
                return AuthManager.shared.hasRefreshToken
            }

            if error == .unauthorized && hasRefreshToken && retryCount > 0 {
                Log.info("🔄 Range请求授权失败，开始静默令牌刷新重试...")
                
                // 🔄 开始静默令牌刷新重试逻辑
                return try await performRangeRefreshWithRetry(
                    url: url,
                    rangeHeader: rangeHeader,
                    contentType: contentType,
                    retryCount: retryCount
                )
            } else {
                // 🚫 没有刷新令牌或重试次数用完，但不直接发送通知
                // 让TokenRefreshManager来决定如何处理这种情况
                let reason = !hasRefreshToken ? "无可用的刷新令牌" : "静默刷新令牌3次失败"
                Log.warning("⚠️ Range请求授权失败且\(reason)，但不直接提示用户")
                Log.warning("🔄 将错误抛出给TokenRefreshManager处理")
                
                // 🔄 不发送通知，让上层TokenRefreshManager根据错误类型智能处理
                throw NetworkError.unauthorized
            }
        }
    }
    
    // 🔄 新增：Range请求的静默令牌刷新重试逻辑
    private func performRangeRefreshWithRetry(
        url: URL,
        rangeHeader: String,
        contentType: ContentType,
        retryCount: Int
    ) async throws -> Data {
        Log.info("🔄 开始Range请求的静默令牌刷新重试 (剩余尝试次数: \(retryCount))")
        
        do {
            // 🔄 尝试刷新令牌
            Log.debug("🔄 调用 AuthManager.refreshToken()...")
            let newToken = try await AuthManager.shared.refreshToken()
            Log.info("✅ 令牌刷新成功，新令牌前缀: \(String(newToken.prefix(10)))...")
            
            // 等待一小段时间确保令牌更新完成
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
            
            Log.info("🔄 使用新令牌重新发送Range请求...")

            // 🔄 直接重新发送Range请求，避免双重重试机制
            var request = URLRequest(url: url)
            request.setValue(rangeHeader, forHTTPHeaderField: "Range")
            await configureRequestHeadersAsync(&request, contentType: contentType)
            
            let session = URLSession.shared
            let (data, response) = try await session.data(for: request)
            
            // 检查HTTP响应
            guard let httpResponse = response as? HTTPURLResponse else {
                throw NetworkError.invalidResponse
            }
            
            // 对于Range请求，期望206 Partial Content响应
            guard httpResponse.statusCode == 206 else {
                Log.error("❌ Range请求错误: 期望206状态码，收到 \(httpResponse.statusCode)")
                throw NetworkError.serverError(httpResponse.statusCode)
            }
            
            Log.debug("✅ 使用新令牌的Range请求成功: \(data.count) bytes")
            return data
            
        } catch let refreshError {
            Log.error("❌ Range请求令牌刷新失败 (尝试 \(4 - retryCount)/3): \(refreshError.localizedDescription)")
            
            // 🔍 检查是否还有重试机会
            if retryCount > 1 {
                Log.info("🔄 Range请求令牌刷新失败，进行下一次静默重试...")
                
                // 🕐 实现指数退避延迟策略
                let delay = pow(2.0, Double(3 - retryCount)) // 2秒、4秒
                Log.debug("⏱️ 等待 \(delay) 秒后进行下一次重试...")
                
                try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                
                // 🔄 递归重试
                return try await performRangeRefreshWithRetry(
                    url: url,
                    rangeHeader: rangeHeader,
                    contentType: contentType,
                    retryCount: retryCount - 1
                )
            } else {
                // 🚫 所有重试都失败了
                Log.error("❌ Range请求静默令牌刷新3次全部失败，触发重新登录")
                Log.error("❌ 最终失败原因: \(refreshError.localizedDescription)")
                
                // 🔍 检查刷新失败的原因
                if let networkError = refreshError as? NetworkError,
                   case .serverError(let code) = networkError,
                   [400, 401].contains(code) {
                    Log.warning("⚠️ 刷新令牌被服务器拒绝 (HTTP \(code))，触发重新登录")
                    await sendAuthenticationExpiredNotification(reason: "刷新令牌被服务器拒绝，3次尝试后失败")
                    throw NetworkError.unauthorized
                } else {
                    Log.warning("⚠️ 令牌刷新失败（其他错误），3次尝试后失败")
                    await sendAuthenticationExpiredNotification(reason: "令牌刷新失败，3次尝试后失败")
                    throw NetworkError.tokenRefreshFailed(refreshError)
                }
            }
        }
    }

    /// 检查设备是否已经注册
    func checkDeviceExists(hardwareSn: String) async throws -> DeviceExistsResponse {
        guard let userId = UserDefaultsManager.shared.userId else {
            throw NetworkError.unauthorized
        }
        
        // 构建 URL
        let urlString = "\(Configuration.API.cabyBaseUrl)/api/devices/check?user_id=\(userId)&hardware_sn=\(hardwareSn)"
        guard let url = URL(string: urlString.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? urlString) else {
            throw NetworkError.invalidURL
        }
        
        // 发送GET请求
        return try await get(url, type: DeviceExistsResponse.self)
    }

    /// 激活设备
    func activateDevice(hardwareSn: String) async throws -> DeviceActivationResponse {
        guard let userId = UserDefaultsManager.shared.userId else {
            throw NetworkError.unauthorized
        }
        
        // 构建请求体
        let request = DeviceActivationRequest(
            user_id: userId,
            hardware_sn: hardwareSn,
            remark: nil
        )
        
        // 编码请求体
        let encoder = JSONEncoder()
        let requestData = try encoder.encode(request)
        
        // 构建 URL
        let urlString = "\(Configuration.API.cabyBaseUrl)/api/devices"
        guard let url = URL(string: urlString) else {
            throw NetworkError.invalidURL
        }
        
        // 发送请求
        return try await post(url, body: requestData, type: DeviceActivationResponse.self)
    }
}

// URLSessionTaskDelegate 的线程安全实现
private final class RedirectHandlingDelegate: NSObject, URLSessionTaskDelegate, Sendable {
    
    // 使用 nonisolated 方法确保线程安全
    nonisolated func urlSession(
        _ session: URLSession,
        task: URLSessionTask,
        willPerformHTTPRedirection response: HTTPURLResponse,
        newRequest request: URLRequest,
        completionHandler: @escaping @Sendable (URLRequest?) -> Void
    ) {
        var newRequest = request
        // Copy original headers to redirected request
        if let originalRequest = task.originalRequest {
            originalRequest.allHTTPHeaderFields?.forEach { key, value in
                newRequest.setValue(value, forHTTPHeaderField: key)
            }
        }
        completionHandler(newRequest)
    }
}

// Extension to get device model name
extension UIDevice {
    var modelName: String {
        var systemInfo = utsname()
        uname(&systemInfo)
        let machineMirror = Mirror(reflecting: systemInfo.machine)
        let identifier = machineMirror.children.reduce("") { identifier, element in
            guard let value = element.value as? Int8, value != 0 else { return identifier }
            return identifier + String(UnicodeScalar(UInt8(value)))
        }
        return identifier
    }
}

/// 空响应结构体，用于不需要响应数据的API
struct EmptyResponse: Codable {
    // 空结构体，用于处理只需要状态码的API响应
}

// MARK: - 网络健康监控扩展
extension NetworkManager {
    
    // 网络统计数据结构
    struct NetworkStats {
        var totalRequests: Int = 0
        var successfulRequests: Int = 0
        var failedRequests: Int = 0
        var cloudflareErrors: Int = 0
        var authErrors: Int = 0
        var averageResponseTime: TimeInterval = 0
        var lastResetTime: Date = Date()
        
        var successRate: Double {
            return totalRequests > 0 ? Double(successfulRequests) / Double(totalRequests) : 0
        }
    }
    
    // 网络统计存储（线程安全）
    private static let networkStats = OSAllocatedUnfairLock(initialState: NetworkStats())
    
    /// 获取网络健康状态信息
    public func getNetworkHealthInfo() -> (
        successRate: Double,
        totalRequests: Int,
        cloudflareErrors: Int,
        authErrors: Int,
        averageResponseTime: TimeInterval,
        connectionStatus: String
    ) {
        let stats = Self.networkStats.withLock { $0 }
        let connectionStatus = isConnected.withLock { connected in
            if !connected {
                return "无网络连接"
            } else if isExpensiveConnection.withLock({ $0 }) {
                return "流量计费网络"
            } else if isLowDataMode.withLock({ $0 }) {
                return "低数据模式"
            } else {
                return "网络连接正常"
            }
        }
        
        return (
            successRate: stats.successRate,
            totalRequests: stats.totalRequests,
            cloudflareErrors: stats.cloudflareErrors,
            authErrors: stats.authErrors,
            averageResponseTime: stats.averageResponseTime,
            connectionStatus: connectionStatus
        )
    }
    
    /// 重置网络统计数据
    public func resetNetworkStats() {
        Self.networkStats.withLock { stats in
            stats = NetworkStats()
        }
        Log.network("网络统计已重置")
    }
    
    /// 更新网络统计数据（内部使用）
    private func updateNetworkStats(success: Bool, responseTime: TimeInterval, errorType: NetworkError? = nil) {
        Self.networkStats.withLock { stats in
            stats.totalRequests += 1
            
            if success {
                stats.successfulRequests += 1
            } else {
                stats.failedRequests += 1
                
                if let error = errorType {
                    switch error {
                    case .cloudflareTimeout:
                        stats.cloudflareErrors += 1
                    case .unauthorized:
                        stats.authErrors += 1
                    default:
                        break
                    }
                }
            }
            
            // 更新平均响应时间（简单移动平均）
            if stats.totalRequests > 0 {
                stats.averageResponseTime = (stats.averageResponseTime * Double(stats.totalRequests - 1) + responseTime) / Double(stats.totalRequests)
            }
        }
    }
    
    /// 获取网络连接类型描述
    public func getConnectionTypeDescription() -> String {
        return isConnected.withLock { connected in
            if !connected {
                return "无网络连接"
            }
            
            var descriptions: [String] = []
            
            if isExpensiveConnection.withLock({ $0 }) {
                descriptions.append("流量计费")
            }
            
            if isLowDataMode.withLock({ $0 }) {
                descriptions.append("低数据模式")
            }
            
            if descriptions.isEmpty {
                return "网络连接正常"
            } else {
                return descriptions.joined(separator: " · ")
            }
        }
    }
    
    /// 检查网络是否适合进行大文件传输
    public var isOptimalForLargeTransfers: Bool {
        return isConnected.withLock { connected in
            connected && !isExpensiveConnection.withLock({ $0 }) && !isLowDataMode.withLock({ $0 })
        }
    }
}

// MARK: - 设备激活相关模型

// 设备激活请求模型
struct DeviceActivationRequest: Codable {
    let user_id: String
    let hardware_sn: String
    let remark: String?
}

// 设备激活响应模型
struct DeviceActivationResponse: Codable {
    let id: Int
    let user_id: String
    let hardware_sn: String
    let status: Int
    let remark: String?
    let created_at: String
    let updated_at: String
}

// 设备存在检查响应模型
struct DeviceExistsResponse: Codable {
    let exists: Bool
}
