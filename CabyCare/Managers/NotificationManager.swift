import UIKit
@preconcurrency import UserNotifications
import SwiftUI

@MainActor
class NotificationManager: NSObject, UNUserNotificationCenterDelegate, ObservableObject {
    static let shared = NotificationManager()

    // 统一使用UserDefaultsManager进行存储
    private let storage = UserDefaultsManager.shared

    // 添加防重复操作的标志
    private var isSyncingSettings = false
    private var isSavingSettings = false

    // 通知设置状态
    @Published var isDailyNotificationsEnabled: Bool {
        didSet {
            storage.isDailyNotificationsEnabled = isDailyNotificationsEnabled
            saveSettingsDebounced()
        }
    }

    @Published var isStatsNotificationsEnabled: Bool {
        didSet {
            storage.isStatsNotificationsEnabled = isStatsNotificationsEnabled
            saveSettingsDebounced()
        }
    }

    @Published var quietTimeStart: Int {
        didSet {
            storage.quietTimeStart = quietTimeStart
            saveSettingsDebounced()
        }
    }

    @Published var quietTimeEnd: Int {
        didSet {
            storage.quietTimeEnd = quietTimeEnd
            saveSettingsDebounced()
        }
    }

    // 添加防抖定时器
    private var saveSettingsTimer: Timer?

    private func saveSettingsDebounced() {
        saveSettingsTimer?.invalidate()
        saveSettingsTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: false) { [weak self] _ in
            Task { [weak self] in
                await self?.saveSettingsToServer()
            }
        }
    }

    // 更新时间格式化辅助方法
    func formatTime(_ minutes: Int) -> String {
        let hours = minutes / 60
        let mins = minutes % 60

        let dateComponents = DateComponents(hour: hours, minute: mins)
        let date = Calendar.current.date(from: dateComponents) ?? Date()

        let formatter = DateFormatter()
        formatter.timeStyle = .short
        formatter.dateStyle = .none
        formatter.locale = Locale(identifier: "zh_CN") // 使用中文区域设置
        formatter.amSymbol = "上午"
        formatter.pmSymbol = "下午"

        return formatter.string(from: date)
    }

    private override init() {
        // 设置默认值 (使用分钟制)
        let defaultQuietTimeStart = 1380    // 晚上23:00 (23 * 60)
        let defaultQuietTimeEnd = 300       // 早上5:00 (5 * 60)

        // 从统一存储中初始化，如果没有值则使用默认值
        isDailyNotificationsEnabled = storage.isDailyNotificationsEnabled
        isStatsNotificationsEnabled = storage.isStatsNotificationsEnabled
        quietTimeStart = storage.quietTimeStart == 0 ? defaultQuietTimeStart : storage.quietTimeStart
        quietTimeEnd = storage.quietTimeEnd == 0 ? defaultQuietTimeEnd : storage.quietTimeEnd

        super.init()

        // 如果是默认值，保存到存储
        if storage.quietTimeStart == 0 {
            storage.quietTimeStart = defaultQuietTimeStart
        }
        if storage.quietTimeEnd == 0 {
            storage.quietTimeEnd = defaultQuietTimeEnd
        }

        // 设置通知中心代理
        UNUserNotificationCenter.current().delegate = self

        // 监听登录状态变化
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAuthStateChange),
            name: .authenticationStateChanged,
            object: nil
        )

        // 监听客户端绑定更新
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleClientBindingUpdated),
            name: .clientBindingUpdated,
            object: nil
        )

        // 不在初始化时同步设置，而是等待登录成功后再同步
    }

    // MARK: - Public Methods

    /// 初始化通知设置
    public func initializeSettings() async {
        // 检查是否已经获得通知权限
        let center = UNUserNotificationCenter.current()
        let settings = await center.notificationSettings()

        if settings.authorizationStatus == .notDetermined {
            // 如果未决定，请求权限
            await requestAuthorization()
        } else if settings.authorizationStatus == .authorized {
            // 如果已授权，注册远程通知
            await MainActor.run {
                UIApplication.shared.registerForRemoteNotifications()
            }

            // 如果已登录，同步设置
            if AuthManager.shared.isAuthenticated {
                await syncNotificationSettings()
            }
        }
    }

    /// 请求通知权限并注册远程通知
    func requestAuthorization() async {
        do {
            // 更新请求权限选项，包括临时通知和关键通知
            let options: UNAuthorizationOptions = [
                .alert,
                .sound,
                .badge,
                .provisional,  // 允许临时授权
                .criticalAlert // 允许关键通知
            ]

            let granted = try await UNUserNotificationCenter.current().requestAuthorization(options: options)
            if granted {
                Log.info("✅ 通知权限已授权")
                await MainActor.run {
                    UIApplication.shared.registerForRemoteNotifications()
                }
            } else {
                Log.warning("⚠️ 用户拒绝了通知权限")
            }
        } catch {
            Log.error("❌ 请求通知权限时出错: \(error.localizedDescription)")
        }
    }

    /// 处理设备令牌
    func handleDeviceToken(_ deviceToken: Data) {
        // 将设备令牌转换为大写的十六进制字符串
        let tokenString = deviceToken.map { String(format: "%02X", $0) }.joined()
        // Log.info("📱 收到设备推送令牌: \(tokenString)")

        // 保存当前令牌
        currentDeviceToken = tokenString

        // 发送令牌到服务器
        Task {
            if AuthManager.shared.isAuthenticated {
                await registerDevice()
            }
        }
    }

    /// 处理注册失败
    func handleRegistrationError(_ error: Error) {
        print("Remote notification support is unavailable due to error: \(error.localizedDescription)")
    }

    /// 保存通知设置
    public func saveSettings() {
        saveSettingsDebounced()
    }

    // MARK: - UNUserNotificationCenterDelegate

    // 添加处理后台通知的代理方法
    nonisolated func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // 允许在前台显示通知
        completionHandler([.banner, .sound, .badge])
    }

    // 更新处理通知的代理方法
    nonisolated func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping @Sendable () -> Void
    ) {
        let userInfo = response.notification.request.content.userInfo

        // 处理静默推送
        if let contentAvailable = userInfo["content-available"] as? Int, contentAvailable == 1 {
            Task { @MainActor in
                // 在后台更新数据 - 不需要传递userInfo，直接同步设置
                await handleSilentNotification()
            }
            completionHandler()
            return
        }

        // 解析通知数据
        guard let notificationData = parseNotificationData(from: userInfo) else {
            completionHandler()
            return
        }

        // 在主线程处理通知
        Task { @MainActor in
            await handleNotification(notificationData)
        }
        completionHandler()
    }

    // 添加处理静默推送的方法
    nonisolated private func handleSilentNotification() async {
        // 处理静默推送，例如同步数据
        await syncNotificationSettings()

        // 可以添加其他后台更新任务
        // 例如：更新缓存、同步其他设置等
    }

    // MARK: - Private Methods

    // 新增：注册推送令牌的方法
    private func registerPushToken(deviceId: String, tokenString: String) async {
        guard let url = URL(string: Configuration.API.Client.clientTokenPath(clientId: deviceId)) else {
            Log.error("❌ Invalid token registration URL")
            return
        }

        // 根据令牌特征判断环境
        // 生产环境的令牌长度为32字节（64个十六进制字符）
        let isSandbox = tokenString.count != 64

        let tokenBody: [String: Any] = [
            "client_token": tokenString,
            "token_type": "apns",
            "is_sandbox": isSandbox,
            "supports_background_notifications": true,  // 添加后台通知支持标志
            "supports_critical_alerts": true           // 添加关键通知支持标志
        ]

        do {
            let jsonData = try JSONSerialization.data(withJSONObject: tokenBody)
            
            // 打印请求信息以便调试
            // Log.debug("🔄 注册推送令牌请求: \(url.absoluteString)")
            // Log.debug("📤 令牌数据: \(tokenString)")
            // Log.debug("🌍 环境: \(isSandbox ? "开发环境" : "生产环境")")
            // Log.debug("📝 请求体: \(tokenBody)")
            
            // 使用NetworkManager发送POST请求
            let _: EmptyResponse = try await NetworkManager.shared.post(url, body: jsonData)

            Log.info("✅ Push token registration successful")
        } catch {
            Log.error("❌ Token registration error: \(error.localizedDescription)")
        }
    }

    private func handleSpecialNotification(name: String, price: Float) {
        // 处理特殊通知的逻辑
        print("Handling special notification: \(name) - \(price)")
    }

    private func formatTime(_ date: Date) -> String {
        Configuration.DateFormat.formatTime(date)
    }

    // 解析通知数据
    nonisolated private func parseNotificationData(from userInfo: [AnyHashable: Any]) -> NotificationData? {
        guard let jsonData = try? JSONSerialization.data(withJSONObject: userInfo),
              let notificationData = try? JSONDecoder().decode(NotificationData.self, from: jsonData) else {
            return nil
        }
        return notificationData
    }

    // 处理不同类型的通知
    private func handleNotification(_ notification: NotificationData) async {
        switch notification.type {
        case .daily:
            await handleDailyNotification(notification)
        case .alert:
            await handleAlertNotification(notification)
        case .stats:
            await handleStatsNotification(notification)
        case .health:
            await handleHealthNotification(notification)
        case .maintain:
            await handleMaintainNotification(notification)
        }
    }

    // 处理日常通知
    private func handleDailyNotification(_ notification: NotificationData) async {
        switch notification.subtype {
        case .excretion:
            if let videoId = notification.metadata.videoId,
               let timestamp = notification.metadata.videoTimestamp {
                await navigateToVideo(videoId: videoId, timestamp: timestamp)
            }
        case .litterLow:
            await showLitterLowAlert()
        case .litterDirty:
            await showLitterDirtyAlert()
        default:
            break
        }
    }

    // 处理异常通知
    private func handleAlertNotification(_ notification: NotificationData) async {
        switch notification.subtype {
        case .foreignAnimal, .foreignObject:
            await showAlertDetails(notification)
        case .systemError:
            await showSystemErrorDetails(notification)
        default:
            break
        }
    }

    // handle stats notification
    private func handleStatsNotification(_ notification: NotificationData) async {
        // TODO: Implement stats notification handling
    }

    // handle health notification
    private func handleHealthNotification(_ notification: NotificationData) async {
        // TODO: Implement health notification handling
    }

    // handle maintain notification
    private func handleMaintainNotification(_ notification: NotificationData) async {
        // TODO: Implement maintain notification handling
    }

    // MARK: - Navigation Helpers

    private func navigateToVideo(videoId: String, timestamp: Date) async {
        NotificationCenter.default.post(
            name: .didReceiveExcretionNotification,
            object: nil,
            userInfo: ["videoId": videoId, "timestamp": timestamp]
        )
    }

    private func showLitterLowAlert() async {
        // TODO: 实现猫砂不足提醒界面
    }

    private func showLitterDirtyAlert() async {
        // TODO: 实现需要铲屎提醒界面
    }

    private func showAlertDetails(_ notification: NotificationData) async {
        // TODO: 实现异常详情界面
    }

    private func showSystemErrorDetails(_ notification: NotificationData) async {
        // TODO: 实现系统错误详情界面
    }

    // MARK: - Notification Settings Sync

    /// 同步通知设置
    public func syncNotificationSettings() async {
        // 防止重复同步
        guard !isSyncingSettings else { return }
        isSyncingSettings = true
        defer { isSyncingSettings = false }

        // 确保用户已登录
        guard AuthManager.shared.isAuthenticated,
              let userId = storage.userId else {
            Log.warning("⚠️ 用户未登录或未找到用户ID，跳过同步通知设置")
            return
        }

        guard let url = URL(string: Configuration.API.Notification.settingsPath(userId: userId)) else {
            Log.error("❌ 无效的通知设置URL")
            return
        }

        do {
            // 使用NetworkManager获取通知设置
            let response: NotificationSettingsResponse = try await NetworkManager.shared.get(url)
            
            await MainActor.run {
                self.updateLocalSettings(with: response.settings)
            }
            Log.info("✅ 已同步通知设置")
        } catch {
            Log.error("❌ 获取通知设置失败: \(error.localizedDescription)")
            // 如果是解码错误，可能是首次登录，保存当前设置
            if error is DecodingError {
                Log.info("⚠️ 解析通知设置失败，将保存当前设置")
                await saveSettingsToServer()
            } else if let networkError = error as? NetworkError, 
                     case .serverError(let code) = networkError, code == 404 {
                // 如果是首次登录，服务器可能还没有设置，此时保存当前设置到服务器
                Log.info("⚠️ 服务器未找到通知设置，将保存当前设置")
                await saveSettingsToServer()
            }
        }
    }

    /// 更新本地设置
    private func updateLocalSettings(with settings: NotificationSettings) {
        isDailyNotificationsEnabled = settings.enable_daily
        isStatsNotificationsEnabled = settings.enable_stats
        quietTimeStart = settings.quiet_hours_start
        quietTimeEnd = settings.quiet_hours_end

        // 保存到本地存储
        storage.isDailyNotificationsEnabled = settings.enable_daily
        storage.isStatsNotificationsEnabled = settings.enable_stats
        storage.quietTimeStart = settings.quiet_hours_start
        storage.quietTimeEnd = settings.quiet_hours_end

        Log.info("✅ 已更新本地通知设置")
        Log.debug("🕒 免打扰时间：\(formatTime(quietTimeStart)) - \(formatTime(quietTimeEnd))")
        Log.debug("🌍 时区：\(settings.timezone)")
    }

    /// 保存设置到服务器
    private func saveSettingsToServer() async {
        // 防止重复保存
        guard !isSavingSettings else { return }
        isSavingSettings = true
        defer { isSavingSettings = false }

        guard let userId = UserDefaultsManager.shared.userId else {
            Log.error("❌ 未找到用户ID，无法保存通知设置")
            return
        }

        guard let url = URL(string: Configuration.API.Notification.settingsPath(userId: userId)) else {
            Log.error("❌ 无效的通知设置URL")
            return
        }

        // 获取本地时区
        let timezone = TimeZone.current.identifier

        // 构建请求体，将 user_id 作为顶层字段
        let requestBody: [String: Any] = [
            "user_id": userId,
            "enable_daily": isDailyNotificationsEnabled,
            "enable_stats": isStatsNotificationsEnabled,
            "quiet_hours_start": quietTimeStart,
            "quiet_hours_end": quietTimeEnd,
            "timezone": timezone
        ]

        do {
            let jsonData = try JSONSerialization.data(withJSONObject: requestBody)

            // 打印请求数据以便调试
//            if let requestString = String(data: jsonData, encoding: .utf8) {
                // Log.debug("📤 发送通知设置请求: \(requestString)")
                // Log.debug("🕒 免打扰时间：\(formatTime(quietTimeStart)) - \(formatTime(quietTimeEnd))")
//            }

            // 使用NetworkManager发送PUT请求
            let _: EmptyResponse = try await NetworkManager.shared.put(url, body: jsonData)
            
            Log.info("✅ 通知设置保存成功")
        } catch {
            Log.error("❌ 保存通知设置失败: \(error.localizedDescription)")
        }
    }

    @objc private func handleAuthStateChange() {
        Task {
            if AuthManager.shared.isAuthenticated {
                // 用户登录时，先初始化设置
                await initializeSettings()
                // 然后注册设备
                await registerDevice()
                // 最后同步设置
                await syncNotificationSettings()
            }
        }
    }

    /// 注册设备
    private func registerDevice() async {
        // 获取设备信息
        let deviceId = UIDevice.current.identifierForVendor?.uuidString ?? "unknown"

        // 获取用户ID
        guard let userId = UserDefaultsManager.shared.userId else {
            Log.error("❌ 未找到用户ID，无法注册设备")
            return
        }

        // 检查认证状态
        guard AuthManager.shared.isAuthenticated else {
            Log.error("❌ 用户未认证，无法注册设备")
            return
        }

        // 检查访问令牌
        guard let accessToken = UserDefaultsManager.shared.accessToken else {
            Log.error("❌ 未找到访问令牌，无法注册设备")
            return
        }

        Log.info("🧑‍💻 当前用户ID: \(userId)")
        Log.info("🔑 访问令牌前缀: \(String(accessToken.prefix(20)))...")
        Log.info("📱 设备ID: \(deviceId)")

        // 构建请求体（严格按照API文档格式）
        let requestBody: [String: Any] = [
            "user_id": userId,
            "client_id": Data(deviceId.utf8).base64EncodedString(),
            "client_type": "ios",
            "name": UIDevice.current.name,
            "model": UIDevice.current.modelName,  // 使用设备型号而非架构
            "os_version": UIDevice.current.systemVersion,
            "app_version": Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "unknown"
            // 移除 force_bind 字段，API文档中没有定义此字段
        ]

        // 构建请求 URL
        guard let url = URL(string: Configuration.API.Client.registerPath) else {
            Log.error("❌ Invalid URL: \(Configuration.API.Client.registerPath)")
            return
        }

        Log.info("🔗 注册设备API URL: \(url.absoluteString)")

        do {
            let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
            
            // 打印请求体以便调试
            if let requestString = String(data: jsonData, encoding: .utf8) {
                Log.info("📤 注册设备请求体: \(requestString)")
            }
            
            // 使用NetworkManager发送POST请求
            let _: EmptyResponse = try await NetworkManager.shared.post(url, body: jsonData)

            if let deviceToken = currentDeviceToken {
                await registerPushToken(deviceId: deviceId, tokenString: deviceToken)
            }
            Log.info("✅ 设备重新绑定成功")
        } catch {
            // 详细的错误处理
            Log.error("❌ 注册设备失败: \(error.localizedDescription)")
            Log.error("❌ 完整错误对象: \(error)")
            
            if let networkError = error as? NetworkError {
                Log.error("❌ NetworkError 类型: \(networkError)")
                switch networkError {
                case .unauthorized:
                    Log.error("❌ 401 未授权错误详细分析:")
                    Log.error("   - 请求URL: \(url)")
                    Log.error("   - 请求方法: POST")
                    Log.error("   - 当前用户ID: \(userId)")
                    Log.error("   - 设备ID: \(deviceId)")
                    Log.error("   - 访问令牌前缀: \(AuthManager.shared.accessToken?.prefix(20) ?? "无令牌")")
                    Log.error("   - 认证状态: \(AuthManager.shared.isAuthenticated)")
                    Log.error("   - 刷新令牌可用: \(AuthManager.shared.hasRefreshToken)")
                    Log.error("❌ 后端分析: /api/clients/register 端点位于 authorizedUser 路由组")
                    Log.error("❌ 中间件要求: AuthMiddleware 验证 Bearer token")
                    Log.error("❌ 可能原因: 令牌格式、内容或后端验证逻辑问题")
                    
                    // 打印完整的Authorization header
                    if let token = AuthManager.shared.accessToken {
                        Log.error("   - Authorization Header 内容: Bearer \(token.prefix(50))...")
                        Log.error("   - 令牌长度: \(token.count) 字符")
                        Log.error("   - 令牌包含特殊字符: \(token.contains("-") || token.contains("_") || token.contains("."))")
                    }
                    
                    // 不再尝试手动刷新令牌，因为NetworkManager已经尝试过了
                    // 现在应该依赖应用层的认证过期处理机制
                case .serverError(let code):
                    Log.error("❌ 服务器错误: HTTP \(code)")
                case .invalidURL:
                    Log.error("❌ 无效的URL")
                case .noData:
                    Log.error("❌ 无响应数据")
                case .decodingError(let decodingError):
                    Log.error("❌ 解码错误: \(decodingError.localizedDescription)")
                case .connectivityError:
                    Log.error("❌ 网络连接不可用")
                case .timeoutError:
                    Log.error("❌ 请求超时")
                case .authError:
                    Log.error("❌ 认证失败")
                case .tokenRefreshFailed(let refreshError):
                    Log.error("❌ 令牌刷新失败: \(refreshError.localizedDescription)")
                case .cloudflareTimeout:
                    Log.error("❌ CDN连接超时")
                case .invalidResponse:
                    Log.error("❌ 服务器响应格式无效")
                case .requestCancelled:
                    Log.error("❌ 网络请求被取消")
                }
            } else {
                Log.error("❌ 未知网络错误: \(type(of: error)) - \(error)")
            }
        }
    }

    // 存储当前设备令牌
    private var currentDeviceToken: String?

    // MARK: - Notification Handlers

    @objc private func handleClientBindingUpdated() {
        // 如果有存储的设备令牌，重新注册
        if let deviceToken = currentDeviceToken {
            Task {
                let deviceId = UIDevice.current.identifierForVendor?.uuidString ?? "unknown"
                await registerPushToken(deviceId: deviceId, tokenString: deviceToken)
            }
        }
    }

    // 添加获取授权头的方法
    private func getAuthorizationHeader() -> String? {
        // 直接从 UserDefaultsManager 获取 token
        if let token = UserDefaultsManager.shared.accessToken {
            return "Bearer \(token)"
        } else {
            Log.error("❌ 尝试访问受保护的API，但未找到访问令牌")
            return nil
        }
    }

    // MARK: - Client Management Methods

    /// 删除客户端推送令牌
    public func deleteClientToken() async {
        let deviceId = UIDevice.current.identifierForVendor?.uuidString ?? "unknown"
        
        guard let url = URL(string: Configuration.API.Client.clientTokenPath(clientId: deviceId)) else {
            Log.error("❌ 无效的令牌删除URL")
            return
        }

        do {
            // 使用NetworkManager发送DELETE请求
            let _: EmptyResponse = try await NetworkManager.shared.delete(url)
            
            Log.info("✅ 客户端推送令牌已删除")
        } catch {
            if let networkError = error as? NetworkError,
               case .serverError(let code) = networkError,
               [200, 404].contains(code) {
                Log.info("✅ 客户端推送令牌已删除")
            } else {
                Log.warning("⚠️ 删除客户端推送令牌时出错: \(error.localizedDescription)")
            }
        }
    }

    /// 删除客户端绑定关系
    public func deleteClientBinding() async {
        let deviceId = UIDevice.current.identifierForVendor?.uuidString ?? "unknown"
        
        guard let url = URL(string: Configuration.API.Client.getClientPath(clientId: deviceId)) else {
            Log.error("❌ 无效的客户端删除URL")
            return
        }

        do {
            // 使用NetworkManager发送DELETE请求
            let _: EmptyResponse = try await NetworkManager.shared.delete(url)
            
            Log.info("✅ 客户端绑定关系已删除")
        } catch {
            if let networkError = error as? NetworkError,
               case .serverError(let code) = networkError,
               [200, 404].contains(code) {
                Log.info("✅ 客户端绑定关系已删除")
            } else {
                Log.warning("⚠️ 删除客户端绑定关系时出错: \(error.localizedDescription)")
            }
        }
    }

    /// 完整的客户端清理方法（删除令牌和绑定关系）
    public func cleanupClientData() async {
        Log.info("🧹 开始清理客户端数据...")
        
        // 先删除推送令牌
        await deleteClientToken()
        
        // 再删除客户端绑定关系
        await deleteClientBinding()
        
        // 清理视频相关缓存
        await cleanupVideoCache()
        
        // 清理设备管理器缓存
        await cleanupDeviceCache()
        
        // 清理动物界面缓存
        await cleanupAnimalCache()
        
        // 清除本地存储的设备令牌
        currentDeviceToken = nil
        
        Log.info("✅ 客户端数据清理完成")
    }

    /// 清理视频相关缓存
    public func cleanupVideoCache() async {
        Log.info("🧹 开始清理视频缓存...")
        
        // 清理 VideoDataCache
        await VideoDataCache.shared.clearCache()
        Log.info("✅ VideoDataCache 已清除")
        
        // 清理 VideoCacheManager
        await withTaskGroup(of: Void.self) { group in
            group.addTask {
                // VideoCacheManager.shared.clearCache {
                //     Log.info("✅ VideoCacheManager 已清除")
                // }
                // 使用 CacheManager 替代
                CacheManager.shared.cleanupIfNeeded()
                Log.info("✅ VideoCacheManager 已清除")
            }
        }
        
        // 清理 URLSession 缓存
        URLCache.shared.removeAllCachedResponses()
        Log.info("✅ URLSession 缓存已清除")
        
        Log.info("✅ 视频缓存清理完成")
    }

    /// 清理设备管理器缓存
    private func cleanupDeviceCache() async {
        Log.info("🧹 开始清理设备管理器缓存...")
        
        await MainActor.run {
            // 清理 DeviceManager 缓存
            let deviceManager = DeviceManager()
            deviceManager.clearCache() // 清理所有设备的缓存
            
            Log.info("✅ DeviceManager 缓存已清除")
        }
        
        Log.info("✅ 设备管理器缓存清理完成")
    }

    /// 清理动物界面缓存
    public func cleanupAnimalCache() async {
        Log.info("🧹 开始清理动物界面缓存...")
        
        await MainActor.run {
            // 清理 CatManager 缓存
            let catManager = CatManager.shared
            catManager.cats = []
            
            // 清理 UserDefaults 中的猫咪缓存
            UserDefaults.standard.removeObject(forKey: "cached_cats")
            UserDefaults.standard.removeObject(forKey: "cats_cache_expiry")
            
            Log.info("✅ CatManager 缓存已清除")
        }
        
        // 清理通用缓存管理器
        CacheManager.shared.cleanupIfNeeded()
        Log.info("✅ CacheManager 缓存已清理")
        
        Log.info("✅ 动物界面缓存清理完成")
    }
}

// 通知相关的通知中心名称
extension Notification.Name {
    static let didReceiveExcretionNotification = Notification.Name("didReceiveExcretionNotification")
    static let authenticationStateChanged = Notification.Name("AuthenticationStateChanged")
    static let clientBindingUpdated = Notification.Name("ClientBindingUpdated")
}

// MARK: - Models

struct NotificationSettingsResponse: Codable {
    let settings: NotificationSettings
}

struct NotificationSettings: Codable {
    let user_id: String
    var enable_daily: Bool
    var enable_stats: Bool
    var quiet_hours_start: Int
    var quiet_hours_end: Int
    let timezone: String
    let updated_at: String

    enum CodingKeys: String, CodingKey {
        case user_id
        case enable_daily
        case enable_stats
        case quiet_hours_start
        case quiet_hours_end
        case timezone
        case updated_at
    }
}
