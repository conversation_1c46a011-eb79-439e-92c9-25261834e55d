import Foundation
@preconcurrency import CoreBluetooth
import Combine

// MARK: - 蓝牙管理器
@MainActor
class BluetoothManager: NSObject, ObservableObject {
    // MARK: - Published Properties
    @Published var isScanning = false
    @Published var discoveredDevices: [CBPeripheral] = []
    @Published var connectedDevice: CBPeripheral?
    @Published var connectionStatus: ConnectionStatus = .disconnected
    @Published var wifiCharacteristic: CBCharacteristic?
    @Published var userIdCharacteristic: CBCharacteristic?
    @Published var receivedData: [String] = []
    @Published var errorMessage: String?

    // 蓝牙状态管理
    @Published var bluetoothState: CBManagerState = .unknown
    @Published var isBluetoothReady: Bool = false
    @Published var bluetoothStatusMessage: String = "正在初始化蓝牙..."

    // 设备激活相关状态
    @Published var isActivating = false
    @Published var activationStatus: ActivationStatus = .idle
    @Published var activationMessage: String = ""
    @Published var isDeviceRegistered = false  // 设备是否已注册
    @Published var isCheckingRegistration = false  // 是否正在检查注册状态

    // 新添加的发送状态属性
    @Published var wifiSendStatus: SendStatus = .idle
    @Published var userIdSendStatus: SendStatus = .idle
    @Published var lastSuccessMessage: String = ""
    @Published var showSuccessAlert: Bool = false
    private var currentSendingType: String = ""

    // MARK: - 蓝牙相关常量
    private let serviceUUIDString = "12345678-1234-1234-1234-123456789ABC"
    private let wifiCharacteristicUUIDString = "12345678-1234-1234-1234-123456789ABD"
    private let userIdCharacteristicUUIDString = "12345678-1234-1234-1234-123456789ABE"

    // MARK: - 核心蓝牙对象
    @Published var centralManager: CBCentralManager!
    private var cancellables = Set<AnyCancellable>()
    private var scanTimeoutTask: Task<Void, Never>?

    // 硬件序列号读取相关
    private var hardwareSnData: Data?
    private var readCompletionHandler: ((Result<String, Error>) -> Void)?

    // 扫描控制
    private var shouldStartScanningWhenReady = false

    // MARK: - 连接状态枚举
    enum ConnectionStatus {
        case disconnected
        case connecting
        case connected
        case discovering
        case ready
    }

    enum SendStatus {
        case idle
        case sending
        case success
        case failed
    }

    enum ActivationStatus {
        case idle
        case checking        // 正在检查注册状态
        case registered      // 设备已注册
        case activating
        case success
        case failed(String)
    }

    // MARK: - 初始化
    override init() {
        super.init()
        centralManager = CBCentralManager(delegate: self, queue: nil)
        updateBluetoothStatus()
    }

    // MARK: - 公共方法

    /// 请求开始扫描（会处理蓝牙权限）
    func requestScanningIfNeeded() {
        shouldStartScanningWhenReady = true

        switch bluetoothState {
        case .poweredOn:
            startScanning()
        case .unauthorized:
            bluetoothStatusMessage = "需要蓝牙权限才能搜索设备"
            errorMessage = "请在设置中允许蓝牙权限"
        case .poweredOff:
            bluetoothStatusMessage = "请开启蓝牙功能"
            errorMessage = "请在设置中开启蓝牙"
        case .unsupported:
            bluetoothStatusMessage = "设备不支持蓝牙"
            errorMessage = "您的设备不支持蓝牙功能"
        default:
            bluetoothStatusMessage = "正在初始化蓝牙..."
        }
    }

    /// 开始扫描 AbyBox 设备
    func startScanning() {
        guard bluetoothState == .poweredOn else {
            // 不要立即显示错误，而是等待蓝牙状态更新
            shouldStartScanningWhenReady = true
            return
        }

        guard !isScanning else { return }

        isScanning = true
        discoveredDevices.removeAll()
        errorMessage = nil
        bluetoothStatusMessage = "正在搜索 AbyBox 设备..."

        centralManager.scanForPeripherals(withServices: nil, options: [CBCentralManagerScanOptionAllowDuplicatesKey: false])

        // 30秒后自动停止扫描
        scanTimeoutTask?.cancel()
        scanTimeoutTask = Task { @MainActor in
            try? await Task.sleep(nanoseconds: 30_000_000_000) // 30秒
            if !Task.isCancelled {
                stopScanning()
            }
        }
    }

    /// 停止扫描
    func stopScanning() {
        isScanning = false
        shouldStartScanningWhenReady = false
        centralManager.stopScan()
        scanTimeoutTask?.cancel()
        scanTimeoutTask = nil

        if discoveredDevices.isEmpty && bluetoothState == .poweredOn {
            bluetoothStatusMessage = "未发现 AbyBox 设备"
        }
    }

    /// 更新蓝牙状态显示
    private func updateBluetoothStatus() {
        bluetoothState = centralManager?.state ?? .unknown
        isBluetoothReady = bluetoothState == .poweredOn

        switch bluetoothState {
        case .poweredOn:
            bluetoothStatusMessage = "蓝牙已就绪"
            errorMessage = nil

            // 如果之前尝试扫描，现在开始扫描
            if shouldStartScanningWhenReady {
                startScanning()
            }

        case .poweredOff:
            bluetoothStatusMessage = "蓝牙已关闭"
            errorMessage = "请在设置中开启蓝牙"

        case .unauthorized:
            bluetoothStatusMessage = "蓝牙权限未授权"
            errorMessage = "请在设置中允许蓝牙权限"

        case .unsupported:
            bluetoothStatusMessage = "设备不支持蓝牙"
            errorMessage = "您的设备不支持蓝牙功能"

        case .resetting:
            bluetoothStatusMessage = "蓝牙正在重置..."

        default:
            bluetoothStatusMessage = "正在初始化蓝牙..."
        }
    }

    /// 连接设备
    func connectToDevice(_ peripheral: CBPeripheral) {
        stopScanning()
        connectionStatus = .connecting
        connectedDevice = peripheral
        peripheral.delegate = self
        centralManager.connect(peripheral, options: nil)
    }

    /// 断开连接
    func disconnectDevice() {
        if let device = connectedDevice {
            centralManager.cancelPeripheralConnection(device)
        }
        resetConnection()
    }

    /// 发送 WiFi 配置
    func sendWiFiConfig(ssid: String, password: String) {
        wifiSendStatus = .sending

        let wifiData = WiFiConfigData(isValid: true, ssid: ssid, password: password)
        let jsonData = wifiData.toJSONData()

        sendData(jsonData, to: wifiCharacteristic, type: "WiFi配置")
    }

    /// 发送 User ID
    func sendUserId(_ userId: String) {
        userIdSendStatus = .sending

        let userData = UserIdData(isValid: true, userId: userId)
        let jsonData = userData.toJSONData()

        sendData(jsonData, to: userIdCharacteristic, type: "User ID")
    }

    /// 读取硬件序列号
    func readHardwareSn() async throws -> String {
        guard let characteristic = userIdCharacteristic else {
            throw NSError(domain: "BluetoothError", code: -1, userInfo: [NSLocalizedDescriptionKey: "User ID特征值不可用"])
        }

        guard let device = connectedDevice else {
            throw NSError(domain: "BluetoothError", code: -2, userInfo: [NSLocalizedDescriptionKey: "设备未连接"])
        }

        return try await withCheckedThrowingContinuation { continuation in
            readCompletionHandler = { result in
                continuation.resume(with: result)
            }

            // 清除之前的数据
            hardwareSnData = nil

            // 读取特征值
            device.readValue(for: characteristic)

            // 设置超时
            DispatchQueue.main.asyncAfter(deadline: .now() + 10) { [weak self] in
                if let handler = self?.readCompletionHandler {
                    self?.readCompletionHandler = nil
                    handler(.failure(NSError(domain: "BluetoothError", code: -4, userInfo: [NSLocalizedDescriptionKey: "读取超时"])))
                }
            }
        }
    }

    /// 激活设备
    func activateDevice() async {
        // 1. 检查设备连接状态
        guard connectionStatus == .ready else {
            activationStatus = .failed("设备未就绪")
            activationMessage = "设备未连接或未完成初始化"
            return
        }

        // 2. 重置激活状态
        resetActivationState()

        do {
            // 3. 开始检查注册状态
            startCheckingRegistration()

            // 4. 读取硬件序列号
            let hardwareSn = try await readHardwareSerialNumber()

            // 5. 检查设备是否已注册
            if try await checkDeviceRegistrationStatus(hardwareSn: hardwareSn) {
                handleDeviceAlreadyRegistered()
                return
            }

            // 6. 开始激活设备
            try await performDeviceActivation(hardwareSn: hardwareSn)

            // 7. 激活成功处理
            handleActivationSuccess(hardwareSn: hardwareSn)

        } catch {
            // 8. 激活失败处理
            handleActivationError(error)
        }
    }

    // MARK: - 激活设备的辅助方法

    /// 重置激活状态
    private func resetActivationState() {
        isCheckingRegistration = false
        isActivating = false
        isDeviceRegistered = false
        activationStatus = .idle
        activationMessage = ""
        errorMessage = nil
    }

    /// 开始检查注册状态
    private func startCheckingRegistration() {
        isCheckingRegistration = true
        activationStatus = .checking
        activationMessage = "正在检查设备注册状态..."
        Log.debug("🔍 开始检查设备注册状态")
    }

    /// 读取硬件序列号
    private func readHardwareSerialNumber() async throws -> String {
        Log.debug("📖 正在读取硬件序列号...")

        let hardwareSn = try await readHardwareSn()

        guard !hardwareSn.isEmpty else {
            throw NSError(domain: "BluetoothError", code: -5, userInfo: [
                NSLocalizedDescriptionKey: "硬件序列号为空"
            ])
        }

        Log.debug("✅ 成功读取硬件序列号: \(hardwareSn)")
        return hardwareSn
    }

    /// 检查设备注册状态
    private func checkDeviceRegistrationStatus(hardwareSn: String) async throws -> Bool {
        Log.debug("🔍 检查设备是否已注册: \(hardwareSn)")

        let existsResponse = try await NetworkManager.shared.checkDeviceExists(hardwareSn: hardwareSn)

        Log.debug("📋 设备注册状态检查结果: \(existsResponse.exists ? "已注册" : "未注册")")
        return existsResponse.exists
    }

    /// 处理设备已注册的情况
    private func handleDeviceAlreadyRegistered() {
        isCheckingRegistration = false
        isDeviceRegistered = true
        activationStatus = .registered
        activationMessage = "设备已经被激活，无需重复激活"

        Log.info("ℹ️ 设备已经注册，无需重复激活")
    }

    /// 执行设备激活
    private func performDeviceActivation(hardwareSn: String) async throws {
        // 更新状态为激活中
        isCheckingRegistration = false
        isActivating = true
        activationStatus = .activating
        activationMessage = "正在激活设备，请稍等..."

        Log.info("🚀 开始激活设备: \(hardwareSn)")

        // 调用激活API并等待结果
        let activationResponse = try await NetworkManager.shared.activateDevice(hardwareSn: hardwareSn)

        // 验证激活响应
        guard let responseHardwareSn = activationResponse.hardware_sn as String?,
              responseHardwareSn == hardwareSn else {
            throw NSError(domain: "ActivationError", code: -6, userInfo: [
                NSLocalizedDescriptionKey: "激活响应中的硬件序列号不匹配"
            ])
        }

        // 检查激活状态
        guard activationResponse.status == 1 else {
            throw NSError(domain: "ActivationError", code: -7, userInfo: [
                NSLocalizedDescriptionKey: "设备激活失败，服务器返回状态: \(activationResponse.status)"
            ])
        }

        Log.info("✅ 设备激活API调用成功")
    }

    /// 处理激活成功
    private func handleActivationSuccess(hardwareSn: String) {
        isActivating = false
        isDeviceRegistered = true
        activationStatus = .success
        activationMessage = "设备激活成功！硬件序列号: \(hardwareSn)"
        lastSuccessMessage = "设备激活成功！"
        showSuccessAlert = true

        Log.info("🎉 设备激活成功: \(hardwareSn)")

        // 3秒后重置状态到空闲
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) { [weak self] in
            self?.activationStatus = .idle
            self?.showSuccessAlert = false
        }
    }

    /// 处理激活错误
    private func handleActivationError(_ error: Error) {
        isCheckingRegistration = false
        isActivating = false

        Log.error("❌ 设备激活失败: \(error.localizedDescription)")

        let errorMessage: String

        // 处理网络错误
        if let networkError = error as? NetworkError {
            errorMessage = handleNetworkActivationError(networkError)
        } else {
            // 处理其他错误（如蓝牙读取错误等）
            errorMessage = "激活失败: \(error.localizedDescription)"
        }

        activationStatus = .failed(errorMessage)
        activationMessage = errorMessage

        Log.error("💔 激活失败，错误信息: \(errorMessage)")
    }

    /// 处理网络激活错误
    private func handleNetworkActivationError(_ networkError: NetworkError) -> String {
        switch networkError {
        case .serverError(let code):
            return handleServerActivationError(code)

        case .unauthorized:
            return "未授权，请重新登录后再试"

        case .timeoutError:
            return "激活请求超时，请检查网络连接后重试"

        case .connectivityError:
            return "网络连接不可用，请检查网络设置"

        case .requestCancelled:
            return "激活请求被取消"

        case .tokenRefreshFailed:
            return "认证令牌刷新失败，请重新登录"

        default:
            return "网络错误: \(networkError.localizedDescription)"
        }
    }

    /// 处理服务器激活错误
    private func handleServerActivationError(_ statusCode: Int) -> String {
        switch statusCode {
        case 400:
            return "请求数据无效，请检查用户ID和硬件序列号"

        case 409:
            // 409 冲突 - 设备已被激活，这实际上是成功的情况
            DispatchQueue.main.async { [weak self] in
                self?.isDeviceRegistered = true
                self?.activationStatus = .registered
                self?.activationMessage = "设备已被激活"
            }
            return "设备已被激活"

        case 500:
            // 500 服务器内部错误 - 可能设备已激活
            DispatchQueue.main.async { [weak self] in
                self?.isDeviceRegistered = true
                self?.activationStatus = .registered
                self?.activationMessage = "设备已经激活过了，无需重复激活"
            }
            return "设备已经激活过了，无需重复激活"

        case 422:
            return "设备数据验证失败，请检查硬件序列号"

        case 503:
            return "服务暂时不可用，请稍后重试"

        default:
            return "服务器错误 (HTTP \(statusCode))，请稍后重试"
        }
    }

    // MARK: - 私有方法

    private func resetConnection() {
        connectedDevice = nil
        connectionStatus = .disconnected
        wifiCharacteristic = nil
        userIdCharacteristic = nil
    }

    private func sendData(_ data: Data, to characteristic: CBCharacteristic?, type: String) {
        guard let characteristic = characteristic else {
            errorMessage = "\(type) 特征值不可用"
            if type == "WiFi配置" {
                wifiSendStatus = .failed
            } else if type == "User ID" {
                userIdSendStatus = .failed
            }
            return
        }

        guard let device = connectedDevice else {
            errorMessage = "设备未连接"
            if type == "WiFi配置" {
                wifiSendStatus = .failed
            } else if type == "User ID" {
                userIdSendStatus = .failed
            }
            return
        }

        currentSendingType = type
        device.writeValue(data, for: characteristic, type: .withResponse)
    }
}

// MARK: - CBCentralManagerDelegate
extension BluetoothManager: CBCentralManagerDelegate {
    nonisolated func centralManagerDidUpdateState(_ central: CBCentralManager) {
        Task { @MainActor [weak self] in
            guard let self = self else { return }
            self.updateBluetoothStatus()
        }
    }

    nonisolated func centralManager(_ central: CBCentralManager, didDiscover peripheral: CBPeripheral, advertisementData: [String : Any], rssi RSSI: NSNumber) {
        // 只添加名称以 "AbyBox" 开头的设备
        if let name = peripheral.name, name.hasPrefix("AbyBox") {
            Task { @MainActor [weak self] in
                guard let self = self else { return }
                if !self.discoveredDevices.contains(where: { $0.identifier == peripheral.identifier }) {
                    self.discoveredDevices.append(peripheral)
                }
            }
        }
    }

    nonisolated func centralManager(_ central: CBCentralManager, didConnect peripheral: CBPeripheral) {
        Task { @MainActor [weak self] in
            guard let self = self else { return }
            self.connectionStatus = .discovering
        }
        peripheral.discoverServices([CBUUID(string: serviceUUIDString)])
    }

    nonisolated func centralManager(_ central: CBCentralManager, didFailToConnect peripheral: CBPeripheral, error: Error?) {
        Task { @MainActor [weak self] in
            guard let self = self else { return }
            self.errorMessage = "连接失败: \(error?.localizedDescription ?? "未知错误")"
            self.resetConnection()
        }
    }

    nonisolated func centralManager(_ central: CBCentralManager, didDisconnectPeripheral peripheral: CBPeripheral, error: Error?) {
        Task { @MainActor [weak self] in
            guard let self = self else { return }
            if let error = error {
                self.errorMessage = "连接断开: \(error.localizedDescription)"
            }
            self.resetConnection()
        }
    }
}

// MARK: - CBPeripheralDelegate
extension BluetoothManager: CBPeripheralDelegate {
    nonisolated func peripheral(_ peripheral: CBPeripheral, didDiscoverServices error: Error?) {
        if let error = error {
            Task { @MainActor [weak self] in
                guard let self = self else { return }
                self.errorMessage = "服务发现失败: \(error.localizedDescription)"
            }
            return
        }

        guard let services = peripheral.services else { return }

        for service in services {
            if service.uuid == CBUUID(string: serviceUUIDString) {
                peripheral.discoverCharacteristics([
                    CBUUID(string: wifiCharacteristicUUIDString),
                    CBUUID(string: userIdCharacteristicUUIDString)
                ], for: service)
            }
        }
    }

    nonisolated func peripheral(_ peripheral: CBPeripheral, didDiscoverCharacteristicsFor service: CBService, error: Error?) {
        if let error = error {
            Task { @MainActor [weak self] in
                guard let self = self else { return }
                self.errorMessage = "特征值发现失败: \(error.localizedDescription)"
            }
            return
        }

        guard let characteristics = service.characteristics else { return }

        // 在非Task上下文中处理characteristics并设置通知
        var foundWifiUUID: String?
        var foundUserIdUUID: String?

        for characteristic in characteristics {
            switch characteristic.uuid {
            case CBUUID(string: wifiCharacteristicUUIDString):
                foundWifiUUID = characteristic.uuid.uuidString
                peripheral.setNotifyValue(true, for: characteristic)
            case CBUUID(string: userIdCharacteristicUUIDString):
                foundUserIdUUID = characteristic.uuid.uuidString
                peripheral.setNotifyValue(true, for: characteristic)
            default:
                break
            }
        }

        // 然后在Task中更新状态，重新查找特征值
        Task { @MainActor [weak self] in
            guard let self = self else { return }
            guard let serviceCharacteristics = service.characteristics else { return }

            for characteristic in serviceCharacteristics {
                if let wifiUUID = foundWifiUUID, characteristic.uuid.uuidString == wifiUUID {
                    self.wifiCharacteristic = characteristic
                }
                if let userIdUUID = foundUserIdUUID, characteristic.uuid.uuidString == userIdUUID {
                    self.userIdCharacteristic = characteristic
                }
            }

            // 检查是否所有特征值都已发现
            if self.wifiCharacteristic != nil && self.userIdCharacteristic != nil {
                self.connectionStatus = .ready
            }
        }
    }

    nonisolated func peripheral(_ peripheral: CBPeripheral, didUpdateValueFor characteristic: CBCharacteristic, error: Error?) {
        if let error = error {
            Task { @MainActor [weak self] in
                guard let self = self else { return }
                self.errorMessage = "数据读取失败: \(error.localizedDescription)"

                // 如果有读取回调，传递错误
                if let handler = self.readCompletionHandler {
                    self.readCompletionHandler = nil
                    handler(.failure(error))
                }
            }
            return
        }

        guard let data = characteristic.value else { return }

        // 处理硬件序列号读取
        if characteristic.uuid == CBUUID(string: userIdCharacteristicUUIDString) {
            Task { @MainActor [weak self] in
                guard let self = self else { return }

                // 如果有读取回调，处理数据
                if let handler = self.readCompletionHandler {
                    self.readCompletionHandler = nil

                    // 尝试解析JSON数据
                    if String(data: data, encoding: .utf8) != nil {
                        do {
                            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                               let hardwareSn = json["hardware_sn"] as? String {
                                handler(.success(hardwareSn))
                            } else {
                                handler(.failure(NSError(domain: "BluetoothError", code: -3, userInfo: [NSLocalizedDescriptionKey: "JSON格式无效或缺少hardware_sn字段"])))
                            }
                        } catch {
                            handler(.failure(NSError(domain: "BluetoothError", code: -3, userInfo: [NSLocalizedDescriptionKey: "JSON解析失败: \(error.localizedDescription)"])))
                        }
                    } else {
                        handler(.failure(NSError(domain: "BluetoothError", code: -3, userInfo: [NSLocalizedDescriptionKey: "无法将数据转换为字符串"])))
                    }
                    return
                }
            }
        }

        if let receivedString = String(data: data, encoding: .utf8) {
            Task { @MainActor [weak self] in
                guard let self = self else { return }
                self.receivedData.append("收到数据: \(receivedString)")
            }
        }
    }

    nonisolated func peripheral(_ peripheral: CBPeripheral, didWriteValueFor characteristic: CBCharacteristic, error: Error?) {
        Task { @MainActor [weak self] in
            guard let self = self else { return }
            self.receivedData.append("数据发送成功: \(self.currentSendingType)")
            if self.currentSendingType == "WiFi配置" {
                self.wifiSendStatus = .success
                self.lastSuccessMessage = "WiFi配置发送成功！"
            } else if self.currentSendingType == "User ID" {
                self.userIdSendStatus = .success
                self.lastSuccessMessage = "User ID发送成功！"
            }
            self.showSuccessAlert = true

            // 3秒后重置状态
            DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                self.wifiSendStatus = .idle
                self.userIdSendStatus = .idle
                self.showSuccessAlert = false
            }

            self.currentSendingType = ""
        }
    }
}

// MARK: - 数据模型
struct WiFiConfigData: Codable {
    let isValid: Bool
    let ssid: String
    let password: String

    private enum CodingKeys: String, CodingKey {
        case isValid = "is_valid"
        case ssid
        case password
    }

    func toJSONData() -> Data {
        let encoder = JSONEncoder()
        return (try? encoder.encode(self)) ?? Data()
    }
}

struct UserIdData: Codable {
    let isValid: Bool
    let userId: String

    private enum CodingKeys: String, CodingKey {
        case isValid = "is_valid"
        case userId = "user_id"
    }

    func toJSONData() -> Data {
        let encoder = JSONEncoder()
        return (try? encoder.encode(self)) ?? Data()
    }
}