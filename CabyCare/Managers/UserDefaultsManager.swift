import Foundation
import os

/// 集中管理UserDefaults的管理器 - 线程安全设计
final class UserDefaultsManager: Sendable {
    static let shared = UserDefaultsManager()

    // 直接使用UserDefaults.standard，通过锁来保证线程安全
    private let lock = OSAllocatedUnfairLock()

    private init() {
        // 私有初始化方法确保单例模式
    }

    // MARK: - 通用存储方法
    
    /// 通用获取字符串方法 - 线程安全
    func getString(key: String) -> String? {
        return lock.withLock {
            UserDefaults.standard.string(forKey: key)
        }
    }
    
    /// 通用获取布尔值方法 - 线程安全
    func getBool(key: String) -> Bool {
        return lock.withLock {
            UserDefaults.standard.bool(forKey: key)
        }
    }
    
    /// 通用获取整数方法 - 线程安全
    func getInt(key: String) -> Int {
        return lock.withLock {
            UserDefaults.standard.integer(forKey: key)
        }
    }
    
    /// 通用设置字符串方法 - 线程安全
    func setString(key: String, value: String?) {
        lock.withLock {
            UserDefaults.standard.set(value, forKey: key)
            UserDefaults.standard.synchronize()
        }
    }
    
    /// 通用设置布尔值方法 - 线程安全
    func setBool(key: String, value: Bool) {
        lock.withLock {
            UserDefaults.standard.set(value, forKey: key)
            UserDefaults.standard.synchronize()
        }
    }
    
    /// 通用设置整数方法 - 线程安全
    func setInt(key: String, value: Int) {
        lock.withLock {
            UserDefaults.standard.set(value, forKey: key)
            UserDefaults.standard.synchronize()
        }
    }
    
    /// 通用删除方法 - 线程安全
    func remove(key: String) {
        lock.withLock {
            UserDefaults.standard.removeObject(forKey: key)
            UserDefaults.standard.synchronize()
        }
    }

    // MARK: - 键定义
    struct Keys {
        // 认证相关
        static let accessToken = "access_token"
        static let refreshToken = "refresh_token"
        static let idToken = "id_token"
        static let userId = "user_id"
        static let logtoId = "logto_id"
        static let authState = "auth_state"

        // 通知相关
        static let dailyNotificationsEnabled = "dailyNotificationsEnabled"
        static let statsNotificationsEnabled = "statsNotificationsEnabled"
        static let quietTimeStart = "quietTimeStart"
        static let quietTimeEnd = "quietTimeEnd"
    }

    // MARK: - 认证相关方法

    var accessToken: String? {
        get { 
            lock.withLock {
                UserDefaults.standard.string(forKey: Keys.accessToken)
            }
        }
        set { 
            lock.withLock {
                UserDefaults.standard.set(newValue, forKey: Keys.accessToken)
                UserDefaults.standard.synchronize()
            }
        }
    }

    var refreshToken: String? {
        get { 
            lock.withLock {
                UserDefaults.standard.string(forKey: Keys.refreshToken)
            }
        }
        set { 
            lock.withLock {
                UserDefaults.standard.set(newValue, forKey: Keys.refreshToken)
                UserDefaults.standard.synchronize()
            }
        }
    }

    var idToken: String? {
        get { 
            lock.withLock {
                UserDefaults.standard.string(forKey: Keys.idToken)
            }
        }
        set { 
            lock.withLock {
                UserDefaults.standard.set(newValue, forKey: Keys.idToken)
                UserDefaults.standard.synchronize()
            }
        }
    }

    var userId: String? {
        get { 
            lock.withLock {
                UserDefaults.standard.string(forKey: Keys.userId)
            }
        }
        set { 
            lock.withLock {
                UserDefaults.standard.set(newValue, forKey: Keys.userId)
                UserDefaults.standard.synchronize()
            }
        }
    }

    var logtoId: String? {
        get { 
            lock.withLock {
                UserDefaults.standard.string(forKey: Keys.logtoId)
            }
        }
        set { 
            lock.withLock {
                UserDefaults.standard.set(newValue, forKey: Keys.logtoId)
                UserDefaults.standard.synchronize()
            }
        }
    }

    var authState: String? {
        get { 
            lock.withLock {
                UserDefaults.standard.string(forKey: Keys.authState)
            }
        }
        set { 
            lock.withLock {
                UserDefaults.standard.set(newValue, forKey: Keys.authState)
                UserDefaults.standard.synchronize()
            }
        }
    }

    // MARK: - 通知相关方法

    var isDailyNotificationsEnabled: Bool {
        get { 
            lock.withLock {
                UserDefaults.standard.bool(forKey: Keys.dailyNotificationsEnabled)
            }
        }
        set { 
            lock.withLock {
                UserDefaults.standard.set(newValue, forKey: Keys.dailyNotificationsEnabled)
                UserDefaults.standard.synchronize()
            }
        }
    }

    var isStatsNotificationsEnabled: Bool {
        get { 
            lock.withLock {
                UserDefaults.standard.bool(forKey: Keys.statsNotificationsEnabled)
            }
        }
        set { 
            lock.withLock {
                UserDefaults.standard.set(newValue, forKey: Keys.statsNotificationsEnabled)
                UserDefaults.standard.synchronize()
            }
        }
    }

    var quietTimeStart: Int {
        get { 
            lock.withLock {
                UserDefaults.standard.integer(forKey: Keys.quietTimeStart)
            }
        }
        set { 
            lock.withLock {
                UserDefaults.standard.set(newValue, forKey: Keys.quietTimeStart)
                UserDefaults.standard.synchronize()
            }
        }
    }

    var quietTimeEnd: Int {
        get { 
            lock.withLock {
                UserDefaults.standard.integer(forKey: Keys.quietTimeEnd)
            }
        }
        set { 
            lock.withLock {
                UserDefaults.standard.set(newValue, forKey: Keys.quietTimeEnd)
                UserDefaults.standard.synchronize()
            }
        }
    }

    // MARK: - 用户凭证管理

    /// 清除所有身份验证相关的数据 - 线程安全
    func clearAuthCredentials() {
        lock.withLock {
            UserDefaults.standard.removeObject(forKey: Keys.accessToken)
            UserDefaults.standard.removeObject(forKey: Keys.refreshToken)
            UserDefaults.standard.removeObject(forKey: Keys.idToken)
            UserDefaults.standard.removeObject(forKey: Keys.userId)
            UserDefaults.standard.removeObject(forKey: Keys.logtoId)
            UserDefaults.standard.removeObject(forKey: Keys.authState)
            
            // 清除令牌过期时间
            UserDefaults.standard.removeObject(forKey: "access_token_expires_at")
            UserDefaults.standard.synchronize()
        }

        Log.info("🧹 已清除所有登录凭证")
    }

    /// 保存身份验证令牌和用户信息 - 线程安全
    func saveAuthData(tokens: [String: Any]) {
        // 创建本地副本避免跨隔离域传递
        let accessTokenValue = tokens["access_token"] as? String
        let refreshTokenValue = tokens["refresh_token"] as? String
        let idTokenValue = tokens["id_token"] as? String
        let userIdValue = tokens["user_id"] as? String
        let logtoIdValue = tokens["logto_id"] as? String
        
        // 添加详细的调试日志
        Log.info("💾 开始保存认证数据...")
        Log.info("🔍 收到的tokens数据: \(tokens)")
        
        if let refreshToken = refreshTokenValue {
            if refreshToken.isEmpty {
                Log.warning("⚠️ 刷新令牌为空字符串")
            } else {
                Log.info("✅ 收到有效的刷新令牌: \(String(refreshToken.prefix(10)))...")
            }
        } else {
            Log.warning("⚠️ 后端响应中没有刷新令牌字段或为nil")
        }
        
        lock.withLock {
            var shouldSynchronize = false

            if let accessToken = accessTokenValue {
                UserDefaults.standard.set(accessToken, forKey: Keys.accessToken)
                Log.info("✅ 已保存访问令牌")
                shouldSynchronize = true
            }

            // 如果响应中包含刷新令牌，则保存它
            // 我们不会使用访问令牌作为刷新令牌
            if let refreshToken = refreshTokenValue, !refreshToken.isEmpty {
                UserDefaults.standard.set(refreshToken, forKey: Keys.refreshToken)
                Log.info("✅ 已保存刷新令牌: \(String(refreshToken.prefix(10)))...")
                shouldSynchronize = true
            } else {
                Log.error("❌ 无法保存刷新令牌 - 值为空或nil")
                // 显式检查UserDefaults中是否还有旧的刷新令牌
                if let existingRefreshToken = UserDefaults.standard.string(forKey: Keys.refreshToken) {
                    Log.info("🔍 UserDefaults中存在旧的刷新令牌: \(String(existingRefreshToken.prefix(10)))...")
                } else {
                    Log.warning("⚠️ UserDefaults中也没有刷新令牌")
                }
            }

            if let idToken = idTokenValue {
                UserDefaults.standard.set(idToken, forKey: Keys.idToken)
                Log.info("✅ 已保存ID令牌")
                shouldSynchronize = true
            }

            if let userId = userIdValue {
                UserDefaults.standard.set(userId, forKey: Keys.userId)
                Log.info("✅ 已保存用户ID: \(userId)")
                shouldSynchronize = true
            }

            if let logtoId = logtoIdValue {
                UserDefaults.standard.set(logtoId, forKey: Keys.logtoId)
                Log.info("✅ 已保存Logto ID: \(logtoId)")
                shouldSynchronize = true
            }

            // 确保更改立即写入
            if shouldSynchronize {
                UserDefaults.standard.synchronize()
                Log.info("💾 UserDefaults已同步")
            }
        }

        // 验证保存结果
        verifyStoredTokens()
        
        // 打印保存结果
        printUserData()
    }
    
    /// 验证存储的令牌状态
    private func verifyStoredTokens() {
        let storedAccessToken = getString(key: Keys.accessToken)
        let storedRefreshToken = getString(key: Keys.refreshToken)
        
        Log.info("🔍 验证存储结果:")
        if let accessToken = storedAccessToken {
            Log.info("✅ 访问令牌已正确存储: \(String(accessToken.prefix(10)))...")
        } else {
            Log.error("❌ 访问令牌未正确存储")
        }
        
        if let refreshToken = storedRefreshToken {
            Log.info("✅ 刷新令牌已正确存储: \(String(refreshToken.prefix(10)))...")
        } else {
            Log.error("❌ 刷新令牌未正确存储")
        }
    }

    /// 打印当前用户数据 - 线程安全
    func printUserData() {
        let userData: [String: String?] = lock.withLock {
            return [
                "access_token": UserDefaults.standard.string(forKey: Keys.accessToken),
                "refresh_token": UserDefaults.standard.string(forKey: Keys.refreshToken),
                "id_token": UserDefaults.standard.string(forKey: Keys.idToken),
                "user_id": UserDefaults.standard.string(forKey: Keys.userId),
                "logto_id": UserDefaults.standard.string(forKey: Keys.logtoId)
            ]
        }

        do {
            try JSONSerialization.data(withJSONObject: userData, options: .prettyPrinted)
            // if let jsonString = String(data: jsonData, encoding: .utf8) {
            //     Log.info("🧑‍💻 当前用户数据: \(jsonString)")
            // }
        } catch {
            Log.error("无法序列化用户数据: \(error.localizedDescription)")
        }
    }
}
