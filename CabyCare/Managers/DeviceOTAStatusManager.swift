import Foundation
import SwiftUI

/// 设备OTA状态管理器
@MainActor
class DeviceOTAStatusManager: ObservableObject {
    static let shared = DeviceOTAStatusManager()
    
    @Published private(set) var otaStatuses: [String: DeviceOTAStatusResponse] = [:]
    @Published private(set) var isLoading: Set<String> = []
    @Published private(set) var errors: [String: Error] = [:]
    
    private var refreshTimers: [String: Timer] = [:]
    private let refreshInterval: TimeInterval = 30 // 30秒刷新一次
    
    private init() {}
    
    // MARK: - 公共方法
    
    /// 获取指定设备的OTA状态
    func getOTAStatus(for deviceId: String) -> DeviceOTAStatusResponse? {
        return otaStatuses[deviceId]
    }
    
    /// 刷新单个设备的OTA状态
    func refreshOTAStatus(for deviceId: String) async {
        guard !isLoading.contains(deviceId) else { return }
        
        isLoading.insert(deviceId)
        errors.removeValue(forKey: deviceId)
        
        do {
            let status = try await fetchOTAStatus(deviceId: deviceId)
            otaStatuses[deviceId] = status
            
            // 如果状态是updating，开始定时刷新
            if status.status == .updating {
                startAutoRefresh(for: deviceId)
            } else {
                stopAutoRefresh(for: deviceId)
            }
            
        } catch {
            Log.error("❌ 获取设备 \(deviceId) OTA状态失败: \(error.localizedDescription)")
            errors[deviceId] = error
        }
        
        isLoading.remove(deviceId)
    }
    
    /// 批量刷新多个设备的OTA状态
    func refreshOTAStatuses(for deviceIds: [String]) async {
        await withTaskGroup(of: Void.self) { group in
            for deviceId in deviceIds {
                group.addTask {
                    await self.refreshOTAStatus(for: deviceId)
                }
            }
        }
    }
    
    /// 开始监控设备OTA状态（当设备列表改变时调用）
    func startMonitoring(deviceIds: [String]) {
        // 停止不需要的监控
        let currentDeviceIds = Set(deviceIds)
        let monitoringDeviceIds = Set(refreshTimers.keys)
        
        for deviceId in monitoringDeviceIds.subtracting(currentDeviceIds) {
            stopAutoRefresh(for: deviceId)
        }
        
        // 为新设备开始监控
        Task {
            await refreshOTAStatuses(for: deviceIds)
        }
    }
    
    /// 停止所有监控
    func stopAllMonitoring() {
        for deviceId in refreshTimers.keys {
            stopAutoRefresh(for: deviceId)
        }
    }
    
    // MARK: - 私有方法
    
    /// 从API获取OTA状态
    private func fetchOTAStatus(deviceId: String) async throws -> DeviceOTAStatusResponse {
        // 构建API URL
        let urlString = "\(Configuration.API.cabyBaseUrl)/api/devices/\(deviceId)/ota-status"
        guard let url = URL(string: urlString) else {
            throw OTAStatusError(message: "无效的URL")
        }
        
        // 🔄 使用NetworkManager的静默重试机制，避免绕过令牌刷新
        Log.info("📡 使用NetworkManager获取设备 \(deviceId) OTA状态")
        
        do {
            // 🔄 使用NetworkManager的request方法，享受静默重试3次机制
            let otaStatus: DeviceOTAStatusResponse = try await NetworkManager.shared.request(url, method: .get, contentType: .json)
            
            Log.info("✅ 成功获取设备 \(deviceId) OTA状态: \(otaStatus.status.rawValue)")
            return otaStatus
            
        } catch let error as NetworkError {
            // 🔍 处理不同类型的网络错误
            switch error {
            case .serverError(403):
                throw OTAStatusError(message: "无权限访问此设备")
            case .serverError(404):
                throw OTAStatusError(message: "设备不存在")
            case .unauthorized:
                Log.warning("⚠️ 设备OTA状态获取授权失败，NetworkManager已完成静默重试")
                throw OTAStatusError(message: "认证失败，请重新登录")
            case .noData:
                Log.info("ℹ️ 设备OTA状态暂无数据")
                throw OTAStatusError(message: "设备OTA状态暂无数据")
            case .timeoutError:
                throw OTAStatusError(message: "网络超时，请稍后重试")
            case .connectivityError:
                throw OTAStatusError(message: "网络连接异常，请检查网络")
            case .serverError(let code):
                throw OTAStatusError(message: "获取OTA状态失败 (状态码: \(code))")
            default:
                throw OTAStatusError(message: "获取OTA状态失败: \(error.localizedDescription)")
            }
        } catch {
            Log.error("❌ 获取设备OTA状态失败: \(error.localizedDescription)")
            throw OTAStatusError(message: "获取OTA状态失败: \(error.localizedDescription)")
        }
    }
    
    /// 开始自动刷新
    private func startAutoRefresh(for deviceId: String) {
        // 如果已经在刷新，先停止
        stopAutoRefresh(for: deviceId)
        
        Log.info("🔄 开始自动刷新设备 \(deviceId) OTA状态")
        
        let timer = Timer.scheduledTimer(withTimeInterval: refreshInterval, repeats: true) { [weak self] _ in
            Task {
                await self?.refreshOTAStatus(for: deviceId)
            }
        }
        
        refreshTimers[deviceId] = timer
    }
    
    /// 停止自动刷新
    private func stopAutoRefresh(for deviceId: String) {
        if let timer = refreshTimers[deviceId] {
            timer.invalidate()
            refreshTimers.removeValue(forKey: deviceId)
            Log.info("⏹️ 停止自动刷新设备 \(deviceId) OTA状态")
        }
    }
}

// MARK: - 便利扩展

extension DeviceOTAStatusManager {
    /// 检查设备是否正在进行OTA升级
    func isDeviceUpdating(_ deviceId: String) -> Bool {
        return otaStatuses[deviceId]?.status == .updating
    }
    
    /// 获取所有正在升级的设备数量
    var updatingDevicesCount: Int {
        return otaStatuses.values.filter { $0.status == .updating }.count
    }
    
    /// 获取设备OTA状态的显示颜色
    func statusColor(for deviceId: String, colorScheme: ColorScheme) -> Color {
        guard let status = otaStatuses[deviceId]?.status else {
            return AppTheme.secondaryTextColor(for: colorScheme)
        }
        
        switch status {
        case .idle:
            return AppTheme.secondaryTextColor(for: colorScheme)
        case .updating:
            return .blue
        case .failed:
            return .red
        case .completed:
            return .green
        }
    }
} 