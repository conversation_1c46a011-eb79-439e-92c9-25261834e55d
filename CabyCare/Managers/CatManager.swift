import Foundation
import UIKit

@MainActor
class CatManager: ObservableObject {
    static let shared = CatManager()
    
    @Published var cats: [CatProfile] = []
    @Published var hiddenCats: [CatProfile] = []
    @Published var isLoading = false
    @Published var error: String?
    
    // Add task management for preventing concurrent requests
    private var fetchTask: Task<Void, Never>?
    private var lastRefreshTime: Date?
    private let refreshCooldown: TimeInterval = 1.0 // 1秒防抖间隔
    
    private let cacheKey = "cached_cats"
    private let hiddenCacheKey = "cached_hidden_cats"
    private let cacheExpiryKey = "cats_cache_expiry"
    private let hiddenCacheExpiryKey = "hidden_cats_cache_expiry"
    private let cacheExpiryInterval: TimeInterval = 300 // 5 minutes
    
    private init() {
        loadCachedCats()
    }
    
    // MARK: - Public Methods
    
    func refreshAllCats(forceRefresh: Bool = false) async {
        // 防抖逻辑：如果距离上次刷新不到1秒且不是强制刷新，则跳过
        if !forceRefresh,
           let lastRefresh = lastRefreshTime,
           Date().timeIntervalSince(lastRefresh) < refreshCooldown {
            Log.debug("🐱 防抖：距离上次刷新不足1秒，跳过本次请求")
            return
        }
        
        // 如果当前正在加载且不是强制刷新，等待当前任务完成
        if !forceRefresh && isLoading {
            Log.debug("🐱 当前正在加载猫咪数据，等待完成...")
            await fetchTask?.value
            return
        }
        
        // 只有在强制刷新时才取消之前的任务
        if forceRefresh {
            fetchTask?.cancel()
        }
        
        // 避免重复启动任务
        if fetchTask != nil && !forceRefresh {
            Log.debug("🐱 已有任务在执行中，等待完成...")
            await fetchTask?.value
            return
        }
        
        fetchTask = Task { @MainActor in
            // 添加取消检查
            guard !Task.isCancelled else { 
                Log.debug("🐱 刷新任务被取消")
                return 
            }
            
            isLoading = true
            error = nil
            lastRefreshTime = Date()
            
            Log.debug("🐱 开始刷新所有猫咪数据（强制刷新: \(forceRefresh)）")
            
            // Fetch both lists sequentially to avoid conflicts
            await fetchCats(forceRefresh: forceRefresh)
            
            // 检查是否被取消
            guard !Task.isCancelled else { 
                Log.debug("🐱 获取正常猫咪后任务被取消")
                isLoading = false
                return 
            }
            
            await fetchHiddenCats(forceRefresh: forceRefresh)
            
            // 检查是否被取消
            guard !Task.isCancelled else { 
                Log.debug("🐱 获取隐藏猫咪后任务被取消")
                isLoading = false
                return 
            }
            
            isLoading = false
            Log.debug("🐱 所有猫咪数据刷新完成")
        }
        
        await fetchTask?.value
        fetchTask = nil
    }
    
    func fetchCats(forceRefresh: Bool = false) async {
        // Check cache first if not forcing refresh
        if !forceRefresh && isCacheValid() {
            Log.debug("🐱 使用缓存的猫咪数据")
            return
        }
        
        // 检查任务是否被取消
        guard !Task.isCancelled else { 
            Log.debug("🐱 获取猫咪任务被取消")
            return 
        }
        
        do {
            guard let url = URL(string: Configuration.API.Cat.listPath) else {
                throw NetworkError.invalidURL
            }
            
            Log.debug("🐱 正在获取猫咪列表...")
            
            // 服务器直接返回数组，不是包装的响应格式
            let catsData: CatsDirectResponse = try await NetworkManager.shared.request(url, method: .get)
            
            // 再次检查任务是否被取消
            guard !Task.isCancelled else { 
                Log.debug("🐱 获取猫咪数据后任务被取消")
                return 
            }
            
            let catProfiles = catsData.map { $0.toCatProfile() }
            
            self.cats = catProfiles
            self.cacheCats(catProfiles)
            Log.debug("🐱 成功获取 \(catProfiles.count) 只猫咪")
            
        } catch {
            // 检查是否是请求被取消的错误
            if let networkError = error as? NetworkError, networkError == .requestCancelled {
                Log.debug("🐱 猫咪列表网络请求被取消，这是正常情况")
                return
            }
            
            self.error = error.localizedDescription
            Log.error("❌ 获取猫咪列表失败: \(error.localizedDescription)")
        }
    }
    
    func createCat(name: String, gender: CatProfileCreation.Gender, neuteredStatus: CatProfileCreation.NeuteredStatus, weight: Double?, birthDate: Date?, photos: [UIImage], avatar: UIImage? = nil) async throws -> Bool {
        isLoading = true
        error = nil
        
        do {
            guard let url = URL(string: Configuration.API.Cat.createPath) else {
                throw NetworkError.invalidURL
            }
            
            // Prepare request data
            let genderCode = calculateGenderCode(gender: gender, neuteredStatus: neuteredStatus)
            
            // Format birth date as yyyy-MM-dd for API consistency
            var birthDateString: String?
            if let birthDate = birthDate {
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM-dd"
                birthDateString = formatter.string(from: birthDate)
            }
            
            // Process avatar image
            var avatarBase64: String?
            if let avatar = avatar {
                avatarBase64 = processAvatarImage(avatar)
                Log.debug("🐱 处理头像图片成功")
            }
            
            // Process photos: crop, compress and convert to base64 (for AI analysis)
            let photosBase64 = processPhotosForAvatar(photos)
            
            let requestData = CreateCatRequest(
                name: name,
                gender: genderCode,
                weight: weight,
                birthDate: birthDateString,
                photosBase64: photosBase64.isEmpty ? nil : photosBase64,
                avatarBase64: avatarBase64
            )
            
            Log.debug("🐱 正在创建猫咪档案: \(name), 包含 \(photosBase64.count) 张照片")
            let jsonData = try JSONEncoder().encode(requestData)
            
            // 使用创建猫咪专用的响应格式
            let response: CreateCatAPIResponse = try await NetworkManager.shared.request(
                url,
                method: .post,
                body: jsonData,
                contentType: .json
            )
            
            // Check if the API call was successful
            guard response.status == "success" else {
                Log.error("❌ 创建猫咪API返回错误: status=\(response.status), message=\(response.message)")
                throw NetworkError.serverError(500) // Use a generic error code
            }
            
            Log.debug("🐱 猫咪档案创建成功: \(response.data.name)")
            
            // Refresh both cats lists
            await refreshAllCats(forceRefresh: true)
            
            // 清除URL缓存以确保获取最新头像
            URLCache.shared.removeAllCachedResponses()
            
            // 触发头像刷新通知
            NotificationCenter.default.post(name: .avatarUpdated, object: nil)
            
            await MainActor.run {
                self.isLoading = false
            }
            
            return true
            
        } catch {
            await MainActor.run {
                self.error = error.localizedDescription
                self.isLoading = false
                Log.error("❌ 创建猫咪档案失败: \(error.localizedDescription)")
            }
            throw error
        }
    }
    
    func updateCat(catId: String, name: String, gender: CatProfileCreation.Gender, neuteredStatus: CatProfileCreation.NeuteredStatus, weight: Double?, birthDate: Date?, photos: [UIImage], avatar: UIImage? = nil) async throws -> Bool {
        isLoading = true
        error = nil
        
        do {
            guard let url = URL(string: "\(Configuration.API.Cat.basePath)/\(catId)") else {
                throw NetworkError.invalidURL
            }
            
            // Prepare request data (same structure as create but different endpoint)
            let genderCode = calculateGenderCode(gender: gender, neuteredStatus: neuteredStatus)
            
            // Format birth date as yyyy-MM-dd for API consistency
            var birthDateString: String?
            if let birthDate = birthDate {
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM-dd"
                birthDateString = formatter.string(from: birthDate)
            }
            
            // Process avatar image
            var avatarBase64: String?
            if let avatar = avatar {
                avatarBase64 = processAvatarImage(avatar)
                Log.debug("🐱 处理头像图片成功")
            }
            
            // Process photos: crop, compress and convert to base64 (for AI analysis)
            let photosBase64 = processPhotosForAvatar(photos)
            
            let requestData = CreateCatRequest(
                name: name,
                gender: genderCode,
                weight: weight,
                birthDate: birthDateString,
                photosBase64: photosBase64.isEmpty ? nil : photosBase64,
                avatarBase64: avatarBase64
            )
            
            Log.debug("🐱 正在更新猫咪档案: \(name) (ID: \(catId)), 包含 \(photosBase64.count) 张照片")
            Log.debug("🐱 请求数据: name=\(name), gender=\(genderCode), weight=\(weight?.description ?? "nil"), birthDate=\(birthDateString ?? "nil")")
            let jsonData = try JSONEncoder().encode(requestData)
            
            // 使用PUT方法更新猫咪档案（使用带令牌刷新的方法）
            let response: UpdateCatAPIResponse = try await NetworkManager.shared.request(
                url,
                method: .put,
                body: jsonData,
                contentType: .json
            )
            
            Log.debug("🐱 猫咪档案更新成功: \(response.name)")
            
            // Refresh both cats lists
            await refreshAllCats(forceRefresh: true)
            
            // 清除URL缓存以确保获取最新头像
            URLCache.shared.removeAllCachedResponses()
            
            // 触发头像刷新通知
            NotificationCenter.default.post(name: .avatarUpdated, object: nil)
            
            await MainActor.run {
                self.isLoading = false
            }
            
            return true
            
        } catch {
            await MainActor.run {
                self.error = error.localizedDescription
                self.isLoading = false
                Log.error("❌ 更新猫咪档案失败: \(error.localizedDescription)")
            }
            throw error
        }
    }
    
    // MARK: - Cat Management Methods
    
    func hideCat(catId: String) async throws {
        isLoading = true
        error = nil
        
        do {
            guard let url = URL(string: "\(Configuration.API.Cat.basePath)/\(catId)/hide") else {
                throw NetworkError.invalidURL
            }
            
            Log.debug("🐱 正在隐藏猫咪: \(catId)")
            
            let _: CatActionResponse = try await NetworkManager.shared.request(
                url,
                method: .put,
                body: nil,
                contentType: .json
            )
            
            // Refresh both lists
            await refreshAllCats(forceRefresh: true)
            
            await MainActor.run {
                self.isLoading = false
                Log.debug("🐱 猫咪隐藏成功: \(catId)")
            }
            
        } catch {
            await MainActor.run {
                self.error = error.localizedDescription
                self.isLoading = false
                Log.error("❌ 隐藏猫咪失败: \(error.localizedDescription)")
            }
            throw error
        }
    }
    
    func deleteCat(catId: String) async throws {
        isLoading = true
        error = nil
        
        do {
            guard let url = URL(string: "\(Configuration.API.Cat.basePath)/\(catId)") else {
                throw NetworkError.invalidURL
            }
            
            Log.debug("🐱 正在删除猫咪: \(catId)")
            
            let _: CatActionResponse = try await NetworkManager.shared.request(
                url,
                method: .delete,
                body: nil,
                contentType: .json
            )
            
            // Refresh both lists
            await refreshAllCats(forceRefresh: true)
            
            await MainActor.run {
                self.isLoading = false
                Log.debug("🐱 猫咪删除成功: \(catId)")
            }
            
        } catch {
            await MainActor.run {
                self.error = error.localizedDescription
                self.isLoading = false
                Log.error("❌ 删除猫咪失败: \(error.localizedDescription)")
            }
            throw error
        }
    }
    
    func restoreCat(catId: String) async throws {
        isLoading = true
        error = nil
        
        do {
            guard let url = URL(string: "\(Configuration.API.Cat.basePath)/\(catId)/restore") else {
                throw NetworkError.invalidURL
            }
            
            Log.debug("🐱 正在恢复猫咪: \(catId)")
            
            let _: CatActionResponse = try await NetworkManager.shared.request(
                url,
                method: .put,
                body: nil,
                contentType: .json
            )
            
            // Refresh both lists
            await refreshAllCats(forceRefresh: true)
            
            await MainActor.run {
                self.isLoading = false
                Log.debug("🐱 猫咪恢复成功: \(catId)")
            }
            
        } catch {
            await MainActor.run {
                self.error = error.localizedDescription
                self.isLoading = false
                Log.error("❌ 恢复猫咪失败: \(error.localizedDescription)")
            }
            throw error
        }
    }
    
    func fetchHiddenCats(forceRefresh: Bool = false) async {
        // Check cache first if not forcing refresh
        if !forceRefresh && isHiddenCacheValid() {
            Log.debug("🐱 使用缓存的隐藏猫咪数据")
            return
        }
        
        // 检查任务是否被取消
        guard !Task.isCancelled else { 
            Log.debug("🐱 获取隐藏猫咪任务被取消")
            return 
        }
        
        do {
            guard let url = URL(string: "\(Configuration.API.Cat.basePath)/hidden") else {
                throw NetworkError.invalidURL
            }
            
            Log.debug("🐱 正在获取隐藏猫咪列表...")
            
            let catsData: CatsDirectResponse = try await NetworkManager.shared.request(url)
            
            // 再次检查任务是否被取消
            guard !Task.isCancelled else { 
                Log.debug("🐱 获取隐藏猫咪数据后任务被取消")
                return 
            }
            
            let catProfiles = catsData.map { $0.toCatProfile() }
            
            self.hiddenCats = catProfiles
            self.cacheHiddenCats(catProfiles)
            Log.debug("🐱 成功获取 \(catProfiles.count) 只隐藏猫咪")
            
        } catch {
            // 检查是否是请求被取消的错误
            if let networkError = error as? NetworkError, networkError == .requestCancelled {
                Log.debug("🐱 隐藏猫咪列表网络请求被取消，这是正常情况")
                return
            }
            
            // Handle noData error specially - it means no hidden cats, which is normal
            if let networkError = error as? NetworkError, networkError == .noData {
                Log.debug("🐱 没有隐藏的猫咪，这是正常情况")
                self.hiddenCats = []
                self.cacheHiddenCats([])
            } else {
                self.error = error.localizedDescription
                Log.error("❌ 获取隐藏猫咪列表失败: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func calculateGenderCode(gender: CatProfileCreation.Gender, neuteredStatus: CatProfileCreation.NeuteredStatus) -> Int8 {
        switch (gender, neuteredStatus) {
        case (.male, .unknown): return 1
        case (.male, .notNeutered): return 11
        case (.male, .neutered): return 10
        case (.female, .unknown): return -1
        case (.female, .notNeutered): return -11
        case (.female, .neutered): return -10
        case (.unknown, _): return 0
        }
    }
    
    private func loadCachedCats() {
        // Load normal cats cache
        if let data = UserDefaults.standard.data(forKey: cacheKey),
           let cachedCats = try? JSONDecoder().decode([CatProfile].self, from: data) {
            self.cats = cachedCats
            Log.debug("🐱 加载了 \(cachedCats.count) 只缓存的猫咪")
        }
        
        // Load hidden cats cache
        if let data = UserDefaults.standard.data(forKey: hiddenCacheKey),
           let cachedHiddenCats = try? JSONDecoder().decode([CatProfile].self, from: data) {
            self.hiddenCats = cachedHiddenCats
            Log.debug("🐱 加载了 \(cachedHiddenCats.count) 只缓存的隐藏猫咪")
        }
    }
    
    private func cacheCats(_ cats: [CatProfile]) {
        if let data = try? JSONEncoder().encode(cats) {
            UserDefaults.standard.set(data, forKey: cacheKey)
            UserDefaults.standard.set(Date().timeIntervalSince1970, forKey: cacheExpiryKey)
            Log.debug("🐱 已缓存 \(cats.count) 只猫咪")
        }
    }
    
    private func cacheHiddenCats(_ cats: [CatProfile]) {
        if let data = try? JSONEncoder().encode(cats) {
            UserDefaults.standard.set(data, forKey: hiddenCacheKey)
            UserDefaults.standard.set(Date().timeIntervalSince1970, forKey: hiddenCacheExpiryKey)
            Log.debug("🐱 已缓存 \(cats.count) 只隐藏猫咪")
        }
    }
    
    private func isCacheValid() -> Bool {
        let lastCacheTime = UserDefaults.standard.double(forKey: cacheExpiryKey)
        let currentTime = Date().timeIntervalSince1970
        return (currentTime - lastCacheTime) < cacheExpiryInterval
    }
    
    private func isHiddenCacheValid() -> Bool {
        let lastCacheTime = UserDefaults.standard.double(forKey: hiddenCacheExpiryKey)
        let currentTime = Date().timeIntervalSince1970
        return (currentTime - lastCacheTime) < cacheExpiryInterval
    }
    
    // MARK: - Cache Management
    
    /// 清除所有猫咪相关的缓存
    @MainActor
    func clearCache() {
        Log.debug("🧹 清除CatManager缓存")
        
        // 清除内存中的数据
        cats = []
        hiddenCats = []
        error = nil
        
        // 清除UserDefaults中的缓存
        UserDefaults.standard.removeObject(forKey: cacheKey)
        UserDefaults.standard.removeObject(forKey: hiddenCacheKey)
        UserDefaults.standard.removeObject(forKey: cacheExpiryKey)
        UserDefaults.standard.removeObject(forKey: hiddenCacheExpiryKey)
        
        // 重置任务状态
        fetchTask?.cancel()
        fetchTask = nil
        lastRefreshTime = nil
        
        Log.debug("✅ CatManager缓存清除完成")
    }
    
    // MARK: - Photo Processing Methods
    
    /// 处理单张头像图片：裁剪、压缩并转换为base64
    /// - Parameter image: 输入的UIImage
    /// - Returns: base64编码的字符串，如果处理失败则返回nil
    private func processAvatarImage(_ image: UIImage) -> String? {
        // 1. 裁剪为正方形
        let croppedImage = cropToSquare(image: image)
        
        // 2. 调整为头像大小（512x512，与服务器端保持一致）
        let resizedImage = resizeImage(image: croppedImage, targetSize: CGSize(width: 512, height: 512))
        
        // 3. 压缩并转换为base64（使用较高质量，因为这是主要头像）
        let base64String = imageToBase64(image: resizedImage, compressionQuality: 0.9)
        
        if let base64String = base64String {
            Log.debug("🐱 头像处理成功，base64长度: \(base64String.count)")
        } else {
            Log.error("❌ 头像处理失败")
        }
        
        return base64String
    }
    
    /// 处理照片为头像：裁剪、压缩并转换为base64（用于AI分析的多张照片）
    /// - Parameter photos: 输入的UIImage数组
    /// - Returns: base64编码的字符串数组
    private func processPhotosForAvatar(_ photos: [UIImage]) -> [String] {
        var processedPhotos: [String] = []
        
        for photo in photos {
            // 1. 裁剪为正方形
            let croppedImage = cropToSquare(image: photo)
            
            // 2. 调整为头像大小（200x200）
            let resizedImage = resizeImage(image: croppedImage, targetSize: CGSize(width: 200, height: 200))
            
            // 3. 压缩并转换为base64
            if let base64String = imageToBase64(image: resizedImage, compressionQuality: 0.8) {
                processedPhotos.append(base64String)
            }
        }
        
        Log.debug("🐱 处理了 \(photos.count) 张照片，成功转换 \(processedPhotos.count) 张")
        return processedPhotos
    }
    
    /// 将图片裁剪为正方形（居中裁剪）
    /// - Parameter image: 输入图片
    /// - Returns: 裁剪后的正方形图片
    private func cropToSquare(image: UIImage) -> UIImage {
        let originalSize = image.size
        let sideLength = min(originalSize.width, originalSize.height)
        
        let x = (originalSize.width - sideLength) / 2
        let y = (originalSize.height - sideLength) / 2
        let cropRect = CGRect(x: x, y: y, width: sideLength, height: sideLength)
        
        guard let cgImage = image.cgImage?.cropping(to: cropRect) else {
            return image
        }
        
        return UIImage(cgImage: cgImage, scale: image.scale, orientation: image.imageOrientation)
    }
    
    /// 调整图片尺寸
    /// - Parameters:
    ///   - image: 输入图片
    ///   - targetSize: 目标尺寸
    /// - Returns: 调整后的图片
    private func resizeImage(image: UIImage, targetSize: CGSize) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: targetSize)
        let resizedImage = renderer.image { _ in
            image.draw(in: CGRect(origin: .zero, size: targetSize))
        }
        return resizedImage
    }
    
    /// 将图片转换为base64字符串
    /// - Parameters:
    ///   - image: 输入图片
    ///   - compressionQuality: JPEG压缩质量 (0.0-1.0)
    /// - Returns: base64编码的字符串，如果转换失败则返回nil
    private func imageToBase64(image: UIImage, compressionQuality: CGFloat = 0.8) -> String? {
        guard let imageData = image.jpegData(compressionQuality: compressionQuality) else {
            Log.error("❌ 无法将图片转换为JPEG数据")
            return nil
        }
        
        let base64String = imageData.base64EncodedString()
        Log.debug("🐱 图片转换为base64成功，大小: \(imageData.count) bytes")
        return base64String
    }
} 