import Foundation
import SwiftUI
import Combine

/// 令牌刷新管理器 - 专门负责处理应用生命周期中的令牌刷新逻辑
@MainActor
class TokenRefreshManager: ObservableObject {
    static let shared = TokenRefreshManager()
    
    @Published private var isAutoRefreshing = false
    
    private let authManager = AuthManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        Log.info("🔄 TokenRefreshManager 初始化")
        setupNotificationObservers()
    }
    
    // MARK: - 通知监听设置
    
    private func setupNotificationObservers() {
        // 监听授权失败通知（来自其他组件）
        NotificationCenter.default.publisher(for: .authorizationFailed)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.handleAuthorizationFailed()
            }
            .store(in: &cancellables)
        
        // 🔄 移除对authenticationExpired的监听，避免循环
        // 因为TokenRefreshManager现在会自己发送authenticationExpired通知
        // 监听这个通知会造成循环依赖
        Log.debug("🔄 TokenRefreshManager不再监听authenticationExpired通知，避免循环")
    }
    
    // MARK: - 应用启动时的令牌检查
    
    /// 应用启动时执行的令牌状态检查
    func performStartupTokenCheck() async {
        Log.info("🔍 应用启动 - 开始令牌状态检查")
        
        guard authManager.isAuthenticated else {
            Log.debug("🔍 用户未认证，跳过令牌检查")
            return
        }
        
        // 检查令牌是否即将过期或已过期
        if let expiresAt = authManager.accessTokenExpiresAt {
            let now = Date()
            let timeUntilExpiry = expiresAt.timeIntervalSince(now)
            
            if timeUntilExpiry <= 0 {
                Log.warning("⚠️ 应用启动 - 令牌已过期，开始静默刷新尝试")
                // 🔄 令牌已过期，先尝试静默刷新，不立即发送通知
                await silentlyRefreshToken()
                return
            } else if timeUntilExpiry <= 5 * 60 { // 5分钟内过期
                Log.info("🔄 应用启动 - 令牌即将过期，执行预防性刷新")
                await silentlyRefreshToken()
            } else {
                Log.info("🎫 应用启动 - 令牌状态良好，剩余 \(Int(timeUntilExpiry))秒")
            }
        } else {
            Log.warning("⚠️ 应用启动 - 无法获取令牌过期时间，执行令牌刷新")
            await silentlyRefreshToken()
        }
    }
    
    // MARK: - 应用前台切换时的令牌检查
    
    /// 应用进入前台时执行的令牌状态检查
    func handleAppBecomeActive() async {
        Log.info("🔍 应用进入前台 - 检查令牌状态")
        
        guard authManager.isAuthenticated else {
            Log.debug("🔍 用户未认证，跳过令牌检查")
            return
        }
        
        // 检查令牌是否需要刷新
        if let expiresAt = authManager.accessTokenExpiresAt {
            let now = Date()
            let timeUntilExpiry = expiresAt.timeIntervalSince(now)
            
            if timeUntilExpiry <= 0 {
                Log.warning("⚠️ 应用前台 - 令牌已过期，开始静默刷新尝试")
                // 🔄 令牌已过期，先尝试静默刷新，不立即发送通知
                await silentlyRefreshToken()
                return
            } else if timeUntilExpiry <= 10 * 60 { // 10分钟内过期
                Log.info("🔄 应用前台 - 令牌即将过期，执行刷新")
                await silentlyRefreshToken()
            } else {
                Log.debug("🎫 应用前台 - 令牌状态良好，剩余 \(Int(timeUntilExpiry))秒")
            }
        } else {
            Log.warning("⚠️ 应用前台 - 无法获取令牌过期时间")
            await silentlyRefreshToken()
        }
        
        // 同步通知设置
        await NotificationManager.shared.syncNotificationSettings()
    }
    
    // MARK: - 静默令牌刷新
    
    /// 静默刷新令牌，不显示错误给用户
    /// 这个方法现在依赖NetworkManager的静默重试机制
    private func silentlyRefreshToken() async {
        // 避免重复刷新
        guard !isAutoRefreshing else {
            Log.debug("🔄 已在进行静默刷新，跳过重复请求")
            return
        }
        
        isAutoRefreshing = true
        Log.info("🔄 TokenRefreshManager开始静默刷新令牌")
        
        defer {
            isAutoRefreshing = false
        }
        
        do {
            // 🔄 使用AuthManager.refreshToken()，它会通过NetworkManager处理重试逻辑
            // NetworkManager现在会自动进行3次静默重试，失败后才抛出错误
            let _ = try await authManager.refreshToken()
            Log.info("✅ TokenRefreshManager静默令牌刷新成功（NetworkManager已处理重试）")
            
            // 在令牌刷新成功后同步通知设置
            await NotificationManager.shared.syncNotificationSettings()
        } catch {
            Log.info("ℹ️ TokenRefreshManager静默令牌刷新失败，NetworkManager已完成3次重试")
            Log.debug("🔍 刷新失败原因: \(error.localizedDescription)")
            
            // 🔄 NetworkManager已经完成了3次静默重试，现在我们需要根据错误类型决定是否发送通知
            if let networkError = error as? NetworkError {
                switch networkError {
                case .serverError(let code) where [400, 401].contains(code):
                    // 🚫 不可恢复的认证错误（刷新令牌无效），发送认证过期通知
                    Log.warning("⚠️ 刷新令牌无效（HTTP \(code)），NetworkManager已重试3次，发送认证过期通知")
                    await sendAuthenticationExpiredNotification(reason: "刷新令牌无效，NetworkManager已重试3次")
                    
                case .unauthorized:
                    // 🚫 未授权错误，发送认证过期通知
                    Log.warning("⚠️ 认证失败，NetworkManager已重试3次，发送认证过期通知")
                    await sendAuthenticationExpiredNotification(reason: "认证失败，NetworkManager已重试3次")
                    
                case .tokenRefreshFailed:
                    // 🚫 令牌刷新机制失败，发送认证过期通知
                    Log.warning("⚠️ 令牌刷新机制失败，NetworkManager已重试3次，发送认证过期通知")
                    await sendAuthenticationExpiredNotification(reason: "令牌刷新机制失败，NetworkManager已重试3次")
                    
                case .connectivityError:
                    // 🌐 网络连接错误，不发送通知，用户稍后可能会恢复网络
                    Log.info("🌐 网络连接问题，NetworkManager已重试3次，但不发送认证过期通知")
                    Log.info("💡 用户网络恢复后可以继续使用应用")
                    
                case .timeoutError:
                    // ⏱️ 超时错误，不发送通知，可能是暂时的网络问题
                    Log.info("⏱️ 网络超时，NetworkManager已重试3次，但不发送认证过期通知")
                    Log.info("💡 这可能是暂时的网络问题")
                    
                case .serverError(let code):
                    // 🔍 其他服务器错误，根据错误码决定
                    if code >= 500 {
                        Log.info("🔧 服务器内部错误（HTTP \(code)），NetworkManager已重试3次，但不发送认证过期通知")
                        Log.info("💡 这是服务器问题，不是用户认证问题")
                    } else {
                        Log.warning("⚠️ 服务器错误（HTTP \(code)），NetworkManager已重试3次，发送认证过期通知")
                        await sendAuthenticationExpiredNotification(reason: "服务器错误（HTTP \(code)），NetworkManager已重试3次")
                    }
                    
                default:
                    Log.debug("🔍 其他网络错误，NetworkManager已重试3次，但不发送认证过期通知")
                    Log.debug("💡 错误类型: \(networkError)")
                }
            } else {
                // 🔍 非网络错误，可能是解析错误等，发送认证过期通知
                Log.warning("⚠️ 非网络错误，NetworkManager已处理，发送认证过期通知")
                await sendAuthenticationExpiredNotification(reason: "令牌刷新失败（非网络错误），NetworkManager已重试3次")
            }
        }
    }
    
    /// 发送认证过期通知（私有方法）
    private func sendAuthenticationExpiredNotification(reason: String) async {
        Log.warning("📢 TokenRefreshManager发送认证过期通知")
        Log.warning("📢 原因: \(reason)")
        
        // 🔍 提供详细的错误原因给用户
        let userFriendlyReason: String
        switch reason {
        case let r where r.contains("刷新令牌无效"):
            userFriendlyReason = "您的会话已过期，请重新登录"
        case let r where r.contains("认证失败"):
            userFriendlyReason = "登录状态异常，请重新登录"
        case let r where r.contains("令牌刷新机制失败"):
            userFriendlyReason = "系统认证异常，请重新登录"
        case let r where r.contains("服务器错误"):
            userFriendlyReason = "服务器异常，请重新登录"
        default:
            userFriendlyReason = "会话已过期，请重新登录"
        }
        
        Task { @MainActor in
            NotificationCenter.default.post(
                name: .authenticationExpired, 
                object: nil, 
                userInfo: [
                    "reason": reason,
                    "userFriendlyReason": userFriendlyReason,
                    "retryCount": 3, // 告诉UI已经重试了3次
                    "silentRetryCompleted": true, // 标记静默重试已完成
                    "source": "TokenRefreshManager" // 标记通知来源
                ]
            )
        }
    }
    
    // MARK: - 通知处理方法
    
    /// 处理授权失败的方法
    /// 现在只记录日志，不再自己实现重试机制
    private func handleAuthorizationFailed() {
        // 首先检查用户是否已认证，如果已登出则不处理
        guard authManager.isAuthenticated else {
            Log.debug("🚫 用户未认证状态，跳过授权失败处理")
            return
        }
        
        // 避免重复刷新
        guard !isAutoRefreshing else { 
            Log.debug("🔄 已在进行令牌刷新，跳过授权失败处理")
            return 
        }
        
        Log.info("🔄 TokenRefreshManager检测到授权失败通知")
        Log.info("ℹ️ NetworkManager会处理静默重试，TokenRefreshManager执行一次刷新尝试")
        
        // 🔄 执行一次静默刷新尝试，NetworkManager会处理重试
        Task {
            await silentlyRefreshToken()
        }
    }
    
    // MARK: - 公共接口
    
    /// 检查当前是否正在自动刷新令牌
    var isRefreshing: Bool {
        return isAutoRefreshing
    }
} 