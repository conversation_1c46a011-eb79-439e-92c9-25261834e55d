import Foundation
import os

/// 缓存管理器 - 线程安全的文件缓存管理
final class CacheManager: Sendable {
    static let shared = CacheManager()
    // 使用 nonisolated(unsafe) 标记 FileManager，因为我们通过队列保证线程安全
    private nonisolated(unsafe) let fileManager = FileManager.default
    private let maxCacheSize: UInt64 = 1024 * 1024 * 1024 // 1GB
    private let cacheQueue = DispatchQueue(label: "com.aby.cachemanager", qos: .utility)
    private let lock = OSAllocatedUnfairLock()
    
    private init() {}
    
    /// 获取缓存文件的URL - 线程安全
    func cacheURL(for identifier: String, fileExtension: String = "mp4") throws -> URL {
        return try lock.withLock {
            let cacheDir = try fileManager.url(
                for: .cachesDirectory,
                in: .userDomainMask,
                appropriateFor: nil,
                create: true
            )
            return cacheDir.appendingPathComponent("\(identifier).\(fileExtension)")
        }
    }
    
    /// 移动文件到缓存目录 - 线程安全
    func moveToCache(from tempURL: URL, to cacheURL: URL) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            cacheQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: NSError(domain: "CacheManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "CacheManager was deallocated"]))
                    return
                }
                
                do {
                    if self.fileManager.fileExists(atPath: cacheURL.path) {
                        try self.fileManager.removeItem(at: cacheURL)
                    }
                    try self.fileManager.moveItem(at: tempURL, to: cacheURL)
                    continuation.resume()
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    /// 同步版本的移动文件到缓存（向后兼容）
    func moveToCache(from tempURL: URL, to cacheURL: URL) throws {
        try lock.withLock {
            if fileManager.fileExists(atPath: cacheURL.path) {
                try fileManager.removeItem(at: cacheURL)
            }
            try fileManager.moveItem(at: tempURL, to: cacheURL)
        }
    }
    
    /// 检查并清理缓存 - 异步执行
    func cleanupIfNeeded() {
        cacheQueue.async { [weak self] in
            self?.performCleanup()
        }
    }
    
    /// 执行缓存清理操作
    private func performCleanup() {
        guard let cacheDir = try? fileManager.url(
            for: .cachesDirectory,
            in: .userDomainMask,
            appropriateFor: nil,
            create: false
        ) else { return }
        
        guard let files = try? fileManager.contentsOfDirectory(
            at: cacheDir,
            includingPropertiesForKeys: [.fileSizeKey, .creationDateKey]
        ) else { return }
        
        let sortedFiles = files.compactMap { url -> (URL, Date, UInt64)? in
            guard let resources = try? url.resourceValues(forKeys: [.fileSizeKey, .creationDateKey]),
                  let size = resources.fileSize,
                  let date = resources.creationDate
            else { return nil }
            return (url, date, UInt64(size))
        }.sorted { $0.1 < $1.1 }
        
        var totalSize: UInt64 = sortedFiles.reduce(0) { $0 + $1.2 }
        
        for (url, _, size) in sortedFiles where totalSize > maxCacheSize {
            do {
                try fileManager.removeItem(at: url)
                totalSize -= size
                Log.info("🗑️ 已删除缓存文件: \(url.lastPathComponent)")
            } catch {
                Log.error("❌ 删除缓存文件失败: \(error.localizedDescription)")
            }
        }
        
        if totalSize <= maxCacheSize {
            Log.info("✅ 缓存清理完成，当前大小: \(ByteCountFormatter.string(fromByteCount: Int64(totalSize), countStyle: .file))")
        }
    }
    
    /// 获取缓存总大小 - 异步
    func getCacheSize() async -> UInt64 {
        return await withCheckedContinuation { continuation in
            cacheQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(returning: 0)
                    return
                }
                
                let size = self.calculateCacheSize()
                continuation.resume(returning: size)
            }
        }
    }
    
    /// 计算缓存大小
    private func calculateCacheSize() -> UInt64 {
        guard let cacheDir = try? fileManager.url(
            for: .cachesDirectory,
            in: .userDomainMask,
            appropriateFor: nil,
            create: false
        ) else { return 0 }
        
        guard let files = try? fileManager.contentsOfDirectory(
            at: cacheDir,
            includingPropertiesForKeys: [.fileSizeKey]
        ) else { return 0 }
        
        return files.reduce(0) { total, url in
            guard let resources = try? url.resourceValues(forKeys: [.fileSizeKey]),
                  let size = resources.fileSize else { return total }
            return total + UInt64(size)
        }
    }
    
    /// 清空所有缓存 - 异步
    func clearAllCache() async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            cacheQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: NSError(domain: "CacheManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "CacheManager was deallocated"]))
                    return
                }
                
                do {
                    guard let cacheDir = try? self.fileManager.url(
                        for: .cachesDirectory,
                        in: .userDomainMask,
                        appropriateFor: nil,
                        create: false
                    ) else {
                        continuation.resume()
                        return
                    }
                    
                    let files = try self.fileManager.contentsOfDirectory(at: cacheDir, includingPropertiesForKeys: nil)
                    for file in files {
                        try self.fileManager.removeItem(at: file)
                    }
                    
                    Log.info("✅ 已清空所有缓存文件")
                    continuation.resume()
                } catch {
                    Log.error("❌ 清空缓存失败: \(error.localizedDescription)")
                    continuation.resume(throwing: error)
                }
            }
        }
    }
} 