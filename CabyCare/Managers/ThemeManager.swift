import SwiftUI

struct AppTheme {
    static func primaryColor(for colorScheme: ColorScheme) -> Color {
        colorScheme == .dark ? .themeSecondary : .themePrimary
    }

    static func secondaryBackgroundColor(for colorScheme: ColorScheme) -> Color {
        colorScheme == .dark ? Color.gray.opacity(0.1) : Color(red: 0.96, green: 0.97, blue: 0.98)
    }

    static func textColor(for colorScheme: ColorScheme) -> Color {
        colorScheme == .dark ? .white : .black
    }

    static func secondaryTextColor(for colorScheme: ColorScheme) -> Color {
        colorScheme == .dark ? .gray : .gray
    }

    static func buttonColor(for colorScheme: ColorScheme) -> Color {
        colorScheme == .dark ? .themeSecondary : .themePrimary
    }

    static func buttonTextColor(for colorScheme: ColorScheme) -> Color {
        colorScheme == .dark ? .black : .white
    }

    static func backgroundColor(for colorScheme: ColorScheme) -> Color {
        colorScheme == .dark ? .black : .white
    }
    
    // MARK: - Card Styling Methods
    
    static func cardBackgroundColor(for colorScheme: ColorScheme) -> Color {
        colorScheme == .dark ? Color(.systemGray6) : Color(.systemBackground)
    }
    
    static func cardShadowColor(for colorScheme: ColorScheme) -> Color {
        colorScheme == .dark ? Color.black.opacity(0.3) : Color.black.opacity(0.1)
    }
}