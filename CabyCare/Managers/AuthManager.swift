import Foundation
import SwiftUI
import Combine
import Logto
import LogtoClient

// 在文件顶部添加通知名称
extension Notification.Name {
    static let authenticationStatusChanged = Notification.Name("AuthenticationStatusChanged")
    static let userLoggedOut = Notification.Name("UserLoggedOut")
}

@MainActor // 标记整个类在主线程运行
class AuthManager: ObservableObject {
    static let shared = AuthManager()

    @Published var isAuthenticated: Bool = false
    @Published var isLoading: Bool = false
    @Published var error: String?

    // 使用原生Logto管理器
    private let nativeLogtoManager = NativeLogtoManager.shared
    
    // 统一使用UserDefaultsManager进行存储
    private let storage = UserDefaultsManager.shared
    
    // 只保留基本的刷新状态跟踪，移除定时器相关代码
    private var refreshTask: Task<String, Error>? // 刷新任务追踪

    var accessToken: String? {
        get { 
            // 由于原生SDK的accessToken现在是异步的，我们优先使用本地存储的token
            // 在需要时会通过异步方法获取最新的token
            return storage.accessToken 
        }
        set { storage.accessToken = newValue }
    }

    var refreshToken: String? {
        get { storage.refreshToken }
        set { storage.refreshToken = newValue }
    }
    
    // 计算属性，获取访问令牌的过期时间
    var accessTokenExpiresAt: Date? {
        get {
            let timestamp = UserDefaultsManager.shared.getInt(key: "access_token_expires_at")
            return timestamp > 0 ? Date(timeIntervalSince1970: TimeInterval(timestamp)) : nil
        }
        set {
            if let date = newValue {
                UserDefaultsManager.shared.setInt(key: "access_token_expires_at", value: Int(date.timeIntervalSince1970))
            } else {
                UserDefaultsManager.shared.remove(key: "access_token_expires_at")
            }
        }
    }

    private init() {
        // 初始化时检查认证状态
        Task {
            await checkInitialAuthStatus()
        }
    }
    
    deinit {
        // 清理刷新任务
        refreshTask?.cancel()
        refreshTask = nil
    }
    
    private func setupObservers() {
        // 监听原生Logto管理器的加载状态
        nativeLogtoManager.$isLoading
            .receive(on: DispatchQueue.main)
            .assign(to: &$isLoading)
        
        // 监听原生Logto管理器的错误状态
        nativeLogtoManager.$errorMessage
            .receive(on: DispatchQueue.main)
            .assign(to: &$error)
    }

    func checkAuthentication() {
        Log.info("🔍 开始检查认证状态")
        
        // 检查原生Logto SDK的认证状态
        let isNativeAuthenticated = nativeLogtoManager.isAuthenticated
        
        // 如果原生SDK已认证，使用它的状态
        if isNativeAuthenticated {
            isAuthenticated = true
            Log.info("🔑 原生Logto SDK检测到用户已登录")
            logTokenStatus()
            storage.printUserData()
        } else if let token = accessToken, !token.isEmpty {
            // 检查本地存储的token有效性
            if isTokenValid() {
                isAuthenticated = true
                Log.info("🔑 发现有效的本地访问令牌，用户已登录")
                logTokenStatus()
                storage.printUserData()
            } else {
                Log.warning("⚠️ 本地访问令牌已过期或无效，需要重新登录")
                // 清除无效的认证数据
                clearInvalidTokens()
                isAuthenticated = false
            }
        } else {
            isAuthenticated = false
            Log.info("🔒 未找到访问令牌，用户未登录")
        }
    }
    
    /// 检查令牌是否有效
    private func isTokenValid() -> Bool {
        guard let token = accessToken, !token.isEmpty else {
            Log.debug("🔍 令牌检查: 无访问令牌")
            return false
        }
        
        // 检查令牌过期时间
        if let expiresAt = accessTokenExpiresAt {
            let now = Date()
            let timeUntilExpiry = expiresAt.timeIntervalSince(now)
            
            if timeUntilExpiry > 300 { // 还有5分钟以上才过期
                Log.debug("🔍 令牌检查: 令牌有效，还剩 \(Int(timeUntilExpiry))秒")
                return true
            } else if timeUntilExpiry > 0 {
                Log.warning("⚠️ 令牌检查: 令牌即将过期，还剩 \(Int(timeUntilExpiry))秒")
                // 令牌即将过期，但还有效，触发刷新
                Task {
                    await attemptTokenRefresh()
                }
                return true
            } else {
                Log.warning("⚠️ 令牌检查: 令牌已过期 \(Int(-timeUntilExpiry))秒")
                return false
            }
        } else {
            // 没有过期时间信息，尝试验证令牌
            Log.warning("⚠️ 令牌检查: 无过期时间信息，需要验证令牌有效性")
            // 对于没有过期时间的令牌，我们暂时认为可能有效，但会在实际使用时验证
            return true
        }
    }
    
    /// 获取令牌剩余时间（秒）
    func getTokenRemainingTime() -> TimeInterval? {
        guard let expiresAt = accessTokenExpiresAt else { return nil }
        return expiresAt.timeIntervalSince(Date())
    }
    
    /// 检查令牌是否需要刷新（剩余时间少于指定分钟数）
    func shouldRefreshToken(withinMinutes minutes: Int = 10) -> Bool {
        guard let remainingTime = getTokenRemainingTime() else { return false }
        return remainingTime <= TimeInterval(minutes * 60)
    }
    
    /// 获取令牌健康状态信息
    func getTokenHealthInfo() -> (isValid: Bool, remainingTime: TimeInterval?, status: String) {
        guard let token = accessToken, !token.isEmpty else {
            return (false, nil, "无访问令牌")
        }
        
        guard let expiresAt = accessTokenExpiresAt else {
            return (true, nil, "令牌有效，但无过期时间信息")
        }
        
        let remainingTime = expiresAt.timeIntervalSince(Date())
        
        if remainingTime <= 0 {
            return (false, remainingTime, "令牌已过期")
        } else if remainingTime <= 300 {
            return (true, remainingTime, "令牌即将过期")
        } else {
            return (true, remainingTime, "令牌健康")
        }
    }
    
    /// 清除无效的令牌数据
    private func clearInvalidTokens() {
        Log.info("🧹 清除无效的认证数据")
        accessToken = nil
        refreshToken = nil
        accessTokenExpiresAt = nil
        storage.clearAuthCredentials()
    }
    
    /// 尝试自动刷新令牌
    private func attemptTokenRefresh() async {
        Log.info("🔄 尝试自动刷新即将过期的令牌")
        
        do {
            _ = try await refreshToken()
            Log.info("✅ 令牌自动刷新成功")
        } catch {
            Log.warning("⚠️ 令牌自动刷新失败: \(error.localizedDescription)")
            // 不在这里触发重新登录，让实际API调用时处理
        }
    }

    private func logTokenStatus() {
        guard let token = accessToken else {
            Log.info("🎫 无访问令牌")
            return
        }
        
        let tokenPrefix = String(token.prefix(10))
        Log.info("🎫 访问令牌状态 - 前缀: \(tokenPrefix)...")
        
        if let expiresAt = accessTokenExpiresAt {
            let now = Date()
            let timeUntilExpiry = expiresAt.timeIntervalSince(now)
            
            if timeUntilExpiry > 0 {
                Log.info("🎫 令牌有效期: 还剩 \(Int(timeUntilExpiry))秒 (过期时间: \(DateFormatter.localizedString(from: expiresAt, dateStyle: .short, timeStyle: .medium)))")
            } else {
                Log.warning("⚠️ 令牌已过期 \(Int(-timeUntilExpiry))秒")
            }
        } else {
            Log.warning("⚠️ 无法获取令牌过期时间")
        }
        
        if let refreshToken = refreshToken {
            if refreshToken == "native_sdk_managed" {
                Log.info("🔄 刷新令牌状态 - 原生SDK自动管理")
            } else {
                let refreshTokenPrefix = String(refreshToken.prefix(10))
                Log.info("🔄 刷新令牌状态 - 前缀: \(refreshTokenPrefix)...")
            }
        } else {
            Log.warning("⚠️ 无刷新令牌")
        }
    }

    // 原生登录方法（支持自动切换到OIDC）
    func signInNative() async {
        Log.info("🎯 开始智能登录流程")
        Log.info("🎯 调试：AuthManager.signInNative() 被调用")
        isLoading = true
        error = nil
        
        do {
            Log.info("🎯 调试：准备调用 nativeLogtoManager.signIn()")
            try await nativeLogtoManager.signIn()
            Log.info("🎯 调试：nativeLogtoManager.signIn() 完成")
            
            // 登录成功后更新状态
            isAuthenticated = nativeLogtoManager.isAuthenticated || storage.accessToken != nil
            error = nil
            
            if isAuthenticated {
                Log.info("🎉 登录认证成功")
                Log.info("🎯 调试：认证状态检查 - nativeLogtoManager.isAuthenticated: \(nativeLogtoManager.isAuthenticated)")
                Log.info("🎯 调试：认证状态检查 - storage.accessToken存在: \(storage.accessToken != nil)")
                logTokenStatus()
                storage.printUserData()
                
                // 检查注册状态
                if !isRegistrationComplete {
                    Log.info("ℹ️ 检测到可能需要完成用户注册，系统已自动处理")
                    // 不设置error，因为系统已经自动处理了
                }
            } else {
                Log.warning("⚠️ 登录完成但认证状态异常")
                Log.warning("🎯 调试：nativeLogtoManager.isAuthenticated = \(nativeLogtoManager.isAuthenticated)")
                Log.warning("🎯 调试：storage.accessToken = \(storage.accessToken?.prefix(10) ?? "nil")")
            }
        } catch let logtoError as LogtoClientErrors.SignIn {
            isLoading = false
            Log.error("❌ 登录错误: \(logtoError)")
            Log.error("🎯 调试：LogtoClientErrors.SignIn 异常详情: \(logtoError)")
            
            // 参考官方Demo的错误处理方式
            if let error = logtoError.innerError as? LogtoErrors.Response,
               case let LogtoErrors.Response.withCode(
                   _,
                   _,
                   data
               ) = error, let data = data {
                
                let errorString = String(decoding: data, as: UTF8.self)
                Log.error("❌ 登录错误详情: \(errorString)")

                // 检查是否为用户取消操作
                if errorString.contains("authentication_failed") {
                    Log.info("ℹ️ 用户取消了登录操作")
                    // 静默处理取消操作
                    self.error = nil
                    self.isAuthenticated = false
                    return
                }

                // 其他错误显示给用户
                self.error = errorString
                self.isAuthenticated = false
            } else {
                // 如果无法解析具体错误，显示通用错误信息
                self.error = "登录失败: \(logtoError.localizedDescription)"
                self.isAuthenticated = false
            }
        } catch {
            Log.error("❌ 登录过程中发生未知错误: \(error)")
            Log.error("🎯 调试：未知错误详情: \(error)")
            self.error = error.localizedDescription
            self.isAuthenticated = false
            self.isLoading = false
        }
    }

    // 保持向后兼容的方法（现在内部使用原生登录）
    func getAuthorizationURL() -> URL {
        Log.info("🎯 调试：AuthManager.getAuthorizationURL() 被调用")
        // 这个方法现在主要用于向后兼容
        // 实际登录会使用原生SDK
        let state = UUID().uuidString
        storage.authState = state

        var components = URLComponents(string: Configuration.API.Logto.authPath)!
        components.queryItems = [
            URLQueryItem(name: "client_id", value: Configuration.API.Logto.clientId),
            URLQueryItem(name: "redirect_uri", value: Configuration.API.Logto.appCallback),
            URLQueryItem(name: "response_type", value: "code"),
            URLQueryItem(name: "scope", value: "openid profile email offline_access"),
            URLQueryItem(name: "state", value: state),
            URLQueryItem(name: "prompt", value: "consent")
        ]
        return components.url!
    }

    // 处理令牌响应的方法 - 支持两种来源的令牌数据
    private func handleTokenResponse(_ json: [String: Any]?) {
        Log.info("🎯 调试：AuthManager.handleTokenResponse() 被调用")
        guard let json = json else {
            Log.error("🎯 调试：handleTokenResponse 收到空的JSON数据")
            self.error = "Invalid response"
            return
        }

        Log.info("🎯 调试：handleTokenResponse 收到的数据: \(json)")

        // 使用统一的UserDefaultsManager保存令牌
        storage.saveAuthData(tokens: json)
        
        // 保存令牌过期时间
        if let expiresIn = json["expires_in"] as? TimeInterval {
            let expiresAt = Date().addingTimeInterval(expiresIn)
            accessTokenExpiresAt = expiresAt
            Log.info("🎫 保存令牌过期时间: \(DateFormatter.localizedString(from: expiresAt, dateStyle: .short, timeStyle: .medium))")
        }

        // 更新认证状态
        self.isAuthenticated = storage.accessToken != nil

        if isAuthenticated {
            Log.info("✅ 登录成功：用户ID \(storage.userId ?? "unknown")")
            logTokenStatus()
        } else {
            self.error = "访问令牌不存在"
        }
    }

    // 新增方法：处理WebView中显示的JSON响应（保持向后兼容）
    func handleWebViewJsonResponse(jsonString: String) {
        Log.info("🎯 调试：AuthManager.handleWebViewJsonResponse(jsonString) 被调用")
        if let data = jsonString.data(using: .utf8) {
            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                    handleTokenResponse(json)
                } else {
                    self.error = "无法解析JSON数据"
                }
            } catch {
                self.error = "JSON解析错误: \(error.localizedDescription)"
            }
        }
    }

    // 添加一个新方法用于解析fragment中的JSON（保持向后兼容）
    func handleWebViewJsonResponse(fragment: String) {
        Log.info("🎯 调试：AuthManager.handleWebViewJsonResponse(fragment) 被调用")
        var jsonString = fragment
        if jsonString.hasPrefix("{") == false {
            if let startIndex = fragment.firstIndex(of: "{") {
                jsonString = String(fragment[startIndex...])
            }
        }

        if jsonString.hasSuffix("}") == false {
            if let endIndex = jsonString.lastIndex(of: "}") {
                jsonString = String(jsonString[...endIndex])
            }
        }

        handleWebViewJsonResponse(jsonString: jsonString)
    }

    // 添加一个公共方法，可以从WebView直接调用（保持向后兼容）
    func processWebViewResponse(jsonString: String) {
        Log.info("🎯 调试：AuthManager.processWebViewResponse() 被调用")
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            if let data = jsonString.data(using: .utf8) {
                do {
                    if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                        DispatchQueue.main.async {
                            self?.handleTokenResponse(json)
                        }
                    }
                } catch {
                    DispatchQueue.main.async {
                        self?.error = "JSON解析错误: \(error.localizedDescription)"
                    }
                }
            }
        }
    }

    func logout() {
        Log.info("🚪 开始登出流程")
        
        Task {
            // 使用原生Logto管理器登出
            await nativeLogtoManager.signOut()
            
            // 清除令牌过期时间
            accessTokenExpiresAt = nil
            
            // 更新本地状态
            isAuthenticated = false
            
            Log.info("🚪 用户已登出，所有数据已清除")
        }
    }

    // 令牌刷新方法 - 使用NetworkManager的静默重试机制
    func refreshToken() async throws -> String {
        Log.info("🔄 开始刷新令牌流程 - 使用NetworkManager的静默重试机制")
        
        // 如果已经有刷新任务在进行，等待它完成
        if let existingTask = refreshTask {
            Log.debug("🔄 已有刷新任务在进行，等待完成...")
            do {
                let result = try await existingTask.value
                Log.debug("✅ 等待的刷新任务完成，获得新令牌")
                return result
            } catch {
                Log.error("❌ 等待的刷新任务失败: \(error.localizedDescription)")
                // 清除失败的任务
                refreshTask = nil
                throw error
            }
        }
        
        // 检查是否有刷新令牌
        guard let currentRefreshToken = availableRefreshToken else {
            Log.error("❌ 无可用的刷新令牌")
            
            // 检查是否是因为后端没有返回刷新令牌
            if let accessToken = accessToken, !accessToken.isEmpty {
                Log.warning("⚠️ 有访问令牌但无刷新令牌，这可能是后端配置问题")
                Log.info("💡 建议：检查后端Logto配置是否包含offline_access scope")
                
                // 清除认证状态，但不立即发送通知
                await MainActor.run {
                    self.clearInvalidTokens()
                    self.isAuthenticated = false
                }
                
                // 抛出错误，让NetworkManager的重试机制处理
                throw NetworkError.unauthorized
            }
            
            // 直接抛出错误，不发送通知，让NetworkManager处理
            throw NetworkError.unauthorized
        }
        
        // 创建新的刷新任务
        let task = Task<String, Error> {
            Log.debug("🔄 创建新的刷新任务，使用刷新令牌: \(String(currentRefreshToken.prefix(10)))...")
            
            do {
                // 🔄 使用NetworkManager的专门令牌刷新方法，享受静默重试3次机制
                guard let refreshUrl = URL(string: Configuration.API.Logto.refreshUrl) else {
                    Log.error("❌ 无效的刷新URL: \(Configuration.API.Logto.refreshUrl)")
                    throw NetworkError.invalidURL
                }
                
                // 构建请求体 - 根据后端handler.go的期望格式
                let requestBody = [
                    "refresh_token": currentRefreshToken
                ]
                
                let requestData = try JSONSerialization.data(withJSONObject: requestBody)
                
                Log.debug("📡 通过NetworkManager.refreshTokenRequest发送刷新请求（支持静默重试3次）")
                
                // ✅ 关键修改：使用NetworkManager的专门令牌刷新方法，享受静默重试3次机制
                let (data, httpResponse) = try await NetworkManager.shared.refreshTokenRequest(refreshUrl, body: requestData)
                
                Log.debug("📡 刷新响应状态码: \(httpResponse.statusCode)")
                
                // 解析响应（NetworkManager已经确保了200-299状态码）
                guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] else {
                    Log.error("❌ 无效的JSON响应")
                    throw NetworkError.decodingError(NSError(domain: "AuthManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid JSON response"]))
                }
                
                Log.info("✅ 令牌刷新响应: \(json)")
                
                // 提取新的访问令牌
                guard let newAccessToken = json["access_token"] as? String, !newAccessToken.isEmpty else {
                    Log.error("❌ 响应中缺少有效的访问令牌")
                    throw NetworkError.decodingError(NSError(domain: "AuthManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "Missing access token"]))
                }
                
                // 更新令牌信息
                await MainActor.run {
                    // 保存新的访问令牌
                    self.accessToken = newAccessToken
                    
                    // 更新刷新令牌（如果后端返回了新的）
                    if let newRefreshToken = json["refresh_token"] as? String, !newRefreshToken.isEmpty {
                        self.refreshToken = newRefreshToken
                        Log.info("🔄 已更新刷新令牌")
                    }
                    
                    // 计算并保存新的过期时间
                    if let expiresIn = json["expires_in"] as? TimeInterval {
                        let expiresAt = Date().addingTimeInterval(expiresIn)
                        self.accessTokenExpiresAt = expiresAt
                        Log.info("🕰️ 令牌过期时间已更新: \(DateFormatter.localizedString(from: expiresAt, dateStyle: .short, timeStyle: .medium))")
                    } else {
                        // 如果没有返回过期时间，默认设置为1小时
                        let expiresAt = Date().addingTimeInterval(3600)
                        self.accessTokenExpiresAt = expiresAt
                        Log.warning("⚠️ 响应中缺少expires_in，默认设置1小时过期")
                    }
                    
                    // 保存用户信息（如果返回了）
                    if let userId = json["user_id"] as? String {
                        storage.userId = userId
                    }
                    
                    if let logtoId = json["logto_id"] as? String {
                        storage.logtoId = logtoId
                    }
                    
                    // 更新认证状态
                    self.isAuthenticated = true
                }
                
                Log.info("✅ 令牌刷新成功（静默重试机制），新令牌前缀: \(String(newAccessToken.prefix(10)))...")
                
                // 通知应用令牌已刷新
                NotificationCenter.default.post(name: .authenticationStatusChanged, object: nil)
                logTokenStatus()
                
                return newAccessToken
                
            } catch let error as NetworkError {
                Log.error("❌ NetworkManager令牌刷新失败: \(error.localizedDescription)")
                
                // 对于401/400错误，清除无效令牌
                if case .serverError(let code) = error, [400, 401].contains(code) {
                    Log.warning("⚠️ 刷新令牌无效，清除认证状态")
                    await MainActor.run {
                        self.clearInvalidTokens()
                        self.isAuthenticated = false
                    }
                }
                
                // 重新抛出NetworkError，让上层处理
                throw error
                
            } catch {
                Log.error("❌ 令牌刷新未知错误: \(error.localizedDescription)")
                throw NetworkError.tokenRefreshFailed(error)
            }
        }
        
        // 保存任务引用
        refreshTask = task
        
        do {
            let result = try await task.value
            // 清除完成的任务
            refreshTask = nil
            return result
        } catch {
            // 清除失败的任务
            refreshTask = nil
            throw error
        }
    }

    // 新增方法：检查用户注册状态
    func checkUserRegistrationStatus() async -> Bool {
        guard let accessToken = storage.accessToken else {
            Log.warning("⚠️ 无访问令牌，无法检查用户注册状态")
            return false
        }
        
        guard let userInfoUrl = URL(string: "\(Configuration.API.cabyBaseUrl)/api/user/info") else {
            Log.error("❌ 无效的用户信息API URL")
            return false
        }
        
        do {
            var request = URLRequest(url: userInfoUrl)
            request.httpMethod = "GET"
            request.setValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse {
                if httpResponse.statusCode == 200 {
                    if let userInfo = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                       let userId = userInfo["user_id"] as? String,
                       !userId.isEmpty {
                        Log.info("✅ 用户已在后端注册，用户ID: \(userId)")
                        return true
                    }
                } else {
                    Log.warning("⚠️ 用户信息请求失败: HTTP \(httpResponse.statusCode)")
                }
            }
        } catch {
            Log.error("❌ 检查用户注册状态时发生错误: \(error.localizedDescription)")
        }
        
        return false
    }

    // 统一智能登录方法 - 推荐使用
    func signInSmart() async {
        Log.info("🧠 开始智能登录流程...")
        Log.info("💡 系统将自动选择最佳登录策略")
        
        // 直接使用原生登录，它现在会自动处理用户注册
        await signInNative()
    }
    
    // 手动OIDC登录方法 - 可以获取授权码
    func signInWithManualOIDC() async {
        Log.info("🚀 开始手动OIDC登录流程（可获取授权码）")
        isLoading = true
        error = nil
        
        do {
            try await nativeLogtoManager.signIn()
            
            // 注意：此时用户被重定向到浏览器，实际的登录完成会在handleCallback中处理
            Log.info("🌐 浏览器授权已启动，等待用户操作...")
            
        } catch {
            Log.error("❌ 手动OIDC登录启动失败: \(error)")
            self.error = error.localizedDescription
            self.isAuthenticated = false
            self.isLoading = false
        }
    }
    
    // 处理手动OIDC回调
    func handleManualOIDCCallback(url: URL) async {
        Log.info("🔄 处理手动OIDC回调")
        
        // 原生SDK会自动处理回调URL，我们只需要检查认证状态
        await MainActor.run {
            // 检查是否成功获取到token
            if storage.accessToken != nil {
                self.isAuthenticated = true
                self.error = nil
                self.isLoading = false
                Log.info("🎉 手动OIDC登录认证成功")
                logTokenStatus()
                storage.printUserData()
            } else {
                self.error = "登录成功但未获取到token"
                self.isAuthenticated = false
                self.isLoading = false
                Log.error("❌ 登录成功但未获取到token")
            }
        }
    }

    // 检查用户注册状态
    var isRegistrationComplete: Bool {
        guard let userId = storage.userId, !userId.isEmpty else {
            return false
        }
        
        // 检查是否有标记注册未完成的标志
        let incompleteFlag = UserDefaultsManager.shared.getBool(key: "registration_incomplete")
        return !incompleteFlag
    }
    
    // 获取用户注册建议
    func getRegistrationAdvice() -> String? {
        if isAuthenticated && !isRegistrationComplete {
            return "检测到您的账户注册未完成。为确保正常使用所有功能，建议使用手动OIDC登录方式重新登录以完成注册。"
        }
        return nil
    }

    /// 检查是否有可用的刷新令牌
    public var hasRefreshToken: Bool {
        Log.debug("🔍 检查刷新令牌可用性...")
        
        // 检查UserDefaults中是否有刷新令牌
        if let token = refreshToken {
            Log.debug("🔍 从UserDefaults获取到刷新令牌: \(String(token.prefix(10)))...")
            
            if token.isEmpty {
                Log.debug("🔍 刷新令牌为空字符串")
                return false
            } else if token == "native_sdk_managed" {
                Log.debug("🔍 刷新令牌标记为原生SDK管理，但我们现在使用后端API")
                return false
            } else {
                Log.debug("🔍 UserDefaults中存在有效刷新令牌")
                return true
            }
        } else {
            Log.debug("🔍 UserDefaults中没有刷新令牌")
        }
        
        // 直接检查UserDefaults以确认
        let directCheck = storage.getString(key: "refresh_token")
        if let directToken = directCheck {
            Log.debug("🔍 直接检查UserDefaults发现刷新令牌: \(String(directToken.prefix(10)))...")
        } else {
            Log.debug("🔍 直接检查UserDefaults确认没有刷新令牌")
        }
        
        Log.debug("🔍 未找到可用的刷新令牌")
        return false
    }
    
    /// 获取可用的刷新令牌（用于手动刷新）
    public var availableRefreshToken: String? {
        Log.debug("🔍 获取可用的刷新令牌...")
        
        // 直接使用UserDefaults中的刷新令牌
        if let token = refreshToken {
            Log.debug("🔍 从refreshToken属性获取到: \(String(token.prefix(10)))...")
            
            if token.isEmpty {
                Log.debug("🔍 刷新令牌为空字符串")
                return nil
            } else if token == "native_sdk_managed" {
                Log.debug("🔍 刷新令牌标记为原生SDK管理，跳过")
                return nil
            } else {
                Log.debug("🔍 返回有效的刷新令牌")
                return token
            }
        }
        
        // 再次直接检查storage
        let directToken = storage.getString(key: "refresh_token")
        if let token = directToken {
            Log.debug("🔍 直接从storage获取到刷新令牌: \(String(token.prefix(10)))...")
            return token
        }
        
        Log.debug("🔍 未找到可用的刷新令牌")
        return nil
    }

    // MARK: - Private Methods
    
    private func checkInitialAuthStatus() async {
        Log.info("🔍 检查初始认证状态...")
        
        // 检查是否有有效的访问令牌
        if let token = accessToken, !token.isEmpty {
            // 检查令牌是否过期
            if let expiresAt = accessTokenExpiresAt, expiresAt > Date() {
                Log.info("✅ 发现有效的访问令牌")
                await MainActor.run {
                    self.isAuthenticated = true
                }
            } else {
                Log.info("⏰ 访问令牌已过期，尝试刷新")
                // 尝试刷新令牌
                do {
                    _ = try await refreshToken()
                    await MainActor.run {
                        self.isAuthenticated = true
                    }
                } catch {
                    Log.warning("⚠️ 令牌刷新失败，需要重新登录")
                    await MainActor.run {
                        self.isAuthenticated = false
                    }
                }
            }
        } else {
            Log.info("ℹ️ 未找到访问令牌")
            await MainActor.run {
                self.isAuthenticated = false
            }
        }
    }
}
