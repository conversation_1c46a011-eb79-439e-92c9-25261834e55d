import SwiftUI
import Combine

// 屏幕方向管理器 - 使用 @MainActor 确保 UI 操作的线程安全
@MainActor
final class OrientationManager: ObservableObject {
    static let shared = OrientationManager()
    
    @Published var orientation: UIInterfaceOrientationMask = .portrait
    
    private init() {}
    
    // 设置为只支持竖屏
    func lockPortrait() {
        orientation = .portrait
        setOrientation()
    }
    
    // 设置为支持所有方向
    func unlockOrientation() {
        orientation = .all
        setOrientation()
    }
    
    // 设置为只支持横屏
    func lockLandscape() {
        orientation = .landscape
        setOrientation()
    }
    
    // 应用方向设置 - 已经在 @MainActor 上下文中
    private func setOrientation() {
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
            if let delegate = windowScene.delegate as? SceneDelegate {
                delegate.orientationManager = self
            }
        }
        
        // 设置首选方向
        if #available(iOS 16.0, *) {
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
                // 使用 self.orientation 来应用正确的方向设置
                let interfaceOrientations = self.orientation 
                windowScene.requestGeometryUpdate(.iOS(interfaceOrientations: interfaceOrientations))
            }
        }
        
        // 强制屏幕旋转
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
            if let rootViewController = windowScene.windows.first?.rootViewController {
                rootViewController.setNeedsUpdateOfSupportedInterfaceOrientations()
                rootViewController.setNeedsStatusBarAppearanceUpdate()
            }
        }
    }
}

// 添加 SceneDelegate 类来处理屏幕方向
class SceneDelegate: UIResponder, UIWindowSceneDelegate {
    var orientationManager: OrientationManager?
    
    func windowScene(_ windowScene: UIWindowScene, 
                   didUpdate previousCoordinateSpace: UICoordinateSpace, 
                   interfaceOrientation previousInterfaceOrientation: UIInterfaceOrientation, 
                   traitCollection previousTraitCollection: UITraitCollection) {
        // 可以添加方向更新的处理逻辑
    }
}

// 添加旋转控制修饰符
struct DeviceRotationViewModifier: ViewModifier {
    let action: @MainActor (UIDeviceOrientation) -> Void
    
    func body(content: Content) -> some View {
        content
            .onAppear()
            .onReceive(NotificationCenter.default.publisher(for: UIDevice.orientationDidChangeNotification)) { _ in
                Task { @MainActor in
                    action(UIDevice.current.orientation)
                }
            }
    }
}

// 为 View 扩展旋转修饰符
extension View {
    func onRotate(perform action: @escaping @MainActor (UIDeviceOrientation) -> Void) -> some View {
        self.modifier(DeviceRotationViewModifier(action: action))
    }
}