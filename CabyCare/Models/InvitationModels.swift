import Foundation

// MARK: - 邀请相关模型

/// 邀请模型
struct Invitation: Identifiable {
    let id: String
    let groupName: String
    let inviterName: String
    let role: Int  // 改为使用Int类型，与API保持一致
    let status: InvitationStatus
    
    enum InvitationStatus {
        case pending
        case accepted
        case rejected
    }
}

// MARK: - API响应模型

/// 邀请列表API响应模型
struct InvitationsResponse: Codable {
    let code: Int
    let status: String
    let message: String
    let data: [InvitationResponse]?  // 改为可选数组
}

/// 邀请响应模型
struct InvitationResponse: Codable, Identifiable {
    let invitationId: String
    let groupId: String
    let groupName: String
    let inviterId: String
    let inviterName: String
    let inviteeId: String
    let inviteeName: String
    let status: Int
    let role: Int
    let createdAt: String
    let expireAt: String?
    
    // 为了符合Identifiable协议
    var id: String { invitationId }
    
    enum CodingKeys: String, CodingKey {
        case invitationId = "invitation_id"
        case groupId = "group_id"
        case groupName = "group_name"
        case inviterId = "inviter_id"
        case inviterName = "inviter_name"
        case inviteeId = "invitee_id"
        case inviteeName = "invitee_name"
        case status
        case role
        case createdAt = "created_at"
        case expireAt = "expire_at"
    }
}

/// 创建家庭组响应模型
struct CreateFamilyGroupResponse: Codable {
    let status: String
    let message: String
    let data: FamilyGroupData
    
    struct FamilyGroupData: Codable {
        let groupId: String
        let groupName: String
        let ownerId: String
        let description: String
        let maxMembers: Int
        let createdAt: String
        let updatedAt: String
        
        enum CodingKeys: String, CodingKey {
            case groupId = "group_id"
            case groupName = "group_name"
            case ownerId = "owner_id"
            case description
            case maxMembers = "max_members"
            case createdAt = "created_at"
            case updatedAt = "updated_at"
        }
    }
}

/// 家庭组API响应模型
struct FamilyGroupsResponse: Codable {
    let code: Int
    let status: String
    let message: String
    let data: [FamilyGroupData]?
    
    struct FamilyGroupData: Codable {
        let groupId: String
        let groupName: String
        let description: String
        let ownerId: String
        let memberCount: Int
        let deviceCount: Int
        let maxMembers: Int
        let createdAt: String
        let updatedAt: String
        // role 字段在当前API响应中缺失，我们后面会处理
        
        enum CodingKeys: String, CodingKey {
            case groupId = "group_id"
            case groupName = "group_name"
            case description
            case ownerId = "owner_id"
            case memberCount = "member_count"
            case deviceCount = "device_count"
            case maxMembers = "max_members"
            case createdAt = "created_at"
            case updatedAt = "updated_at"
        }
    }
} 