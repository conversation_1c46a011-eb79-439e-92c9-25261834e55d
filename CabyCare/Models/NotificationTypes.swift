import Foundation

// 通知类型枚举
enum NotificationType: String, Codable {
    case daily      // 日常类
    case alert      // 异常类
    case stats      // 统计类
    case health     // 猫咪状态类
    case maintain   // 维护类

    // 是否可以在设置中关闭
    var canBeDisabled: Bool {
        switch self {
        case .daily, .stats:
            return true
        case .alert, .health, .maintain:
            return false
        }
    }
}

// 通知子类型枚举
enum NotificationSubtype: String, Codable {
    // 日常类
    case excretion          // 排泄
    case litterLow          // 猫砂不足
    case litterDirty       // 需要铲屎

    // 异常类
    case foreignAnimal     // 异常动物
    case foreignObject     // 异物
    case systemError       // 系统异常

    // 统计类
    case dailyStats       // 每日统计
    case weeklyStats      // 每周统计
    case monthlyStats     // 每月统计

    // 健康类
    case weightChange     // 体重变化
    case behaviorChange   // 行为变化
    case habitChange      // 习惯变化

    // 维护类
    case newCat          // 新猫咪
    case missingCat      // 猫咪失踪
    case returnCat       // 猫咪回归
}

// 通知数据结构
struct NotificationData: Codable {
    let id: String                    // 通知唯一标识
    let type: NotificationType        // 通知类型
    let subtype: NotificationSubtype  // 通知子类型
    let timestamp: Date               // 通知时间
    let title: String                 // 通知标题
    let body: String                  // 通知内容
    let metadata: NotificationMetadata // 元数据
}

// 通知元数据结构
struct NotificationMetadata: Codable {
    // 通用字段
    let catId: String?               // 相关猫咪ID

    // 视频相关
    let videoId: String?             // 视频ID
    let videoTimestamp: Date?        // 视频时间戳

    // 统计相关
    let statsStartDate: Date?        // 统计开始时间
    let statsEndDate: Date?          // 统计结束时间

    // 健康相关
    let healthMetric: String?        // 健康指标
    let oldValue: String?            // 原值
    let newValue: String?            // 新值

    // 维护相关
    let maintenanceType: String?     // 维护类型
    let severity: Int?               // 严重程度 1-5
}