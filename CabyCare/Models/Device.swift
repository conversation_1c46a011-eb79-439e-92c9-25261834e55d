import Foundation

struct Device: Codable {
    let deviceId: String
    let userId: String
    let hardwareSn: String
    let name: String
    let model: String
    let timezone: String
    let firmwareVersion: String
    let status: Int
    let lastHeartbeat: Date?
    let lastActive: Date?
    let createdAt: Date
    let updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case deviceId = "device_id"
        case userId = "user_id"
        case hardwareSn = "hardware_sn"
        case name
        case model
        case timezone = "timezone"
        case firmwareVersion = "firmware_version"
        case status
        case lastHeartbeat = "last_heartbeat"
        case lastActive = "last_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        deviceId = try container.decode(String.self, forKey: .deviceId)
        userId = try container.decode(String.self, forKey: .userId)
        hardwareSn = try container.decode(String.self, forKey: .hardwareSn)
        name = try container.decode(String.self, forKey: .name)
        model = try container.decode(String.self, forKey: .model)
        timezone = try container.decode(String.self, forKey: .timezone)
        firmwareVersion = try container.decode(String.self, forKey: .firmwareVersion)
        status = try container.decode(Int.self, forKey: .status)
        
        if let lastHeartbeatString = try container.decodeIfPresent(String.self, forKey: .lastHeartbeat),
           let lastHeartbeatDate = Configuration.DateFormat.iso8601Formatter.date(from: lastHeartbeatString) {
            lastHeartbeat = lastHeartbeatDate
        } else {
            lastHeartbeat = nil
        }
        
        if let lastActiveString = try container.decodeIfPresent(String.self, forKey: .lastActive),
           let lastActiveDate = Configuration.DateFormat.iso8601Formatter.date(from: lastActiveString) {
            lastActive = lastActiveDate
        } else {
            lastActive = nil
        }
        
        let createdAtString = try container.decode(String.self, forKey: .createdAt)
        let updatedAtString = try container.decode(String.self, forKey: .updatedAt)
        
        guard let createdAtDate = Configuration.DateFormat.iso8601Formatter.date(from: createdAtString),
              let updatedAtDate = Configuration.DateFormat.iso8601Formatter.date(from: updatedAtString) else {
            throw DecodingError.dataCorrupted(
                DecodingError.Context(
                    codingPath: container.codingPath,
                    debugDescription: "Invalid date format"
                )
            )
        }
        
        createdAt = createdAtDate
        updatedAt = updatedAtDate
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(deviceId, forKey: .deviceId)
        try container.encode(userId, forKey: .userId)
        try container.encode(hardwareSn, forKey: .hardwareSn)
        try container.encode(name, forKey: .name)
        try container.encode(model, forKey: .model)
        try container.encode(timezone, forKey: .timezone)
        try container.encode(firmwareVersion, forKey: .firmwareVersion)
        try container.encode(status, forKey: .status)
        
        if let lastHeartbeat = lastHeartbeat {
            try container.encode(Configuration.DateFormat.formatISO8601(lastHeartbeat), forKey: .lastHeartbeat)
        } else {
            try container.encodeNil(forKey: .lastHeartbeat)
        }
        
        if let lastActive = lastActive {
            try container.encode(Configuration.DateFormat.formatISO8601(lastActive), forKey: .lastActive)
        } else {
            try container.encodeNil(forKey: .lastActive)
        }
        
        try container.encode(Configuration.DateFormat.formatISO8601(createdAt), forKey: .createdAt)
        try container.encode(Configuration.DateFormat.formatISO8601(updatedAt), forKey: .updatedAt)
    }
    
    // 便利初始化方法，用于从 AccessibleDevice 创建 Device
    init(deviceId: String, userId: String, hardwareSn: String, name: String, model: String,
         timezone: String, firmwareVersion: String, status: Int,
         lastHeartbeat: Date?, lastActive: Date?, createdAt: Date, updatedAt: Date) {
        self.deviceId = deviceId
        self.userId = userId
        self.hardwareSn = hardwareSn
        self.name = name
        self.model = model
        self.timezone = timezone
        self.firmwareVersion = firmwareVersion
        self.status = status
        self.lastHeartbeat = lastHeartbeat
        self.lastActive = lastActive
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }
}
