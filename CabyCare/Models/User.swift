import Foundation

struct User: Codable {
    let userId: String
    let username: String
    let email: String
    let phone: String
    let nickname: String
    let status: Int
    let createdAt: Date
    let updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case userId = "user_id"
        case username
        case email
        case phone
        case nickname
        case status
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        userId = try container.decode(String.self, forKey: .userId)
        username = try container.decode(String.self, forKey: .username)
        email = try container.decode(String.self, forKey: .email)
        phone = try container.decode(String.self, forKey: .phone)
        nickname = try container.decode(String.self, forKey: .nickname)
        status = try container.decode(Int.self, forKey: .status)
        
        let createdAtString = try container.decode(String.self, forKey: .createdAt)
        let updatedAtString = try container.decode(String.self, forKey: .updatedAt)
        
        guard let createdAtDate = Configuration.DateFormat.iso8601Formatter.date(from: createdAtString),
              let updatedAtDate = Configuration.DateFormat.iso8601Formatter.date(from: updatedAtString) else {
            throw DecodingError.dataCorrupted(
                DecodingError.Context(
                    codingPath: container.codingPath,
                    debugDescription: "Invalid date format"
                )
            )
        }
        
        createdAt = createdAtDate
        updatedAt = updatedAtDate
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(userId, forKey: .userId)
        try container.encode(username, forKey: .username)
        try container.encode(email, forKey: .email)
        try container.encode(phone, forKey: .phone)
        try container.encode(nickname, forKey: .nickname)
        try container.encode(status, forKey: .status)
        try container.encode(Configuration.DateFormat.formatISO8601(createdAt), forKey: .createdAt)
        try container.encode(Configuration.DateFormat.formatISO8601(updatedAt), forKey: .updatedAt)
    }
}

struct UserResponse: Codable {
    let code: Int
    let message: String
    let data: [User]
}