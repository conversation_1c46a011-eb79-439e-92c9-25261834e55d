import Foundation
import UIKit

// MARK: - API Request Models
struct CreateCatRequest: Codable {
    let name: String
    let gender: Int8
    let weight: Double?
    let birthDate: String?
    let photosBase64: [String]?
    let avatarBase64: String?
    
    enum CodingKeys: String, CodingKey {
        case name
        case gender
        case weight
        case birthDate = "birthday"
        case photosBase64 = "photos_base64"
        case avatarBase64 = "avatar_base64"
    }
}

// MARK: - API Response Models
struct CatAPIResponse: Codable {
    let catId: String
    let userId: String
    let name: String
    let birthday: String?
    let gender: Int8
    let color: String?
    let weight: Double
    let status: Int
    let avatarUrl: String?
    let createdAt: String
    let updatedAt: String
    
    enum CodingKeys: String, CodingKey {
        case catId = "cat_id"
        case userId = "user_id"
        case name
        case birthday
        case gender
        case color
        case weight
        case status
        case avatarUrl = "avatar_url"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Generic API Response Wrapper (for future use)
struct APIResponse<T: Codable>: Codable {
    let code: Int
    let status: String?
    let message: String
    let data: T?
    let error: String?
}

// MARK: - Create Cat API Response (different format)
struct CreateCatAPIResponse: Codable {
    let status: String
    let message: String
    let data: CatAPIResponse
}

// MARK: - Update Cat API Response
struct UpdateCatAPIResponse: Codable {
    let catId: String
    let name: String
    let status: Int
    let updatedAt: String
    
    enum CodingKeys: String, CodingKey {
        case catId = "cat_id"
        case name
        case status
        case updatedAt = "updated_at"
    }
}

// MARK: - Specific Response Types
struct CatsListResponse: Codable {
    let cats: [CatAPIResponse]
}

// For the wrapped response from server (if needed in future)
typealias CatsListAPIResponse = APIResponse<[CatAPIResponse]>

// For the direct array response (current server format)
typealias CatsDirectResponse = [CatAPIResponse]

// MARK: - API Error Response
struct APIErrorResponse: Codable {
    let code: Int
    let message: String
    let error: String?
}

// MARK: - Cat Action Response
struct CatActionResponse: Codable {
    let status: String
    let message: String
    let catId: String?
    
    enum CodingKeys: String, CodingKey {
        case status
        case message
        case catId = "cat_id"
    }
}

// MARK: - Extensions for conversion
extension CatAPIResponse {
    func toCatProfile() -> CatProfile {
        // 根据gender值确定绝育状态
        let neuteredInfo = decodeNeuteredStatus(gender)
        
        // 只有当有明确绝育状态时才设置，"未知"状态不显示
        let neuteredStatus: String? = neuteredInfo != NSLocalizedString("cat_status_unknown", comment: "") ? neuteredInfo : nil
        
        // Calculate age from birth date
        var ageString = NSLocalizedString("cat_age_unknown", comment: "")
        if let birthdayString = birthday,
           let date = ISO8601DateFormatter().date(from: birthdayString) {
            let calendar = Calendar.current
            let ageComponents = calendar.dateComponents([.year, .month], from: date, to: Date())
            
            if let years = ageComponents.year, years > 0 {
                ageString = "\(years)" + NSLocalizedString("cat_creation_age_year", comment: "")
            } else if let months = ageComponents.month, months > 0 {
                ageString = "\(months)" + NSLocalizedString("cat_creation_age_month", comment: "")
            } else {
                ageString = NSLocalizedString("cat_creation_age_less_than_month", comment: "")
            }
        }
        
        let weightString = weight > 0 ? String(format: "%.1fkg", weight) : NSLocalizedString("cat_weight_unknown", comment: "")
        
        // Parse birth date
        var parsedBirthDate: Date?
        if let birthdayString = birthday {
            parsedBirthDate = ISO8601DateFormatter().date(from: birthdayString)
        }
        
        return CatProfile(
            id: catId,
            name: name,
            age: ageString,
            gender: gender, // 直接使用Int8类型
            weight: weightString,
            type: .white, // Default type since API doesn't provide this info
            neuteredStatus: neuteredStatus,
            avatarUrl: avatarUrl,
            birthDate: parsedBirthDate
        )
    }
    
    private func decodeNeuteredStatus(_ genderCode: Int8) -> String {
        switch genderCode {
        case 1, -1: return NSLocalizedString("cat_status_unknown", comment: "")
        case 10, -10: return NSLocalizedString("cat_neutered_status", comment: "")
        case 11, -11: return NSLocalizedString("cat_status_not_neutered", comment: "")
        default: return NSLocalizedString("cat_status_unknown", comment: "")
        }
    }
} 