import Foundation
import UIKit

struct CatProfileCreation {
    static let maxNameLength = 256
    
    // Required fields
    var name: String = ""
    var gender: Gender = .unknown
    var neuteredStatus: NeuteredStatus = .unknown
    
    // Optional fields
    var weight: Double?
    var birthDate: Date?
    var photos: [UIImage] = []
    
    // Validation states
    var isNameValid: Bool {
        !name.isEmpty && name.utf8.count <= Self.maxNameLength
    }
    
    var nameValidationError: String? {
        if name.isEmpty {
            return NSLocalizedString("cat_creation_error_invalid_name", comment: "")
        }
        if name.utf8.count > Self.maxNameLength {
            return NSLocalizedString("cat_creation_error_name_too_long", comment: "")
        }
        return nil
    }
    
    var isWeightValid: Bool { weight == nil || weight! > 0 }
    
    // Photo validation states
    var validatedPhotos: [(image: UIImage, isValid: Bool, catBoundingBox: CGRect?, validationStatus: ValidationStatus)] = []
    
    enum Gender: String {
        case male = "male"
        case female = "female"
        case unknown = "unknown"
    }
    
    enum NeuteredStatus: String {
        case neutered = "neutered"
        case notNeutered = "not_neutered"
        case unknown = "unknown"
    }

    enum ProfileStatus: String {
        case ok = "ok"
        case nameInvalid = "name_invalid"
        case noValidPhoto = "no_valid_photo"
        case submissionFailed = "submission_failed"
    }

    enum ProfileSubmissionStatus: String {
        case pending = "pending"
        case submitted = "submitted"
        case failed = "failed"
    }
    
    // Computed property for API
    var genderCode: Int8 {
        switch (gender, neuteredStatus) {
        case (.male, .unknown): return 1
        case (.male, .notNeutered): return 11
        case (.male, .neutered): return 10
        case (.female, .unknown): return -1
        case (.female, .notNeutered): return -11
        case (.female, .neutered): return -10
        case (.unknown, _): return 0
        }
    }
}

// MARK: - Localization Keys
extension CatProfileCreation {
    enum LocalizationKey {
        static let addCat = "add_cat"
        static let addDevice = "add_device"
        static let cancel = "cat_creation_cancel"
        static let confirm = "cat_creation_confirm"
        static let next = "cat_creation_next"
        static let previous = "cat_creation_previous"
        static let submit = "cat_creation_submit"
        static let preview = "cat_creation_preview"
        static let finish = "cat_creation_finish"
        // Page 1
        static let introTitle = "cat_creation_intro_title"
        static let introDescription = "cat_creation_intro_description"
        
        // Page 2
        static let basicInfoTitle = "cat_creation_basic_info_title"
        static let nameLabel = "cat_creation_name_label"
        static let nameRequired = "cat_creation_name_required"
        static let genderLabel = "cat_creation_gender_label"
        static let neuteredLabel = "cat_creation_neutered_label"
        static let male = "cat_creation_male"
        static let female = "cat_creation_female"
        static let unknown = "cat_creation_unknown"
        static let neutered = "cat_creation_neutered"
        static let notNeutered = "cat_creation_not_neutered"
        
        // Page 3
        static let physicalInfoTitle = "cat_creation_physical_info_title"
        static let weightLabel = "cat_creation_weight_label"
        static let weightUnit = "cat_creation_weight_unit"
        static let birthDateLabel = "cat_creation_birth_date_label"
        static let ageLabel = "cat_creation_age_label"
        static let ageYear = "cat_creation_age_year"
        static let ageMonth = "cat_creation_age_month"
        static let ageLessThanMonth = "cat_creation_age_less_than_month"
        static let skip = "cat_creation_skip"
        
        // Page 4
        static let photosTitle = "cat_creation_photos_title"
        static let photosDescription = "cat_creation_photos_description"
        static let addPhotos = "cat_creation_add_photos"
        static let maxPhotos = "cat_creation_max_photos"
        static let invalidPhotosWarning = "cat_creation_invalid_photos_warning"
        
        // Page 5
        static let processingTitle = "cat_creation_processing_title"
        static let successTitle = "cat_creation_success_title"
        static let successDescription = "cat_creation_success_description"
        
        // Alerts
        static let cancelConfirmationTitle = "cat_creation_cancel_confirmation_title"
        static let cancelConfirmationMessage = "cat_creation_cancel_confirmation_message"
        static let invalidPhotosTitle = "cat_creation_invalid_photos_title"
        static let invalidPhotosMessage = "cat_creation_invalid_photos_message"
    }
} 