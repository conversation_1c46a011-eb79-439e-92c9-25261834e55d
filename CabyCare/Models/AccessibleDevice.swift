import Foundation

// 可访问设备的响应模型
struct AccessibleDevicesResponse: Codable {
    let code: Int
    let status: String
    let message: String
    let data: [AccessibleDevice]
    
    // 自定义初始化器来处理缺失的data字段
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        code = try container.decode(Int.self, forKey: .code)
        status = try container.decode(String.self, forKey: .status)
        message = try container.decode(String.self, forKey: .message)
        
        // 处理data字段可能缺失或为null的情况
        if container.contains(.data) {
            // 如果data字段存在，尝试解码，如果失败则使用空数组
            data = (try? container.decode([AccessibleDevice].self, forKey: .data)) ?? []
        } else {
            // 如果data字段不存在，使用空数组
            data = []
        }
    }
    
    // 自定义编码实现
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(code, forKey: .code)
        try container.encode(status, forKey: .status)
        try container.encode(message, forKey: .message)
        try container.encode(data, forKey: .data)
    }
    
    // 定义编码键
    enum CodingKeys: String, CodingKey {
        case code
        case status
        case message
        case data
    }
}

// 可访问设备模型
struct AccessibleDevice: Codable, Identifiable {
    let deviceId: String
    let userId: String
    let hardwareSn: String
    let name: String
    let model: String
    let timezone: String
    let firmwareVersion: String
    let status: Int
    let lastHeartbeat: String?
    let lastActive: String?
    let createdAt: String
    let updatedAt: String
    
    // 从API转换为Swift属性名
    enum CodingKeys: String, CodingKey {
        case deviceId = "device_id"
        case userId = "user_id"
        case hardwareSn = "hardware_sn"
        case name
        case model
        case timezone
        case firmwareVersion = "firmware_version"
        case status
        case lastHeartbeat = "last_heartbeat"
        case lastActive = "last_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
    
    // 计算属性-设备是否在线
    var online: Bool {
        return status == 1
    }
    
    // 为了遵循Identifiable协议
    var id: String { deviceId }
}

// 设备所有权类型
enum OwnershipType: String, Codable {
    case own = "own"
    case shared = "shared"
}