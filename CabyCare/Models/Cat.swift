import Foundation
import SwiftUI

// MARK: - Cat Profile Model
struct CatProfile: Identifiable, Codable {
    let id: String
    let name: String
    var age: String
    let gender: Int8
    var weight: String
    var healthStatus: CatHealthStatus
    var activityLevel: CatActivityLevel
    var type: CatType
    var neuteredStatus: String? // 绝育状态，仅当有明确数据时显示
    var avatarUrl: String? // 头像URL

    // UI Constants
    static let defaultAvatar = Image(systemName: "cat.fill")
    static let placeholderColor = Color.gray.opacity(0.3)
    static let avatarSize: CGFloat = 40
    static let editMenuTitle = "修改猫咪"

    // 添加更多猫咪相关的属性
    var birthDate: Date?
    var lastCheckupDate: Date?
    var vaccinations: [Vaccination]
    var medications: [Medication]
    var dietaryRestrictions: [String]

    init(
        id: String = UUID().uuidString,
        name: String,
        age: String,
        gender: Int8,
        weight: String,
        type: CatType,
        healthStatus: CatHealthStatus = .healthy,
        activityLevel: CatActivityLevel = .normal,
        neuteredStatus: String? = nil,
        avatarUrl: String? = nil,
        birthDate: Date? = nil,
        lastCheckupDate: Date? = nil,
        vaccinations: [Vaccination] = [],
        medications: [Medication] = [],
        dietaryRestrictions: [String] = []
    ) {
        self.id = id
        self.name = name
        self.age = age
        self.gender = gender
        self.weight = weight
        self.type = type
        self.healthStatus = healthStatus
        self.activityLevel = activityLevel
        self.neuteredStatus = neuteredStatus
        self.avatarUrl = avatarUrl
        self.birthDate = birthDate
        self.lastCheckupDate = lastCheckupDate
        self.vaccinations = vaccinations
        self.medications = medications
        self.dietaryRestrictions = dietaryRestrictions
    }
}

// MARK: - Supporting Types

func getCatGender(gender: Int8) -> String {
  switch gender {
  case 1, 10, 11:
    return NSLocalizedString("cat_gender_male", comment: "")
  case -1, -10, -11:
    return NSLocalizedString("cat_gender_female", comment: "")
  default:
    return NSLocalizedString("cat_creation_unknown", comment: "")
  }
}

enum CatHealthStatus: String, CaseIterable, Codable {
    case healthy
    case needsAttention
    case sick
    
    var localizedString: String {
        switch self {
        case .healthy:
            return NSLocalizedString("cat_health_healthy", comment: "")
        case .needsAttention:
            return NSLocalizedString("cat_health_needs_attention", comment: "")
        case .sick:
            return NSLocalizedString("cat_health_sick", comment: "")
        }
    }
}

enum CatActivityLevel: String, Codable {
    case low = "不活跃"
    case normal = "正常"
    case high = "活跃"
}

// MARK: - Cat Type
enum CatType: String, Codable, CaseIterable {
    case white = "小白"
    case black = "小黑"
    case calico = "三花"

    var icon: String {
        "cat.fill"  // 统一使用同一个基础图标
    }

    // 返回类型为具体的 Image
    func styledIcon(size: CGFloat = CatProfile.avatarSize) -> some View {
        let baseImage = Image(systemName: icon)
            .resizable()
            .scaledToFit()
            .frame(width: size, height: size)

        switch self {
        case .white:
            return baseImage
                .symbolRenderingMode(.palette)
                .foregroundStyle(
                    .gray,            // 主色
                    .white.opacity(0.8)  // 背景色
                )
        case .black:
            return baseImage
                .symbolRenderingMode(.palette)
                .foregroundStyle(
                    .black,
                    .gray.opacity(0.3)
                )
        case .calico:
            return baseImage
                .symbolRenderingMode(.palette)
                .foregroundStyle(
                    .orange,
                    Color(red: 0.9, green: 0.8, blue: 0.7).opacity(0.5)
                )
        }
    }
}

struct Vaccination: Identifiable, Codable {
    let id: String
    let name: String
    let date: Date
    let nextDueDate: Date?
    let veterinarian: String?
}

struct Medication: Identifiable, Codable {
    let id: String
    let name: String
    let startDate: Date
    let endDate: Date?
    let dosage: String
    let frequency: String
    let notes: String?
}
