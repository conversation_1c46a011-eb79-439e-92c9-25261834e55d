import Foundation

/// 设备状态枚举
enum DeviceStatus: Equatable {
    case online      // 在线
    case offline     // 离线
    case unknown     // 未知状态（缓存数据）
}

/// 设备状态响应模型 - 对应test_device_status.sh中的API响应
struct DeviceStatusResponse: Codable, Identifiable, Equatable {
    let id: String // 使用 name 作为 ID，或者可以是设备的唯一标识符
    let name: String
    let model: String
    let firmware: String
    let online: Bool? // 修改为可选类型，支持未知状态
    let lastHeartbeat: String?
    let ipv4: String?
    let ipv6: String?
    
    // 添加用于UI显示的计算属性
    var status: DeviceStatus {
        guard let online = online else { return .unknown }
        return online ? .online : .offline
    }
    
    // 添加设备位置信息（从name中提取或默认值）
    var location: String {
        // 可以从设备名称中提取位置信息，或者返回默认值
        if name.contains("客厅") {
            return "客厅"
        } else if name.contains("卧室") {
            return "卧室"
        } else if name.contains("厨房") {
            return "厨房"
        } else {
            return "未知位置"
        }
    }
    
    // 格式化的最后心跳时间
    var formattedLastHeartbeat: String {
        guard let heartbeat = lastHeartbeat else { return "未知" }
        
        // 解析ISO8601格式的时间
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        
        guard let date = formatter.date(from: heartbeat) else { return heartbeat }
        
        // 格式化为用户友好的时间
        let displayFormatter = DateFormatter()
        displayFormatter.dateStyle = .short
        displayFormatter.timeStyle = .medium
        displayFormatter.locale = Locale.current
        
        return displayFormatter.string(from: date)
    }
    
    private enum CodingKeys: String, CodingKey {
        case name, model, firmware, online
        case lastHeartbeat = "last_heartbeat"
        case ipv4, ipv6
    }
    
    // 实现Identifiable协议的init
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        name = try container.decode(String.self, forKey: .name)
        model = try container.decode(String.self, forKey: .model)
        firmware = try container.decode(String.self, forKey: .firmware)
        online = try container.decodeIfPresent(Bool.self, forKey: .online) // 修改为可选解码
        lastHeartbeat = try container.decodeIfPresent(String.self, forKey: .lastHeartbeat)
        ipv4 = try container.decodeIfPresent(String.self, forKey: .ipv4)
        ipv6 = try container.decodeIfPresent(String.self, forKey: .ipv6)
        
        // 使用name作为ID，如果有多个同名设备，可以考虑使用设备的MAC地址或其他唯一标识符
        id = name
    }
    
    // 自定义初始化器 - 更新参数类型
    init(id: String? = nil, name: String, model: String, firmware: String, online: Bool? = nil, lastHeartbeat: String? = nil, ipv4: String? = nil, ipv6: String? = nil) {
        self.id = id ?? name
        self.name = name
        self.model = model
        self.firmware = firmware
        self.online = online
        self.lastHeartbeat = lastHeartbeat
        self.ipv4 = ipv4
        self.ipv6 = ipv6
    }
}

/// 设备视图模型 - 用于设备管理界面
struct DeviceViewModel: Identifiable, Equatable {
    let id: String            // device_id
    let userId: String        // user_id
    let hardwareSn: String    // hardware_sn
    let name: String          // name
    let model: String         // model
    let timezone: String      // timezone
    let firmwareVersion: String // firmware_version
    let status: DeviceStatus  // 基于 status 确定
    let lastHeartbeat: String? // last_heartbeat
    let lastActive: String?    // last_active
    let createdAt: String     // created_at
    let updatedAt: String     // updated_at
    let location: String      // 自定义属性，API中没有对应值
    
    // 基本初始化方法
    init(id: String, name: String, timezone: String, model: String, location: String, status: DeviceStatus) {
        self.id = id
        self.userId = ""
        self.hardwareSn = ""
        self.name = name
        self.model = model
        self.timezone = timezone
        self.firmwareVersion = ""
        self.status = status
        self.lastHeartbeat = nil
        self.lastActive = nil
        self.createdAt = ""
        self.updatedAt = ""
        self.location = location
    }
    
    // 从API响应数据完整初始化
    init(deviceId: String, userId: String, hardwareSn: String, name: String, model: String, 
         timezone: String, firmwareVersion: String, status: Int, lastHeartbeat: String?, 
         lastActive: String?, createdAt: String, updatedAt: String, location: String = "") {
        self.id = deviceId
        self.userId = userId
        self.hardwareSn = hardwareSn
        self.name = name
        self.model = model
        self.timezone = timezone
        self.firmwareVersion = firmwareVersion
        self.status = status == 1 ? .online : .offline
        self.lastHeartbeat = lastHeartbeat
        self.lastActive = lastActive
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.location = location.isEmpty ? model : location // 如果没有提供位置，使用model作为备用
    }
    
    /// 从 AccessibleDevice 初始化
    init(from accessibleDevice: AccessibleDevice) {
        self.id = accessibleDevice.deviceId
        self.userId = accessibleDevice.userId
        self.hardwareSn = accessibleDevice.hardwareSn
        self.name = accessibleDevice.name
        self.model = accessibleDevice.model
        self.timezone = accessibleDevice.timezone
        self.firmwareVersion = accessibleDevice.firmwareVersion
        self.status = accessibleDevice.online ? .online : .offline
        self.lastHeartbeat = accessibleDevice.lastHeartbeat
        self.lastActive = accessibleDevice.lastActive
        self.createdAt = accessibleDevice.createdAt
        self.updatedAt = accessibleDevice.updatedAt
        self.location = ""
    }
}

// 为了向后兼容，保留原有的DeviceViewModel枚举
enum DeviceStatus_Legacy: Int, Codable {
    case offline = 0
    case online = 1
} 