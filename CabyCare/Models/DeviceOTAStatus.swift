import Foundation

// MARK: - OTA状态枚举
enum DeviceOTAStatus: String, CaseIterable, Codable {
    case idle = "idle"          // 空闲状态
    case updating = "updating"  // 正在更新
    case failed = "failed"      // 更新失败
    case completed = "completed" // 更新完成
    
    var displayName: String {
        switch self {
        case .idle:
            return "空闲"
        case .updating:
            return "升级中"
        case .failed:
            return "升级失败"
        case .completed:
            return "升级完成"
        }
    }
    
    var color: String {
        switch self {
        case .idle:
            return "gray"
        case .updating:
            return "blue"
        case .failed:
            return "red"
        case .completed:
            return "green"
        }
    }
    
    var systemImage: String {
        switch self {
        case .idle:
            return "circle"
        case .updating:
            return "arrow.clockwise.circle.fill"
        case .failed:
            return "xmark.circle.fill"
        case .completed:
            return "checkmark.circle.fill"
        }
    }
}

// MARK: - OTA状态响应模型
struct DeviceOTAStatusResponse: Codable, Identifiable {
    let deviceId: String
    let status: DeviceOTAStatus
    let lastUpdated: String
    
    var id: String { deviceId }
    
    enum CodingKeys: String, CodingKey {
        case deviceId = "device_id"
        case status
        case lastUpdated = "last_updated"
    }
    
    // 格式化的最后更新时间
    var formattedLastUpdated: String {
        let formatter = ISO8601DateFormatter()
        if let date = formatter.date(from: lastUpdated) {
            let displayFormatter = DateFormatter()
            displayFormatter.dateFormat = "MM-dd HH:mm"
            return displayFormatter.string(from: date)
        }
        return lastUpdated
    }
    
    // 相对时间显示
    var relativeTime: String {
        let formatter = ISO8601DateFormatter()
        if let date = formatter.date(from: lastUpdated) {
            let now = Date()
            let timeInterval = now.timeIntervalSince(date)
            
            if timeInterval < 60 {
                return "刚刚"
            } else if timeInterval < 3600 {
                let minutes = Int(timeInterval / 60)
                return "\(minutes)分钟前"
            } else if timeInterval < 86400 {
                let hours = Int(timeInterval / 3600)
                return "\(hours)小时前"
            } else {
                let days = Int(timeInterval / 86400)
                return "\(days)天前"
            }
        }
        return "未知"
    }
}

// MARK: - API错误模型
struct OTAStatusError: Error, LocalizedError {
    let message: String
    
    var errorDescription: String? {
        return message
    }
} 

// MARK: - 传感器状态相关模型

/// 传感器类型
enum SensorType: String, CaseIterable, Codable {
    case camera = "camera"
    case weightSensor = "weight_sensor"
    case wifi = "wifi"
    case microphone = "microphone"
    case bluetooth = "bluetooth"
    case temperatureHumiditySensor = "temperature_humidity_sensor"
    
    var displayName: String {
        switch self {
        case .camera:
            return "相机"
        case .weightSensor:
            return "重量传感器"
        case .wifi:
            return "WiFi"
        case .microphone:
            return "麦克风"
        case .bluetooth:
            return "蓝牙"
        case .temperatureHumiditySensor:
            return "温湿度传感器"
        }
    }
    
    var iconName: String {
        switch self {
        case .camera:
            return "camera.fill"
        case .weightSensor:
            return "scalemass.fill"
        case .wifi:
            return "wifi"
        case .microphone:
            return "mic.fill"
        case .bluetooth:
            return "bluetooth"
        case .temperatureHumiditySensor:
            return "thermometer"
        }
    }
}

/// 传感器错误信息
struct SensorError: Codable {
    let sensorType: SensorType
    let errorType: String?
    let errorTime: String?
    
    var isError: Bool {
        // 如果有错误类型且不为空，认为有错误
        return errorType != nil && !errorType!.isEmpty && errorType != "0"
    }
    
    var errorMessage: String {
        if isError {
            return "传感器异常 (错误类型: \(errorType ?? "未知"))"
        } else {
            return "传感器正常"
        }
    }
    
    var timestamp: String? {
        return errorTime
    }
    
    var additionalInfo: String? {
        if let time = errorTime, !time.isEmpty {
            // 尝试格式化时间
            if let date = ISO8601DateFormatter().date(from: time) {
                let formatter = DateFormatter()
                formatter.dateFormat = "MM-dd HH:mm"
                return "最后错误时间: \(formatter.string(from: date))"
            } else {
                return "最后错误时间: \(time)"
            }
        }
        return nil
    }
}

/// 设备传感器状态响应（根据实际API响应调整）
struct DeviceSensorStatusResponse: Codable {
    let deviceId: String
    let cameraLastErrorType: String?
    let cameraLastErrorTime: String?
    let weightSensorLastErrorType: String?
    let weightSensorLastErrorTime: String?
    let wifiLastErrorType: String?
    let wifiLastErrorTime: String?
    let microphoneLastErrorType: String?
    let microphoneLastErrorTime: String?
    let bluetoothLastErrorType: String?
    let bluetoothLastErrorTime: String?
    let temperatureHumiditySensorLastErrorType: String?
    let temperatureHumiditySensorLastErrorTime: String?
    let lastUpdated: String
    
    enum CodingKeys: String, CodingKey {
        case deviceId = "device_id"
        case cameraLastErrorType = "camera_last_error_type"
        case cameraLastErrorTime = "camera_last_error_time"
        case weightSensorLastErrorType = "weight_sensor_last_error_type"
        case weightSensorLastErrorTime = "weight_sensor_last_error_time"
        case wifiLastErrorType = "wifi_last_error_type"
        case wifiLastErrorTime = "wifi_last_error_time"
        case microphoneLastErrorType = "microphone_last_error_type"
        case microphoneLastErrorTime = "microphone_last_error_time"
        case bluetoothLastErrorType = "bluetooth_last_error_type"
        case bluetoothLastErrorTime = "bluetooth_last_error_time"
        case temperatureHumiditySensorLastErrorType = "temperature_humidity_sensor_last_error_type"
        case temperatureHumiditySensorLastErrorTime = "temperature_humidity_sensor_last_error_time"
        case lastUpdated = "updated_at"
    }
    
    /// 获取所有传感器状态
    var sensorStatuses: [SensorError] {
        return [
            SensorError(sensorType: .camera, errorType: cameraLastErrorType, errorTime: cameraLastErrorTime),
            SensorError(sensorType: .weightSensor, errorType: weightSensorLastErrorType, errorTime: weightSensorLastErrorTime),
            SensorError(sensorType: .wifi, errorType: wifiLastErrorType, errorTime: wifiLastErrorTime),
            SensorError(sensorType: .microphone, errorType: microphoneLastErrorType, errorTime: microphoneLastErrorTime),
            SensorError(sensorType: .bluetooth, errorType: bluetoothLastErrorType, errorTime: bluetoothLastErrorTime),
            SensorError(sensorType: .temperatureHumiditySensor, errorType: temperatureHumiditySensorLastErrorType, errorTime: temperatureHumiditySensorLastErrorTime)
        ]
    }
    
    /// 检查是否有传感器错误
    var hasErrors: Bool {
        return sensorStatuses.contains { $0.isError }
    }
    
    /// 获取错误传感器数量
    var errorCount: Int {
        return sensorStatuses.filter { $0.isError }.count
    }
    
    /// 获取特定传感器的状态
    func sensorStatus(for type: SensorType) -> SensorError? {
        return sensorStatuses.first { $0.sensorType == type }
    }
    
    /// 格式化的最后更新时间
    var formattedLastUpdated: String {
        let formatter = ISO8601DateFormatter()
        if let date = formatter.date(from: lastUpdated) {
            let displayFormatter = DateFormatter()
            displayFormatter.dateFormat = "MM-dd HH:mm"
            return displayFormatter.string(from: date)
        }
        return lastUpdated
    }
}

// MARK: - 传感器状态错误处理

/// 传感器状态错误
struct SensorStatusError: Error, LocalizedError {
    let message: String
    
    var errorDescription: String? {
        return message
    }
    
    init(message: String) {
        self.message = message
    }
} 