import Foundation
import SwiftUI

// 家庭组详情API响应模型
struct FamilyGroupDetailResponse: Codable {
    let code: Int
    let status: String
    let message: String
    let data: FamilyGroupDetailData
    
    struct FamilyGroupDetailData: Codable {
        let groupInfo: GroupInfo
        let members: [MemberInfo]
        let devices: [DeviceInfo]
        
        enum CodingKeys: String, CodingKey {
            case groupInfo = "group_info"
            case members
            case devices
        }
    }
    
    // 家庭组基本信息
    struct GroupInfo: Codable {
        let groupId: String
        let groupName: String
        let ownerId: String
        let description: String
        let maxMembers: Int
        let createdAt: String
        let updatedAt: String
        
        enum CodingKeys: String, CodingKey {
            case groupId = "group_id"
            case groupName = "group_name"
            case ownerId = "owner_id"
            case description
            case maxMembers = "max_members"
            case createdAt = "created_at"
            case updatedAt = "updated_at"
        }
    }
    
    // 家庭组成员信息
    struct MemberInfo: Codable {
        let id: Int
        let groupId: String
        let userId: String
        let nickname: String
        let role: Int
        let joinTime: String
        let updatedAt: String
        let userInfo: UserInfo
        
        enum CodingKeys: String, CodingKey {
            case id
            case groupId = "group_id"
            case userId = "user_id"
            case nickname
            case role
            case joinTime = "join_time"
            case updatedAt = "updated_at"
            case userInfo = "user_info"
        }
    }
    
    // 用户信息
    struct UserInfo: Codable {
        let userId: String
        let logtoId: String
        let username: String
        let email: String
        let phone: String
        let nickname: String
        let status: Int
        let createdAt: String
        let updatedAt: String
        
        enum CodingKeys: String, CodingKey {
            case userId = "user_id"
            case logtoId = "logto_id"
            case username
            case email
            case phone
            case nickname
            case status
            case createdAt = "created_at"
            case updatedAt = "updated_at"
        }
    }
    
    // 设备信息
    struct DeviceInfo: Codable {
        let id: Int
        let groupId: String
        let deviceId: String
        let addedBy: String
        let createdAt: String
        let updatedAt: String
        let deviceInfo: DeviceDetailInfo
        
        enum CodingKeys: String, CodingKey {
            case id
            case groupId = "group_id"
            case deviceId = "device_id"
            case addedBy = "added_by"
            case createdAt = "created_at"
            case updatedAt = "updated_at"
            case deviceInfo = "device_info"
        }
    }
    
    // 设备详细信息
    struct DeviceDetailInfo: Codable {
        let deviceId: String
        let userId: String
        let hardwareSn: String
        let name: String
        let model: String
        let timezone: String
        let firmwareVersion: String
        let status: Int
        let lastHeartbeat: String?
        let lastActive: String?
        let createdAt: String
        let updatedAt: String
        
        enum CodingKeys: String, CodingKey {
            case deviceId = "device_id"
            case userId = "user_id"
            case hardwareSn = "hardware_sn"
            case name
            case model
            case timezone
            case firmwareVersion = "firmware_version"
            case status
            case lastHeartbeat = "last_heartbeat"
            case lastActive = "last_active"
            case createdAt = "created_at"
            case updatedAt = "updated_at"
        }
    }
}

// 扩展：获取成员角色的可读字符串
extension FamilyGroupDetailResponse.MemberInfo {
    var roleString: String {
        switch role {
        case 2:
            return "拥有者"
        case 1:
            return "管理员"
        case 0:
            return "成员"
        default:
            return "未知角色"
        }
    }
    
    var roleColor: Color {
        switch role {
        case 2:
            return .red
        case 1:
            return .orange
        case 0:
            return .blue
        default:
            return .gray
        }
    }
}

// 扩展：获取设备状态的可读字符串
extension FamilyGroupDetailResponse.DeviceDetailInfo {
    var statusString: String {
        status == 1 ? "在线" : "离线"
    }
    
    var statusColor: Color {
        status == 1 ? .green : .red
    }
}
