// translate to english
### 🎉 New Features
- **Cat Avatar Management**：New avatar upload and management feature
  - Support taking photos from camera or selecting from photo library
  - Built-in image editor (crop, zoom, rotate)
  - Can select avatar from validated photos
  - Automatically optimized to 512x512 high-quality image

- **Enhanced Cat Profile Creation**：
  - New dedicated avatar selection step
  - Optimized 6-step creation process
  - More intuitive user experience

### 🔧 Function Improvements
- **Network Request Optimization**：
  - Unified authentication mechanism, improve API call stability
  - Fix device registration 401 error problem
  - Improve token refresh logic

- **Avatar Display Enhancement**：
  - Display real avatar in cat list
  - Support authenticated avatar loading
  - Pull-to-refresh avatar function

- **User Interface Optimization**：
  - More fluid animation effects
  - Improved error handling
  - Optimized loading status display

### 🐛 Bug Fixes
- Fix weight information not displayed in edit mode
- Fix avatar authentication loading failure
- Fix network request method deprecated warning
- Fix UI state initialization problem

### 🔒 Security Improvements
- Improved API authentication mechanism
- More secure image upload processing
- Optimized error information handling