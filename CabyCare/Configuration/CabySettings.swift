import SwiftUI

// 猫咪行为类型
enum BehaviorType: String, CaseIterable {
    case excretion = "排泄"
    case normal = "日常"

    var icon: String {
        switch self {
        case .excretion: return "pawprint.circle.fill"
        case .normal: return "cat.fill"
        }
    }
}

// 排泄类型
enum ExcretionType: String, CaseIterable {
    case urination = "小便"
    case defecation = "大便"
    case both = "大小便"

    // 修改为返回图标数组
    var icons: [String] {
        switch self {
        case .urination: return ["drop.fill"]
        case .defecation: return ["circle.fill"]
        case .both: return ["drop.fill", "circle.fill"]  // 同时返回两个图标
        }
    }

    // 修改为返回颜色数组，与图标一一对应
    var colors: [Color] {
        switch self {
        case .urination: return [.yellow]
        case .defecation: return [.brown]
        case .both: return [.yellow, .brown]  // 对应两个图标的颜色
        }
    }
}

// 测试数据生成器
struct TestDataGenerator {
    // 使用 UUID 作为种子来确保相同 ID 的视频段生成相同的猫咪类型
    static func randomCat(forId id: String) -> CatType {
        var hasher = Hasher()
        hasher.combine(id)
        let seed = hasher.finalize()

        var generator = SeededRandomNumberGenerator(seed: UInt64(bitPattern: Int64(seed)))
        return CatType.allCases.randomElement(using: &generator) ?? .white
    }

    // 使用 UUID 作为种子来确保相同 ID 的视频段生成相同的行为类型
    static func randomBehavior(forId id: String) -> BehaviorType {
        // 使用视频 ID 作为随机种子
        var hasher = Hasher()
        hasher.combine(id)
        let seed = hasher.finalize()

        // 使用固定种子生成随机值
        var generator = SeededRandomNumberGenerator(seed: UInt64(bitPattern: Int64(seed)))
        return Bool.random(using: &generator) ? .excretion : .normal
    }

    static func randomExcretion(forId id: String) -> ExcretionType? {
        // 使用不同的哈希组合来生成排泄类型
        var hasher = Hasher()
        hasher.combine(id)
        hasher.combine("excretion") // 添加额外字符串使其与行为类型哈希值不同
        let seed = hasher.finalize()

        var generator = SeededRandomNumberGenerator(seed: UInt64(bitPattern: Int64(seed)))
        return ExcretionType.allCases.randomElement(using: &generator)
    }
}

// 添加一个可重复的随机数生成器
struct SeededRandomNumberGenerator: RandomNumberGenerator {
    private let multiplier: UInt64 = 6364136223846793005
    private let increment: UInt64 = 1442695040888963407
    private var state: UInt64

    init(seed: UInt64) {
        state = seed
    }

    mutating func next() -> UInt64 {
        state = state &* multiplier &+ increment
        return state
    }
}
