import Foundation

/// HomeView 相关的配置常量
struct HomeConstants {
    /// 如厕警告阈值 - 多少小时未如厕时显示警告
    static let toiletAlertThresholdHours: Int = 12
    
    /// 统计分析的时间范围 - 当前24小时
    static let currentAnalysisHours: Int = 24
    
    /// 对比分析的时间范围 - 前24小时
    static let comparisonAnalysisHours: Int = 24
    
    /// 总分析时间范围（当前 + 对比）
    static let totalAnalysisHours: Int = currentAnalysisHours + comparisonAnalysisHours
    
    /// 刷新间隔（秒） - 避免频繁刷新
    static let refreshIntervalSeconds: TimeInterval = 300 // 5分钟
    
    /// 体重变化显示精度（小数位数）
    static let weightChangeDecimalPlaces: Int = 1
    
    /// 体重显示精度（小数位数）
    static let weightDisplayDecimalPlaces: Int = 1
    
    /// 如厕次数变化阈值 - 超过这个数值才显示特殊颜色
    static let toiletCountChangeThreshold: Int = 4
    
    /// 体重变化阈值（百分比） - 超过这个比例才显示特殊颜色
    static let weightChangeThreshold: Double = 2.0
}

/// 扩展：提供便捷的时间计算方法
extension HomeConstants {
    /// 获取当前时间之前指定小时数的时间点
    static func hoursAgo(_ hours: Int, from date: Date = Date()) -> Date {
        return Calendar.current.date(byAdding: .hour, value: -hours, to: date) ?? date
    }
    
    /// 获取如厕警告的时间阈值
    static var toiletAlertThreshold: Date {
        return hoursAgo(toiletAlertThresholdHours)
    }
    
    /// 获取当前24小时的开始时间
    static var currentPeriodStart: Date {
        return hoursAgo(currentAnalysisHours)
    }
    
    /// 获取对比期间的开始时间（48小时前）
    static var comparisonPeriodStart: Date {
        return hoursAgo(totalAnalysisHours)
    }
} 