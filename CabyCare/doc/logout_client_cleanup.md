# 登出时客户端清理功能

## 概述

当用户登出时，系统会自动清理客户端相关的数据，包括推送令牌和客户端绑定关系，确保用户不会在登出后继续收到推送通知。

## 实现细节

### 1. NotificationManager 新增方法

#### `deleteClientToken()` - 删除客户端推送令牌
- **功能**: 删除服务器上存储的客户端推送令牌
- **API**: `DELETE /api/clients/{clientId}/token`
- **错误处理**: 404 错误被视为成功（令牌已不存在）

#### `deleteClientBinding()` - 删除客户端绑定关系
- **功能**: 删除服务器上的客户端绑定关系
- **API**: `DELETE /api/clients/{clientId}`
- **错误处理**: 404 错误被视为成功（绑定关系已不存在）

#### `cleanupClientData()` - 完整的客户端清理
- **功能**: 执行完整的客户端数据清理流程
- **步骤**:
  1. 删除推送令牌
  2. 删除客户端绑定关系
  3. 清理视频相关缓存
  4. 清理设备管理器缓存
  5. 清理动物界面缓存
  6. 清除本地存储的设备令牌

#### `cleanupVideoCache()` - 清理视频缓存
- **功能**: 清理所有视频相关的缓存数据
- **清理内容**:
  - VideoDataCache（视频数据缓存）
  - VideoCacheManager（视频文件缓存）
  - URLSession 缓存

#### `cleanupDeviceCache()` - 清理设备管理器缓存
- **功能**: 清理设备管理器中的缓存数据
- **清理内容**:
  - 设备视频片段缓存
  - 设备可用日期缓存
  - 设备加载时间缓存

#### `cleanupAnimalCache()` - 清理动物界面缓存
- **功能**: 清理动物界面相关的缓存数据
- **清理内容**:
  - CatManager 中的猫咪列表
  - UserDefaults 中的猫咪缓存数据

### 2. AuthManager 登出流程更新

修改后的 `logout()` 方法执行顺序：

1. **客户端清理** - 调用 `NotificationManager.shared.cleanupClientData()`
2. **清除凭证** - 调用 `storage.clearAuthCredentials()`
3. **清除 WebView 数据** - 调用 `clearWebViewData()`
4. **发送通知** - 通知其他组件用户已登出

## API 接口

### 删除客户端推送令牌

```http
DELETE /api/clients/{clientId}/token
Authorization: Bearer {access_token}
```

**响应**:
- `200 OK` - 删除成功
- `404 Not Found` - 令牌不存在（视为成功）
- `401 Unauthorized` - 认证失败

### 删除客户端绑定关系

```http
DELETE /api/clients/{clientId}
Authorization: Bearer {access_token}
```

**响应**:
- `200 OK` - 删除成功
- `404 Not Found` - 客户端不存在（视为成功）
- `401 Unauthorized` - 认证失败

## 错误处理

- **网络错误**: 记录警告日志，不阻止登出流程
- **认证错误**: 记录警告日志，继续执行后续清理步骤
- **404 错误**: 视为成功，因为目标资源已不存在

## 测试

### 开发者菜单测试

在开发者菜单中提供了以下测试选项：

1. **测试视频缓存清理** - 单独测试视频缓存清理功能
2. **测试动物界面缓存清理** - 单独测试动物界面缓存清理功能
3. **测试客户端清理** - 测试完整的客户端清理功能
4. **清除用户数据** - 测试完整的登出流程

### 测试步骤

1. 确保用户已登录并注册了推送通知
2. 使用应用一段时间，确保有视频缓存和动物数据缓存
3. 在开发者菜单中分别测试各种缓存清理功能：
   - 点击"测试视频缓存清理"测试视频缓存清理
   - 点击"测试动物界面缓存清理"测试动物界面缓存清理
   - 点击"测试客户端清理"测试完整的客户端清理
4. 查看控制台日志，确认清理步骤执行成功
5. 验证相应的缓存数据已被清除
6. 最后测试完整的登出流程，确保所有数据都被清理

## 日志输出

清理过程中会输出以下日志：

```
🧹 开始清理客户端数据...
✅ 客户端推送令牌已删除
✅ 客户端绑定关系已删除
🧹 开始清理视频缓存...
✅ VideoDataCache 已清除
✅ VideoCacheManager 已清除
✅ URLSession 缓存已清除
✅ 视频缓存清理完成
🧹 开始清理设备管理器缓存...
✅ DeviceManager 缓存已清除
✅ 设备管理器缓存清理完成
🧹 开始清理动物界面缓存...
✅ CatManager 缓存已清除
✅ 动物界面缓存清理完成
✅ 客户端数据清理完成
�� 用户已登出，所有数据已清除
```

## 注意事项

1. **执行顺序**: 客户端清理必须在清除访问令牌之前执行，因为删除操作需要有效的认证令牌
2. **异步操作**: 所有网络请求都是异步执行的，不会阻塞 UI
3. **容错性**: 即使清理操作失败，登出流程仍会继续执行
4. **设备标识**: 使用 `UIDevice.current.identifierForVendor` 作为客户端 ID

## 相关文件

- `CabyCare/Managers/NotificationManager.swift` - 客户端清理方法实现
- `CabyCare/Auth/AuthManager.swift` - 登出流程实现
- `CabyCare/Views/DeveloperMenuView.swift` - 测试功能
- `CabyCare/Configuration.swift` - API 路径配置 