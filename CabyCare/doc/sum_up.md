# CabyCare 项目设计文档

## 1. 项目概述

### 项目名称
CabyCare - 智能宠物关爱应用

### 项目描述
CabyCare 是一款专为宠物主人设计的智能监护应用，通过连接智能摄像设备，为用户提供实时的宠物监控、健康管理、设备协作等功能。应用支持多用户家庭组协作，让全家人共同关爱宠物。

### 核心价值
- **实时监控**: 24/7宠物状态监控和录像回放
- **智能分析**: 基于AI的宠物行为分析和健康提醒
- **家庭协作**: 多用户家庭组管理和权限控制
- **设备管理**: 智能设备的统一管理和配置

## 2. 功能架构设计

### 2.1 应用架构
```
┌─────────────────────────────────────┐
│           CabyCare App              │
├─────────────────────────────────────┤
│  认证层 (Authentication Layer)        │
├─────────────────────────────────────┤
│  业务逻辑层 (Business Logic Layer)    │
│  ├─ VideoPlayerViewModel            │
│  ├─ DeviceManager                   │
│  ├─ AuthManager                     │
│  └─ NotificationManager             │
├─────────────────────────────────────┤
│  数据层 (Data Layer)                 │
│  ├─ NetworkManager                  │
│  ├─ CacheManager                    │
│  └─ UserDefaultsManager             │
├─────────────────────────────────────┤
│  UI层 (Presentation Layer)          │
│  ├─ SwiftUI Views                   │
│  ├─ ViewModels                      │
│  └─ Components                      │
└─────────────────────────────────────┘
```

### 2.2 核心功能模块

#### 模块1: 用户认证 (Authentication)
- **功能**: OAuth2.0认证、令牌管理、自动刷新
- **技术栈**: Logto SDK、JWT Token
- **界面**: 登录页面、权限管理

#### 模块2: 视频监控 (Video Monitoring)
- **功能**: 实时播放、历史回放、视频段管理
- **技术栈**: AVPlayer、HLS流媒体、视频缓存
- **界面**: 视频播放器、全屏播放、时间轴控制

#### 模块3: 宠物管理 (Pet Management)
- **功能**: 宠物档案、健康记录、行为统计
- **数据模型**: Cat Profile、健康状态、活动等级
- **界面**: 宠物列表、档案编辑、健康仪表板

#### 模块4: 设备管理 (Device Management)
- **功能**: 设备注册、状态监控、固件管理
- **数据模型**: Device、设备状态、网络连接
- **界面**: 设备列表、设备详情、状态监控

#### 模块5: 家庭组协作 (Family Group)
- **功能**: 创建家庭组、成员邀请、权限管理
- **数据模型**: FamilyGroup、成员关系、设备共享
- **界面**: 家庭组管理、邀请系统、权限设置

#### 模块6: 通知系统 (Notification)
- **功能**: 推送通知、通知设置、消息历史
- **技术栈**: APNs、本地通知、通知权限
- **界面**: 通知设置、消息中心

## 3. 界面设计规范

### 3.1 设计系统

#### 颜色方案
```swift
// 主色调
Primary Light: #themePrimary
Primary Dark: #themeSecondary

// 背景色
Background Light: #FFFFFF
Background Dark: #000000
Secondary Background Light: #F6F7F8
Secondary Background Dark: rgba(128,128,128,0.1)

// 文本色
Text Primary Light: #000000
Text Primary Dark: #FFFFFF
Text Secondary: #808080

// 功能色
Success: #28A745
Warning: #FFC107
Error: #DC3545
Info: #17A2B8
```

#### 字体系统
```swift
// 自定义字体
Brand Font: "Chillax" (Medium, Light)
- 标题: Chillax-Medium, 36pt
- 副标题: Chillax-Light, 18pt

// 系统字体
Body: SF Pro (System Font)
- 标题: .headline
- 正文: .body
- 说明文字: .caption
```

#### 间距系统
```swift
// 标准间距
Micro: 4pt
Small: 8pt
Medium: 16pt
Large: 24pt
XLarge: 32pt
XXLarge: 40pt

// 组件间距
Card Padding: 16pt
Section Spacing: 24pt
Screen Padding: 20pt
```

### 3.2 导航结构

#### 主导航 (Tab Navigation)
```
┌─────────────────────────────────────┐
│  🏠 首页    ❤️ 关爱    🐾 宠物    📰 发现    ⚙️ 设置  │
└─────────────────────────────────────┘
```

1. **首页 (Home)** - `house.fill`
   - 今日概览
   - 快速统计
   - 最近通知

2. **关爱 (Care)** - `heart.circle.fill`
   - 视频监控
   - 实时播放
   - 历史回放

3. **宠物 (Animals)** - `pawprint.circle.fill`
   - 宠物档案
   - 健康管理
   - 行为统计

4. **发现 (Discover)** - `newspaper.fill`
   - 功能介绍
   - 使用指南
   - 社区内容

5. **设置 (Settings)** - `gearshape.2.fill`
   - 设备管理
   - 家庭组管理
   - 账户设置

### 3.3 界面布局

#### 首页布局
```
┌─────────────────────────────────────┐
│  📊 今日概览                         │
│  ┌─────────┬─────────┬─────────┐     │
│  │ 互动时间  │ 喂食次数  │ 活跃度    │     │
│  │ 45分钟   │ 3次     │ 85%      │     │
│  └─────────┴─────────┴─────────┘     │
│                                     │
│  🔔 最近通知                         │
│  ┌─────────────────────────────────┐ │
│  │ 🐾 喂食提醒                      │ │
│  │    该给小咪喂食了...    5分钟前   │ │
│  ├─────────────────────────────────┤ │
│  │ ❤️ 互动提醒                      │ │
│  │    小咪正在寻找玩伴...  30分钟前  │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### 视频监控布局
```
┌─────────────────────────────────────┐
│  📹 视频播放器                       │
│  ┌─────────────────────────────────┐ │
│  │                                 │ │
│  │        视频画面区域               │ │
│  │                                 │ │
│  │  ⏸️  ──●───────── 🔊  ⛶        │ │
│  └─────────────────────────────────┘ │
│                                     │
│  📅 日期选择: 2024年1月24日          │
│                                     │
│  📋 视频记录列表                     │
│  ┌─────────────────────────────────┐ │
│  │ 🎬 09:15 - 排泄行为 (2分钟)      │ │
│  │ 🎬 11:30 - 进食行为 (5分钟)      │ │
│  │ 🎬 14:45 - 玩耍活动 (3分钟)      │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### 宠物管理布局
```
┌─────────────────────────────────────┐
│  🐱 我的宠物                         │
│  ┌─────────────────────────────────┐ │
│  │ 🐾 小咪 (3岁, 母猫, 三花)        │ │
│  │    健康状况: 良好                │ │
│  │    活跃度: 正常                  │ │
│  │    体重: 4.2kg                  │ │
│  └─────────────────────────────────┘ │
│                                     │
│  📊 健康统计                         │
│  ┌─────────────────────────────────┐ │
│  │ 本周活动: 85% ↗️                │ │
│  │ 进食频率: 正常 ✅                │ │
│  │ 睡眠质量: 良好 😴                │ │
│  └─────────────────────────────────┘ │
│                                     │
│  ➕ 添加新宠物                       │
└─────────────────────────────────────┘
```

#### 设备管理布局
```
┌─────────────────────────────────────┐
│  📱 设备管理    👥 家庭组             │
│  ┌─────────────────────────────────┐ │
│  │ 📹 客厅摄像头                    │ │
│  │ ● 在线  v2.1.0  CabyCam Pro    │ │
│  │                                 │ │
│  │ 📊 状态监控  ⚙️ 设备设置         │ │
│  └─────────────────────────────────┘ │
│                                     │
│  👨‍👩‍👧‍👦 我的家庭组                    │
│  ┌─────────────────────────────────┐ │
│  │ 🏠 温馨的家                      │ │
│  │    成员: 3人  设备: 2台          │ │
│  │    👤 爸爸, 👤 妈妈, 👤 我        │ │
│  └─────────────────────────────────┘ │
│                                     │
│  ➕ 邀请新成员    ➕ 添加设备         │
└─────────────────────────────────────┘
```

### 3.4 组件设计

#### 核心UI组件

1. **VideoPlayerView**
   - 视频播放控制
   - 全屏切换
   - 进度条和音量控制
   - 播放状态指示

2. **CatProfileCard**
   - 宠物基本信息
   - 健康状态图标
   - 快速操作按钮

3. **DeviceStatusCard**
   - 设备在线状态
   - 固件版本信息
   - 网络连接质量

4. **NotificationRow**
   - 通知图标和内容
   - 时间戳显示
   - 操作按钮

5. **StatCard**
   - 数值统计显示
   - 图标和标题
   - 趋势指示器

## 4. 数据模型设计

### 4.1 核心数据模型

#### 用户模型 (User)
```swift
struct User: Codable {
    let id: String
    let email: String
    let name: String
    let avatar: String?
    let createdAt: Date
    let updatedAt: Date
}
```

#### 宠物模型 (Cat)
```swift
struct CatProfile: Codable {
    let id: String
    let name: String
    let age: String
    let gender: CatGender
    let weight: String
    let type: CatType
    let healthStatus: CatHealthStatus
    let activityLevel: CatActivityLevel
    let birthDate: Date?
    let vaccinations: [Vaccination]
    let medications: [Medication]
}

enum CatGender: Codable {
    case male, female
}

enum CatType: String, Codable {
    case white = "小白"
    case black = "小黑"  
    case calico = "三花"
}

enum CatHealthStatus: String, Codable {
    case healthy = "健康"
    case needsAttention = "需要关注"
    case sick = "生病"
}
```

#### 设备模型 (Device)
```swift
struct Device: Codable {
    let deviceId: String
    let userId: String
    let hardwareSn: String
    let name: String
    let model: String
    let firmwareVersion: String
    let status: Int
    let lastHeartbeat: Date?
    let lastActive: Date?
    let createdAt: Date
    let updatedAt: Date
}
```

#### 家庭组模型 (FamilyGroup)
```swift
struct FamilyGroup: Codable {
    let id: String
    let name: String
    let description: String?
    let ownerId: String
    let members: [GroupMember]
    let devices: [String]
    let createdAt: Date
    let updatedAt: Date
}

struct GroupMember: Codable {
    let userId: String
    let name: String
    let role: MemberRole
    let joinedAt: Date
}

enum MemberRole: String, Codable {
    case owner = "owner"
    case admin = "admin"
    case member = "member"
}
```

#### 视频段模型 (VideoSegment)
```swift
struct VideoSegment: Codable {
    let id: String
    let deviceId: String
    let filename: String
    let path: String
    let size: Int64
    let duration: TimeInterval
    let startTime: Date
    let endTime: Date
    let type: SegmentType
    let thumbnail: String?
}

enum SegmentType: String, Codable {
    case motion = "motion"
    case manual = "manual"
    case scheduled = "scheduled"
}
```

### 4.2 API接口设计

#### 认证API
```
POST /auth/login           # 用户登录
POST /auth/refresh         # 刷新令牌
POST /auth/logout          # 用户登出
GET  /auth/profile         # 获取用户信息
```

#### 设备API
```
GET    /devices/accessible?user_id={userId}     # 获取用户可访问设备
GET    /devices/{deviceId}/status               # 获取设备状态
POST   /devices/register                        # 注册新设备
PUT    /devices/{deviceId}                      # 更新设备信息
DELETE /devices/{deviceId}                      # 删除设备
```

#### 视频API
```
GET /records/videos/list?path=device{deviceId}&start={start}&end={end}  # 获取视频列表
GET /records/videos/segment?path={segmentPath}                          # 获取视频段
```

#### 家庭组API
```
GET    /family-groups?user_id={userId}                    # 获取用户家庭组
POST   /family-groups                                     # 创建家庭组
GET    /family-groups/{groupId}?user_id={userId}         # 获取家庭组详情
PUT    /family-groups/{groupId}                          # 更新家庭组
DELETE /family-groups/{groupId}                          # 删除家庭组
POST   /family-groups/{groupId}/members                  # 添加成员
DELETE /family-groups/{groupId}/members/{memberId}       # 移除成员
POST   /family-groups/{groupId}/devices                  # 添加设备
DELETE /family-groups/{groupId}/devices/{deviceId}       # 移除设备
```

#### 宠物API
```
GET    /cats?user_id={userId}    # 获取用户宠物列表
POST   /cats                     # 创建宠物档案
GET    /cats/{catId}             # 获取宠物详情
PUT    /cats/{catId}             # 更新宠物信息
DELETE /cats/{catId}             # 删除宠物档案
```

#### 通知API
```
GET  /notifications/settings?user_id={userId}    # 获取通知设置
POST /notifications/settings                     # 创建通知设置
PUT  /notifications/settings/{settingId}         # 更新通知设置
```

## 5. 技术实现要求

### 5.1 核心技术栈

#### Flutter前端技术
- **UI框架**: Flutter (目标平台)
- **状态管理**: Provider/Riverpod/Bloc
- **路由管理**: GoRouter/AutoRoute
- **本地存储**: SharedPreferences/Hive/SQLite
- **网络请求**: Dio/HTTP
- **音视频播放**: video_player/chewie/better_player
- **国际化**: flutter_localizations/easy_localization
- **图片处理**: cached_network_image
- **权限管理**: permission_handler

#### 架构模式
- **设计模式**: MVVM/Clean Architecture
- **依赖注入**: get_it/injectable
- **响应式编程**: RxDart/Streams
- **错误处理**: Either/Result pattern

### 5.2 关键功能实现

#### 视频播放功能 (Flutter)
```dart
// Flutter视频播放器实现
class VideoPlayerWidget extends StatefulWidget {
  final String videoUrl;
  final VoidCallback? onFullscreen;
  
  @override
  _VideoPlayerWidgetState createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {
  late VideoPlayerController _controller;
  
  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.network(widget.videoUrl);
    _controller.initialize().then((_) {
      setState(() {});
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return _controller.value.isInitialized
        ? AspectRatio(
            aspectRatio: _controller.value.aspectRatio,
            child: VideoPlayer(_controller),
          )
        : Container(
            height: 200,
            child: Center(child: CircularProgressIndicator()),
          );
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
```

#### 认证管理 (Flutter)
```dart
class AuthService {
  final Dio _dio;
  final TokenStorage _tokenStorage;
  
  AuthService(this._dio, this._tokenStorage);
  
  Future<AuthResult> login() async {
    try {
      // OAuth2.0 认证流程
      final result = await _oauth2Client.requestAccessToken();
      await _tokenStorage.saveTokens(result.tokens);
      return AuthResult.success(result.user);
    } catch (e) {
      return AuthResult.failure(e.toString());
    }
  }
  
  Future<void> refreshToken() async {
    final refreshToken = await _tokenStorage.getRefreshToken();
    if (refreshToken != null) {
      final response = await _dio.post('/auth/refresh', data: {
        'refresh_token': refreshToken,
      });
      final newTokens = TokenModel.fromJson(response.data);
      await _tokenStorage.saveTokens(newTokens);
    }
  }
  
  Future<void> logout() async {
    await _tokenStorage.clearTokens();
    await _dio.post('/auth/logout');
  }
}
```

#### 实时数据同步 (Flutter)
```dart
class DeviceStatusService {
  final WebSocketChannel _channel;
  final StreamController<DeviceStatus> _statusController;
  
  Stream<DeviceStatus> watchDeviceStatus(String deviceId) {
    return _statusController.stream
        .where((status) => status.deviceId == deviceId);
  }
  
  Future<void> updateDeviceStatus(String deviceId) async {
    final response = await _apiService.getDeviceStatus(deviceId);
    _statusController.add(DeviceStatus.fromJson(response.data));
  }
  
  void _listenToWebSocket() {
    _channel.stream.listen((data) {
      final status = DeviceStatus.fromJson(jsonDecode(data));
      _statusController.add(status);
    });
  }
}
```

#### 状态管理 (Provider示例)
```dart
class VideoPlayerProvider extends ChangeNotifier {
  VideoPlayerController? _controller;
  List<VideoSegment> _segments = [];
  bool _isLoading = false;
  String? _error;
  
  // Getters
  VideoPlayerController? get controller => _controller;
  List<VideoSegment> get segments => _segments;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  // Methods
  Future<void> loadVideos(String deviceId, DateTime date) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      final result = await _videoService.getVideoSegments(deviceId, date);
      _segments = result;
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  void playVideo(String videoUrl) {
    _controller?.dispose();
    _controller = VideoPlayerController.network(videoUrl);
    _controller!.initialize().then((_) {
      notifyListeners();
    });
  }
}
```

### 5.3 平台适配

#### iOS/Android差异处理
- **权限管理**: 使用permission_handler统一处理平台权限
- **文件系统**: 使用path_provider处理平台特定路径
- **通知系统**: 分别配置iOS和Android的推送通知
- **网络安全**: iOS的ATS配置，Android的网络安全配置
- **深度链接**: 配置custom URL schemes和App Links

#### 响应式设计
```dart
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  
  const ResponsiveLayout({
    Key? key,
    required this.mobile,
    this.tablet,
    this.desktop,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= 1024 && desktop != null) {
          return desktop!;
        } else if (constraints.maxWidth >= 768 && tablet != null) {
          return tablet!;
        } else {
          return mobile;
        }
      },
    );
  }
}
```

### 5.4 性能优化

#### 视频播放优化
- **预加载机制**: 提前缓存视频段
- **内存管理**: 及时释放视频资源
- **网络优化**: 根据网络状况调整视频质量
- **后台播放**: 支持音频后台播放

#### 数据缓存策略
```dart
class CacheManager {
  final Map<String, dynamic> _memoryCache = {};
  final Box _hiveBox;
  
  CacheManager(this._hiveBox);
  
  T? get<T>(String key) {
    // 先从内存缓存获取
    if (_memoryCache.containsKey(key)) {
      return _memoryCache[key] as T?;
    }
    
    // 再从持久化缓存获取
    return _hiveBox.get(key) as T?;
  }
  
  Future<void> put<T>(String key, T value, {Duration? expiry}) async {
    _memoryCache[key] = value;
    await _hiveBox.put(key, value);
    
    if (expiry != null) {
      Timer(expiry, () => remove(key));
    }
  }
  
  Future<void> remove(String key) async {
    _memoryCache.remove(key);
    await _hiveBox.delete(key);
  }
}
```

## 6. 用户体验设计

### 6.1 交互流程

#### 首次使用流程
```
启动应用 → 登录认证 → 权限授权 → 设备配置 → 宠物档案创建 → 完成引导
```

#### 日常使用流程
```
打开应用 → 查看首页概览 → 进入视频监控 → 选择日期/时间 → 观看回放 → 检查通知
```

#### 设备管理流程
```
进入设置 → 设备管理 → 添加设备 → 扫描二维码 → 网络配置 → 完成绑定
```

#### 家庭协作流程
```
创建家庭组 → 邀请成员 → 分配权限 → 共享设备 → 协作管理
```

### 6.2 关键用户体验点

#### 视频播放体验
- **快速加载**: 优化视频加载速度，减少等待时间
- **流畅播放**: 确保视频播放的流畅性和稳定性
- **智能缓存**: 预测用户需求，提前缓存常用时间段
- **操作反馈**: 清晰的播放状态指示和操作反馈

#### 设备连接体验
- **简化配置**: 一键配置或扫码配置
- **状态反馈**: 实时显示设备连接状态
- **故障诊断**: 提供设备问题的自动诊断和解决建议
- **批量管理**: 支持多设备的批量操作

#### 数据安全体验
- **隐私保护**: 清晰的隐私政策和数据使用说明
- **访问控制**: 精细的权限管理和访问控制
- **数据备份**: 重要数据的云端备份和恢复
- **安全提醒**: 及时的安全状态提醒和建议

## 7. 国际化与本地化

### 7.1 支持语言
- **简体中文**: 主要用户群体
- **English**: 国际用户支持

### 7.2 本地化内容
- **界面文字**: 所有UI文字的多语言支持
- **日期时间**: 本地化的日期时间格式
- **数字格式**: 本地化的数字和货币格式
- **图标文化**: 考虑文化差异的图标选择

### 7.3 Flutter国际化实现
```dart
// pubspec.yaml
dependencies:
  flutter_localizations:
    sdk: flutter
  intl: ^0.18.0

// AppLocalizations类
class AppLocalizations {
  AppLocalizations(this.locale);
  
  final Locale locale;
  
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }
  
  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();
      
  String get homeTitle {
    switch (locale.languageCode) {
      case 'zh':
        return '首页';
      case 'en':
      default:
        return 'Home';
    }
  }
  
  String get careTitle {
    switch (locale.languageCode) {
      case 'zh':
        return '关爱';
      case 'en':
      default:
        return 'Care';
    }
  }
}

// 在MaterialApp中配置
MaterialApp(
  localizationsDelegates: [
    AppLocalizations.delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
  ],
  supportedLocales: [
    Locale('en', ''),
    Locale('zh', ''),
  ],
  home: HomeScreen(),
)
```

## 8. 测试策略

### 8.1 测试类型

#### 单元测试 (Unit Tests)
```dart
// 测试业务逻辑
void main() {
  group('AuthService Tests', () {
    late AuthService authService;
    late MockApiClient mockApiClient;
    
    setUp(() {
      mockApiClient = MockApiClient();
      authService = AuthService(mockApiClient);
    });
    
    test('should login successfully with valid credentials', () async {
      // Arrange
      when(mockApiClient.post(any, data: anyNamed('data')))
          .thenAnswer((_) async => MockResponse(data: {'token': 'test_token'}));
      
      // Act
      final result = await authService.login('email', 'password');
      
      // Assert
      expect(result.isSuccess, true);
      expect(result.token, 'test_token');
    });
  });
}
```

#### Widget测试
```dart
void main() {
  testWidgets('VideoPlayerWidget should display video controls', (tester) async {
    // Arrange
    const videoUrl = 'https://example.com/video.mp4';
    
    // Act
    await tester.pumpWidget(
      MaterialApp(
        home: VideoPlayerWidget(videoUrl: videoUrl),
      ),
    );
    await tester.pumpAndSettle();
    
    // Assert
    expect(find.byType(VideoPlayer), findsOneWidget);
    expect(find.byIcon(Icons.play_arrow), findsOneWidget);
  });
}
```

#### 集成测试
```dart
void main() {
  group('App Integration Tests', () {
    testWidgets('complete login flow', (tester) async {
      app.main();
      await tester.pumpAndSettle();
      
      // 点击登录按钮
      await tester.tap(find.byKey(Key('login_button')));
      await tester.pumpAndSettle();
      
      // 验证导航到主页
      expect(find.byType(HomeScreen), findsOneWidget);
    });
  });
}
```

### 8.2 测试工具
- **flutter_test**: 单元测试和Widget测试
- **integration_test**: 端到端集成测试
- **mockito**: Mock对象生成
- **golden_toolkit**: UI回归测试

## 9. 部署与发布

### 9.1 应用商店发布

#### iOS App Store
- Apple开发者账号
- App Store Connect配置
- 应用审核指南遵守
- TestFlight beta测试

#### Google Play Store
- Google Play Console
- 应用签名配置
- Play Store审核流程
- Internal/Closed testing

### 9.2 持续集成/持续部署

#### GitHub Actions示例
```yaml
name: Build and Test

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
    - run: flutter pub get
    - run: flutter test
    - run: flutter build apk

  build-ios:
    runs-on: macos-latest
    steps:
    - uses: actions/checkout@v3
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
    - run: flutter build ios --no-codesign
```

### 9.3 监控与分析
- **Firebase Crashlytics**: 崩溃监控
- **Firebase Analytics**: 用户行为分析
- **Firebase Performance**: 性能监控
- **Sentry**: 错误追踪和性能监控

## 10. 迁移计划与建议

### 10.1 迁移策略

#### 阶段1: 核心功能迁移 (4-6周)
- 用户认证系统
- 基础UI框架和导航
- 视频播放功能
- 设备管理基础功能

#### 阶段2: 高级功能迁移 (4-6周)
- 家庭组协作功能
- 宠物管理系统
- 通知系统
- 数据同步机制

#### 阶段3: 优化与完善 (3-4周)
- 性能优化
- UI/UX完善
- 测试覆盖
- 文档完善

### 10.2 技术选型建议

#### 状态管理
- **Riverpod**: 现代化的状态管理解决方案，类型安全，测试友好
- **Bloc**: 适合复杂业务逻辑，事件驱动的架构模式

#### 视频播放
- **better_player**: 功能丰富的视频播放器，支持多种格式和自定义控制
- **video_player**: Flutter官方视频播放器，简单易用

#### 网络请求
- **Dio**: 功能强大的HTTP客户端，支持拦截器、缓存等高级功能

#### 本地存储
- **Hive**: 高性能的本地数据库，适合缓存和配置存储
- **SQLite**: 关系型数据库，适合复杂数据关系

### 10.3 迁移注意事项

1. **数据模型转换**: 将Swift的数据模型转换为Dart的数据类
2. **API兼容性**: 确保Flutter应用与现有后端API的兼容性
3. **平台特定功能**: 使用platform channels处理iOS/Android特定功能
4. **性能监控**: 在迁移过程中持续监控应用性能
5. **用户体验**: 确保迁移后的用户体验不降低

## 11. 总结

CabyCare是一款功能丰富的智能宠物关爱应用，具备完整的视频监控、设备管理、家庭协作等功能。在迁移到Flutter平台时，需要重点关注：

1. **视频播放功能的跨平台实现**
2. **实时数据同步的性能优化**  
3. **响应式UI设计的平台适配**
4. **用户认证和安全机制的移植**
5. **国际化和本地化的完整支持**

通过合理的架构设计和技术选型，可以实现一个高质量的跨平台宠物关爱应用，为用户提供优秀的使用体验。该设计文档为Flutter重构提供了完整的技术规范和实现指导。
