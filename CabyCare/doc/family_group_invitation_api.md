# Family Group Invitation API Documentation

This document describes the API endpoints for managing family group invitations.

## Overview

Family group invitations allow administrators and owners of family groups to invite new members to join their groups. Invitations can be created by user ID or email address, and recipients can accept or reject them.

## Authentication

All endpoints require user authentication. The `user_id` parameter must be provided in query parameters to identify the requesting user.

## Endpoints

### 2. Create Family Group Invitation (by Email)

Creates a new invitation for a user identified by email address.

**Endpoint:** `POST /api/family-groups/{group_id}/invitations/email`

**Parameters:**
- `group_id` (path, required): The ID of the family group
- `user_id` (query, required): The ID of the user creating the invitation

**Request Body:**
```json
{
  "invitee_email": "<EMAIL>", // Required: Email of the user to invite
  "role": 2,                           // Optional: Role to assign, default: 3
  "expire_at": "2024-12-31T23:59:59Z"  // Optional: Expiration time (ISO8601)
}
```

**Response:** Same as create invitation by user ID.

**Error Responses:**
- `400 Bad Request`: Invalid email or user not found with that email
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Group not found or no user with specified email

### 3. Get Family Group Invitation Details

Retrieves details of a specific invitation.

**Endpoint:** `GET /api/family-groups/invitations/{invitation_id}`

**Parameters:**
- `invitation_id` (path, required): The ID of the invitation
- `user_id` (query, required): The ID of the requesting user

**Response:**
```json
{
  "status": "success",
  "message": "获取家庭组邀请成功",
  "data": {
    "invitation_id": "inv_123456789012345678",
    "group_id": "fg_123456789012345678",
    "inviter_id": "user_123456789012345678",
    "invitee_id": "user_123456789012345679",
    "status": 0,
    "role": 2,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z",
    "expire_at": "2024-12-31T23:59:59Z",
    "inviter_info": {
      "user_id": "user_123456789012345678",
      "username": "inviter",
      "nickname": "Inviter User"
    },
    "invitee_info": {
      "user_id": "user_123456789012345679",
      "username": "invitee",
      "nickname": "Invitee User"
    },
    "group_info": {
      "group_id": "fg_123456789012345678",
      "group_name": "My Family Group",
      "description": "A family group"
    }
  }
}
```

**Error Responses:**
- `403 Forbidden`: User not authorized to view this invitation
- `404 Not Found`: Invitation not found

### 4. List Received Invitations

Gets all invitations received by the current user.

**Endpoint:** `GET /api/family-groups/invitations/received`

**Parameters:**
- `user_id` (query, required): The ID of the user

**Response:**
```json
{
  "status": "success",
  "message": "获取收到的家庭组邀请成功",
  "data": [
    {
      "invitation_id": "inv_123456789012345678",
      "group_id": "fg_123456789012345678",
      "group_name": "My Family Group",
      "inviter_id": "user_123456789012345678",
      "inviter_name": "Inviter User",
      "invitee_id": "user_123456789012345679",
      "invitee_name": "Invitee User",
      "status": 0,
      "role": 2,
      "created_at": "2024-01-01T00:00:00Z",
      "expire_at": "2024-12-31T23:59:59Z"
    }
  ]
}
```

### 5. List Sent Invitations

Gets all invitations sent by the current user.

**Endpoint:** `GET /api/family-groups/invitations/sent`

**Parameters:**
- `user_id` (query, required): The ID of the user

**Response:** Same format as received invitations.

### 6. Process Invitation (Accept/Reject)

Accepts or rejects a received invitation.

**Endpoint:** `PUT /api/family-groups/invitations/{invitation_id}/process`

**Parameters:**
- `invitation_id` (path, required): The ID of the invitation
- `user_id` (query, required): The ID of the user processing the invitation
- `action` (query, required): Either "accept" or "reject"

**Response:**
```json
{
  "status": "success",
  "message": "接受家庭组邀请成功"  // or "拒绝家庭组邀请成功"
}
```

**Error Responses:**
- `400 Bad Request`: Invalid action parameter or invitation already processed
- `403 Forbidden`: User not authorized to process this invitation
- `404 Not Found`: Invitation not found

### 7. Cancel Invitation

Cancels a pending invitation (inviter or group admin/owner only).

**Endpoint:** `DELETE /api/family-groups/invitations/{invitation_id}/cancel`

**Parameters:**
- `invitation_id` (path, required): The ID of the invitation
- `user_id` (query, required): The ID of the user cancelling the invitation

**Response:**
```json
{
  "status": "success",
  "message": "取消家庭组邀请成功"
}
```

**Error Responses:**
- `400 Bad Request`: Invitation already processed
- `403 Forbidden`: User not authorized to cancel this invitation
- `404 Not Found`: Invitation not found

## Data Models

### Invitation Status Values
- `0`: Pending (待处理)
- `1`: Accepted (已接受)
- `2`: Rejected (已拒绝)
- `3`: Cancelled (已取消)

### Family Member Roles
- `1`: Owner (拥有者) - Full permissions
- `2`: Admin (管理员) - Can manage members and devices
- `3`: Normal (普通成员) - Basic access

## Permission Requirements

### Creating Invitations
- Must be a member of the family group
- Must have Admin (2) or Owner (1) role
- Cannot invite users who are already members
- Group must not be at member capacity

### Processing Invitations
- Only the invitee can accept or reject an invitation
- Invitation must be in pending status and not expired

### Cancelling Invitations
- Inviter can cancel their own invitations
- Group admins and owners can cancel any invitation in their group
- Invitation must be in pending status

### Viewing Invitations
- Users can view invitations they sent or received
- Group admins and owners can view any invitation in their group

## Examples

### Example 1: Create Invitation by Email
```bash
curl -X POST "http://localhost:8080/api/family-groups/fg_123/invitations/email?user_id=user_456" \
  -H "Content-Type: application/json" \
  -d '{
    "invitee_email": "<EMAIL>",
    "role": 3,
    "expire_at": "2024-12-31T23:59:59Z"
  }'
```

### Example 2: Accept Invitation
```bash
curl -X PUT "http://localhost:8080/api/family-groups/invitations/inv_789/process?user_id=user_456&action=accept"
```

### Example 3: List Received Invitations
```bash
curl -X GET "http://localhost:8080/api/family-groups/invitations/received?user_id=user_456"
```

## Error Handling

All endpoints return consistent error responses:

```json
{
  "error": "Error message description"
}
```

Common HTTP status codes:
- `200 OK`: Success
- `400 Bad Request`: Invalid request parameters
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server error

## Notes

1. Invitations automatically expire after 24 hours if no custom expiration is set
2. Only pending invitations can be processed or cancelled
3. Users cannot invite themselves to groups
4. Duplicate invitations to the same user for the same group are not allowed
5. Email invitations require the target user to have an account with that email address
