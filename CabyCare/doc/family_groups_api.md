# 家庭组 API 文档

本文档详细介绍了家庭组相关的所有 API 接口，包括家庭组基本操作、成员管理、设备管理和邀请系统。

## 目录

- [角色与权限](#角色与权限)
- [家庭组基本操作](#家庭组基本操作)
- [家庭组成员管理](#家庭组成员管理)
- [家庭组设备管理](#家庭组设备管理)
- [家庭组邀请系统](#家庭组邀请系统)
- [设备访问接口](#设备访问接口)
- [数据模型](#数据模型)

## 角色与权限

家庭组内成员有三种角色：

- **拥有者 (2)**: 拥有对家庭组的完全控制权，可添加/删除成员、更改角色，以及删除家庭组
- **管理员 (1)**: 可管理组设置、添加/删除成员和管理设备
- **普通成员 (0)**: 只能查看和使用共享的设备

## 家庭组基本操作

### 创建家庭组

创建一个新的家庭组，创建者自动成为组的拥有者。

- **路径**: `/api/family-groups`
- **方法**: `POST`
- **参数**: 
  - Query: `user_id` (必需) - 创建者用户ID
- **请求体**:
```json
{
  "group_name": "我的家庭",
  "description": "家庭设备共享组"
}
```
- **返回示例**:
```json
{
  "status": "success",
  "message": "创建家庭组成功",
  "data": {
    "group_id": "fg_123456789",
    "group_name": "我的家庭",
    "owner_id": "user_123",
    "description": "家庭设备共享组",
    "max_members": 8,
    "created_at": "2023-06-01T12:00:00Z",
    "updated_at": "2023-06-01T12:00:00Z"
  }
}
```

### 获取用户的家庭组列表

获取当前用户加入的所有家庭组列表。

- **路径**: `/api/family-groups`
- **方法**: `GET`
- **参数**:
  - Query: `user_id` (必需) - 用户ID
- **返回示例**:
```json
{
  "status": "success",
  "message": "获取家庭组列表成功",
  "data": [
    {
      "group_id": "fg_123456789",
      "group_name": "我的家庭",
      "owner_id": "user_123",
      "description": "家庭设备共享组",
      "max_members": 8,
      "member_count": 3,
      "device_count": 2,
      "created_at": "2023-06-01T12:00:00Z",
      "updated_at": "2023-06-01T12:00:00Z"
    }
  ]
}
```

### 获取家庭组详情

获取特定家庭组的详细信息，包括成员和设备列表。

- **路径**: `/api/family-groups/:group_id`
- **方法**: `GET`
- **参数**:
  - Path: `group_id` (必需) - 家庭组ID
  - Query: `user_id` (必需) - 查询者用户ID
- **返回示例**:
```json
{
  "status": "success",
  "message": "获取家庭组详情成功",
  "data": {
    "group": {
      "group_id": "fg_123456789",
      "group_name": "我的家庭",
      "owner_id": "user_123",
      "description": "家庭设备共享组",
      "max_members": 8,
      "created_at": "2023-06-01T12:00:00Z",
      "updated_at": "2023-06-01T12:00:00Z"
    },
    "members": [
      {
        "user_id": "user_123",
        "nickname": "爸爸",
        "role": 2,
        "joined_at": "2023-06-01T12:00:00Z",
        "user_info": {
          "user_id": "user_123",
          "username": "zhang_san",
          "email": "<EMAIL>",
          "nickname": "张三"
        }
      }
    ],
    "devices": [
      {
        "device_id": "dev_123",
        "added_by": "user_123",
        "added_at": "2023-06-01T12:00:00Z",
        "device_info": {
          "device_id": "dev_123",
          "name": "客厅猫厕所",
          "model": "CabyPro 2023",
          "firmware_version": "1.0.0"
        }
      }
    ]
  }
}
```

### 更新家庭组信息

更新家庭组的基本信息，需要管理员或拥有者权限。

- **路径**: `/api/family-groups/:group_id`
- **方法**: `PUT`
- **参数**:
  - Path: `group_id` (必需) - 家庭组ID
  - Query: `user_id` (必需) - 操作者用户ID
- **请求体**:
```json
{
  "group_name": "我的新家庭名称",
  "description": "更新后的描述"
}
```
- **返回示例**:
```json
{
  "status": "success",
  "message": "更新家庭组成功",
  "data": {
    "group_id": "fg_123456789",
    "group_name": "我的新家庭名称",
    "owner_id": "user_123",
    "description": "更新后的描述",
    "max_members": 8,
    "created_at": "2023-06-01T12:00:00Z",
    "updated_at": "2023-06-01T13:00:00Z"
  }
}
```

### 删除家庭组

删除家庭组，仅拥有者可执行。

- **路径**: `/api/family-groups/:group_id`
- **方法**: `DELETE`
- **参数**:
  - Path: `group_id` (必需) - 家庭组ID
  - Query: `user_id` (必需) - 操作者用户ID
- **返回示例**:
```json
{
  "status": "success",
  "message": "删除家庭组成功"
}
```

## 家庭组成员管理

### 获取家庭组成员列表

获取家庭组的所有成员列表。

- **路径**: `/api/family-groups/:group_id/members`
- **方法**: `GET`
- **参数**:
  - Path: `group_id` (必需) - 家庭组ID
  - Query: `user_id` (必需) - 查询者用户ID
- **返回示例**:
```json
{
  "status": "success",
  "message": "获取家庭组成员列表成功",
  "data": [
    {
      "user_id": "user_123",
      "nickname": "爸爸",
      "role": 2,
      "joined_at": "2023-06-01T12:00:00Z",
      "user_info": {
        "user_id": "user_123",
        "username": "zhang_san",
        "email": "<EMAIL>",
        "nickname": "张三"
      }
    },
    {
      "user_id": "user_456",
      "nickname": "妈妈",
      "role": 1,
      "joined_at": "2023-06-01T13:00:00Z",
      "user_info": {
        "user_id": "user_456",
        "username": "li_si",
        "email": "<EMAIL>",
        "nickname": "李四"
      }
    }
  ]
}
```

### 添加家庭组成员

直接添加成员到家庭组（不通过邀请系统），需要管理员或拥有者权限。

- **路径**: `/api/family-groups/:group_id/members`
- **方法**: `POST`
- **参数**:
  - Path: `group_id` (必需) - 家庭组ID
  - Query: `user_id` (必需) - 操作者用户ID
- **请求体**:
```json
{
  "user_id": "user_789",
  "nickname": "儿子",
  "role": 0
}
```
- **返回示例**:
```json
{
  "status": "success",
  "message": "添加家庭组成员成功",
  "data": {
    "group_id": "fg_123456789",
    "user_id": "user_789",
    "nickname": "儿子",
    "role": 0,
    "joined_at": "2023-06-01T14:00:00Z"
  }
}
```

### 更新家庭组成员

更新家庭组成员的昵称或角色，需要适当的权限。

- **路径**: `/api/family-groups/:group_id/members/:member_id`
- **方法**: `PUT`
- **参数**:
  - Path: 
    - `group_id` (必需) - 家庭组ID
    - `member_id` (必需) - 成员用户ID
  - Query: `user_id` (必需) - 操作者用户ID
- **请求体**:
```json
{
  "nickname": "小儿子",
  "role": 0
}
```
- **返回示例**:
```json
{
  "status": "success",
  "message": "更新家庭组成员成功",
  "data": {
    "group_id": "fg_123456789",
    "user_id": "user_789",
    "nickname": "小儿子",
    "role": 0,
    "joined_at": "2023-06-01T14:00:00Z"
  }
}
```

### 移除家庭组成员

从家庭组中移除成员，需要适当的权限。

- **路径**: `/api/family-groups/:group_id/members/:member_id`
- **方法**: `DELETE`
- **参数**:
  - Path: 
    - `group_id` (必需) - 家庭组ID
    - `member_id` (必需) - 要移除的成员用户ID
  - Query: `user_id` (必需) - 操作者用户ID
- **返回示例**:
```json
{
  "status": "success",
  "message": "移除家庭组成员成功"
}
```

## 家庭组设备管理

### 获取家庭组设备列表

获取家庭组下的所有设备列表。

- **路径**: `/api/family-groups/:group_id/devices`
- **方法**: `GET`
- **参数**:
  - Path: `group_id` (必需) - 家庭组ID
  - Query: `user_id` (必需) - 查询者用户ID
- **返回示例**:
```json
{
  "status": "success",
  "message": "获取家庭组设备列表成功",
  "data": [
    {
      "device_id": "dev_123",
      "added_by": "user_123",
      "added_at": "2023-06-01T12:00:00Z",
      "device_info": {
        "device_id": "dev_123",
        "name": "客厅猫厕所",
        "model": "CabyPro 2023",
        "firmware_version": "1.0.0"
      }
    },
    {
      "device_id": "dev_456",
      "added_by": "user_456",
      "added_at": "2023-06-01T13:00:00Z",
      "device_info": {
        "device_id": "dev_456",
        "name": "卧室猫厕所",
        "model": "CabyLite 2023",
        "firmware_version": "1.0.0"
      }
    }
  ]
}
```

### 添加设备到家庭组

将设备添加到家庭组，需要管理员或拥有者权限。

- **路径**: `/api/family-groups/:group_id/devices`
- **方法**: `POST`
- **参数**:
  - Path: `group_id` (必需) - 家庭组ID
  - Query: `user_id` (必需) - 操作者用户ID
- **请求体**:
```json
{
  "device_id": "dev_789"
}
```
- **返回示例**:
```json
{
  "status": "success",
  "message": "添加设备到家庭组成功",
  "data": {
    "group_id": "fg_123456789",
    "device_id": "dev_789",
    "added_by": "user_123",
    "added_at": "2023-06-01T15:00:00Z"
  }
}
```

### 从家庭组移除设备

从家庭组中移除设备，需要管理员或拥有者权限。

- **路径**: `/api/family-groups/:group_id/devices/:device_id`
- **方法**: `DELETE`
- **参数**:
  - Path: 
    - `group_id` (必需) - 家庭组ID
    - `device_id` (必需) - 设备ID
  - Query: `user_id` (必需) - 操作者用户ID
- **返回示例**:
```json
{
  "status": "success",
  "message": "从家庭组移除设备成功"
}
```

## 家庭组邀请系统

### 创建家庭组邀请

向用户发送加入家庭组的邀请，需要管理员或拥有者权限。

- **路径**: `/api/family-groups/:group_id/invitations`
- **方法**: `POST`
- **参数**:
  - Path: `group_id` (必需) - 家庭组ID
  - Query: `user_id` (必需) - 邀请者用户ID
- **请求体**:
```json
{
  "invitee_id": "user_789",
  "role": 0,
  "expire_at": "2023-06-08T12:00:00Z"  // 可选，默认24小时后过期
}
```
- **返回示例**:
```json
{
  "status": "success",
  "message": "创建家庭组邀请成功",
  "data": {
    "invitation_id": "inv_123456789",
    "group_id": "fg_123456789",
    "inviter_id": "user_123",
    "invitee_id": "user_789",
    "status": 0,
    "role": 0,
    "created_at": "2023-06-01T15:00:00Z",
    "expire_at": "2023-06-08T12:00:00Z"
  }
}
```

### 获取收到的邀请列表

获取当前用户收到的所有待处理邀请。

- **路径**: `/api/family-groups/invitations/received`
- **方法**: `GET`
- **参数**:
  - Query: `user_id` (必需) - 用户ID
- **返回示例**:
```json
{
  "status": "success",
  "message": "获取收到的邀请列表成功",
  "data": [
    {
      "invitation_id": "inv_123456789",
      "group_id": "fg_123456789",
      "group_name": "张三的家庭",
      "inviter_id": "user_123",
      "inviter_name": "张三",
      "invitee_id": "user_456",
      "invitee_name": "李四",
      "status": 0,
      "role": 0,
      "created_at": "2023-06-01T15:00:00Z",
      "expire_at": "2023-06-08T12:00:00Z"
    }
  ]
}
```

### 获取发送的邀请列表

获取当前用户发送的所有邀请。

- **路径**: `/api/family-groups/invitations/sent`
- **方法**: `GET`
- **参数**:
  - Query: `user_id` (必需) - 用户ID
- **返回示例**:
```json
{
  "status": "success",
  "message": "获取发送的邀请列表成功",
  "data": [
    {
      "invitation_id": "inv_123456789",
      "group_id": "fg_123456789",
      "group_name": "我的家庭",
      "inviter_id": "user_123",
      "inviter_name": "",
      "invitee_id": "user_456",
      "invitee_name": "李四",
      "status": 0,
      "role": 0,
      "created_at": "2023-06-01T15:00:00Z",
      "expire_at": "2023-06-08T12:00:00Z"
    }
  ]
}
```

### 获取邀请详情

获取特定邀请的详细信息。

- **路径**: `/api/family-groups/invitations/:invitation_id`
- **方法**: `GET`
- **参数**:
  - Path: `invitation_id` (必需) - 邀请ID
  - Query: `user_id` (必需) - 查询者用户ID
- **返回示例**:
```json
{
  "status": "success",
  "message": "获取邀请详情成功",
  "data": {
    "invitation_id": "inv_123456789",
    "group_id": "fg_123456789",
    "inviter_id": "user_123",
    "invitee_id": "user_456",
    "status": 0,
    "role": 0,
    "created_at": "2023-06-01T15:00:00Z",
    "expire_at": "2023-06-08T12:00:00Z",
    "inviter_info": {
      "user_id": "user_123",
      "username": "zhang_san",
      "email": "<EMAIL>",
      "nickname": "张三"
    },
    "invitee_info": {
      "user_id": "user_456",
      "username": "li_si",
      "email": "<EMAIL>",
      "nickname": "李四"
    },
    "group_info": {
      "group_id": "fg_123456789",
      "group_name": "我的家庭",
      "description": "家庭设备共享组"
    }
  }
}
```

### 处理邀请

接受或拒绝收到的邀请。

- **路径**: `/api/family-groups/invitations/:invitation_id/process`
- **方法**: `PUT`
- **参数**:
  - Path: `invitation_id` (必需) - 邀请ID
  - Query: 
    - `user_id` (必需) - 操作者用户ID
    - `action` (必需) - 操作类型："accept"接受或"reject"拒绝
- **返回示例**:
```json
{
  "status": "success",
  "message": "接受家庭组邀请成功"
}
```

### 取消邀请

取消已发送但未被处理的邀请。

- **路径**: `/api/family-groups/invitations/:invitation_id/cancel`
- **方法**: `DELETE`
- **参数**:
  - Path: `invitation_id` (必需) - 邀请ID
  - Query: `user_id` (必需) - 操作者用户ID
- **返回示例**:
```json
{
  "status": "success",
  "message": "取消家庭组邀请成功"
}
```

## 设备访问接口

### 获取用户可访问的设备列表

获取用户可访问的所有设备，包括个人设备和通过家庭组共享的设备。

- **路径**: `/api/devices/accessible`
- **方法**: `GET`
- **参数**:
  - Query: `user_id` (必需) - 用户ID
- **返回示例**:
```json
{
  "status": "success",
  "message": "获取可访问设备列表成功",
  "data": [
    {
      "device_id": "dev_123",
      "name": "客厅猫厕所",
      "model": "CabyPro 2023",
      "user_id": "user_123",
      "firmware_version": "1.0.0",
      "status": 1,
      "timezone": "Asia/Shanghai",
      "last_active": "2023-06-01T15:00:00Z"
    },
    {
      "device_id": "dev_456",
      "name": "卧室猫厕所",
      "model": "CabyLite 2023",
      "user_id": "user_456", // 表示这是通过家庭组访问的其他用户设备
      "firmware_version": "1.0.0",
      "status": 1,
      "timezone": "Asia/Shanghai",
      "last_active": "2023-06-01T15:00:00Z"
    }
  ]
}
```

## 数据模型

### FamilyGroup - 家庭组

```go
type FamilyGroup struct {
    GroupID     string    // 家庭组ID
    GroupName   string    // 家庭组名称
    OwnerID     string    // 拥有者用户ID
    Description string    // 描述
    MaxMembers  int       // 最大成员数
    CreatedAt   time.Time // 创建时间
    UpdatedAt   time.Time // 更新时间
}
```

### FamilyGroupMember - 家庭组成员

```go
type FamilyGroupMember struct {
    GroupID   string    // 家庭组ID
    UserID    string    // 成员用户ID
    Nickname  string    // 在组内的昵称
    Role      int8      // 角色: 0=普通成员, 1=管理员, 2=拥有者
    JoinedAt  time.Time // 加入时间
    UserInfo  *User     // 用户信息(非数据库字段)
}
```

### FamilyGroupDevice - 家庭组设备关联

```go
type FamilyGroupDevice struct {
    GroupID    string    // 家庭组ID
    DeviceID   string    // 设备ID
    AddedBy    string    // 添加者用户ID
    AddedAt    time.Time // 添加时间
    DeviceInfo *Device   // 设备信息(非数据库字段)
}
```

### FamilyGroupInvitation - 家庭组邀请

```go
type FamilyGroupInvitation struct {
    InvitationID string     // 邀请ID
    GroupID      string     // 家庭组ID
    InviterID    string     // 邀请者用户ID
    InviteeID    string     // 被邀请者用户ID
    Status       int8       // 状态: 0=待处理, 1=已接受, 2=已拒绝, 3=已过期
    Role         int8       // 邀请的角色: 0=普通成员, 1=管理员
    CreatedAt    time.Time  // 创建时间
    UpdatedAt    time.Time  // 更新时间
    ExpireAt     *time.Time // 过期时间
    
    // 非数据库字段
    InviterInfo *User        // 邀请者信息
    InviteeInfo *User        // 被邀请者信息
    GroupInfo   *FamilyGroup // 家庭组信息
}
```

### FamilyGroupInvitationResponse - 家庭组邀请响应

```go
type FamilyGroupInvitationResponse struct {
    InvitationID string     // 邀请ID
    GroupID      string     // 家庭组ID
    GroupName    string     // 家庭组名称
    InviterID    string     // 邀请者用户ID
    InviterName  string     // 邀请者用户名
    InviteeID    string     // 被邀请者用户ID
    InviteeName  string     // 被邀请者用户名
    Status       int8       // 状态
    Role         int8       // 角色
    CreatedAt    time.Time  // 创建时间
    ExpireAt     *time.Time // 过期时间
}
```
