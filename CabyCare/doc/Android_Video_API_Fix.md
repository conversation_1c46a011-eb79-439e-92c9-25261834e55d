# Android版本视频API修复方案

## 问题描述

Android版本的视频获取API调用参数不正确，导致服务器返回400错误。根据iOS版本的正确实现和API文档，需要修改API调用参数。

## 错误的API调用（Android当前使用）

```
GET https://api.caby.care/api/records/videos/list?device_id=202502270220f7cbb4421000&start_date=2025-07-08&end_date=2025-07-10&timezone=Asia%2FShanghai
```

**问题：**
- ❌ 参数名错误：`device_id` 应为 `path`
- ❌ 参数名错误：`start_date` 应为 `start`  
- ❌ 参数名错误：`end_date` 应为 `end`
- ❌ 不需要的参数：`timezone` 应删除
- ❌ path值格式错误：应为 `device{设备ID}` 而不是单独的设备ID

## 正确的API调用（应修改为）

```
GET https://api.caby.care/api/records/videos/list?path=device202502270220f7cbb4421000&start=2025-07-08&end=2025-07-10
```

**修改点：**
- ✅ `device_id=202502270220f7cbb4421000` → `path=device202502270220f7cbb4421000`
- ✅ `start_date=2025-07-08` → `start=2025-07-08`
- ✅ `end_date=2025-07-10` → `end=2025-07-10`
- ✅ 删除 `timezone=Asia%2FShanghai` 参数

## Android代码修改示例

### 1. 如果使用字符串拼接

```kotlin
// 修改前（错误）
val url = "${Constants.API_BASE_URL}/api/records/videos/list" +
    "?device_id=$deviceId" +
    "&start_date=$startDate" +
    "&end_date=$endDate" +
    "&timezone=Asia%2FShanghai"

// 修改后（正确）
val url = "${Constants.API_BASE_URL}/api/records/videos/list" +
    "?path=device$deviceId" +
    "&start=$startDate" +
    "&end=$endDate"
```

### 2. 如果使用Retrofit或OkHttp

```kotlin
// 修改前（错误）
@GET("api/records/videos/list")
suspend fun getVideoList(
    @Query("device_id") deviceId: String,
    @Query("start_date") startDate: String,
    @Query("end_date") endDate: String,
    @Query("timezone") timezone: String = "Asia/Shanghai"
): List<VideoSegment>

// 修改后（正确）
@GET("api/records/videos/list")
suspend fun getVideoList(
    @Query("path") path: String,  // 传入 "device$deviceId"
    @Query("start") startDate: String,
    @Query("end") endDate: String
): List<VideoSegment>
```

### 3. 调用方式修改

```kotlin
// 修改前（错误）
val videoList = apiService.getVideoList(
    deviceId = "202502270220f7cbb4421000",
    startDate = "2025-07-08",
    endDate = "2025-07-10"
)

// 修改后（正确）
val videoList = apiService.getVideoList(
    path = "device202502270220f7cbb4421000",  // 注意添加"device"前缀
    startDate = "2025-07-08",
    endDate = "2025-07-10"
)
```

## 验证修复

修复后，API应该返回HTTP 200状态码和类似以下格式的JSON数据：

```json
[
  {
    "start": 1742204800,
    "duration": "45.5",
    "url": "https://api.caby.care/api/records/videos/get?path=records/device202502270220f7cbb4421000&start=2025-07-08T08:00:00Z&duration=45.5",
    "weight_litter": 2.5,
    "weight_cat": 4.2,
    "weight_waste": 0.3,
    "thumbnail_url": "https://api.caby.care/api/records/videos/thumbnail/2025-07-08T08%3A00%3A00%2B08%3A00?bucket=records/device202502270220f7cbb4421000",
    "animal_id": "cat_001"
  }
]
```

## 测试验证

可以使用以下curl命令测试修复效果：

```bash
curl -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
"https://api.caby.care/api/records/videos/list?path=device202502270220f7cbb4421000&start=2025-07-08&end=2025-07-10"
```

## 重要提醒

1. **path参数格式**：必须是 `device` + 设备ID，如 `device202502270220f7cbb4421000`
2. **日期格式**：必须是 `YYYY-MM-DD` 格式，如 `2025-07-08`
3. **时区处理**：服务器端会根据设备的时区设置自动处理，不需要客户端传递timezone参数
4. **空数据处理**：如果某个时间范围内没有视频，API会返回空数组 `[]`，这是正常情况，不是错误

## 参考

- iOS版本正确实现：`CabyCare/Configuration/Configuration.swift` 第47-56行
- API文档：`doc/Video_API_Documentation.md` 