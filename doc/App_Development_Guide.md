# 智能猫砂盆APP开发指南

## 📱 APP功能概述

这是一个智能猫砂盆监控系统的移动应用，主要功能包括：
- 猫咪健康监控和数据分析
- 实时设备状态查看
- 视频录像回放
- 家庭成员协作
- 智能提醒和通知

## 🚀 开发流程

### 1. 认证集成
```
用户登录 -> Logto第三方认证 -> 获取Token -> 访问API
```

**关键步骤**:
1. 集成Logto SDK
2. 处理登录回调 `GET /api/callback`
3. 存储access_token和refresh_token
4. 实现自动刷新机制 `POST /api/refresh`

**示例流程**:
```javascript
// 1. 用户点击登录
loginWithLogto()

// 2. 处理回调
handleCallback(code, state, codeVerifier) {
  call GET /api/callback?code=xxx&state=xxx&code_verifier=xxx
  save tokens to storage
}

// 3. 自动刷新
refreshToken() {
  call POST /api/refresh with refresh_token
  update stored tokens
}
```

### 2. 主要页面结构

#### 🏠 首页 (Dashboard)
- **数据源**: 
  - `GET /api/cats` - 获取猫咪列表
  - `GET /api/devices` - 获取设备列表
  - `GET /api/records?limit=10` - 最近记录
- **显示内容**:
  - 猫咪卡片（头像、姓名、健康状态）
  - 设备状态（在线/离线、电量）
  - 今日如厕次数
  - 最近异常提醒

#### 🐱 猫咪管理
- **猫咪列表**: `GET /api/cats`
- **添加猫咪**: `POST /api/cats`
- **编辑猫咪**: `PUT /api/cats/{cat_id}`
- **猫咪详情**: `GET /api/cats/{cat_id}`
- **健康统计**: `GET /api/cats/{cat_id}/metrics/daily`

#### 📱 设备管理
- **设备列表**: `GET /api/devices`
- **设备详情**: `GET /api/devices/{device_id}`
- **设备配置**: `GET/PUT /api/devices/{device_id}/config`
- **OTA更新**: `GET/PUT /api/devices/{device_id}/ota-status`

#### 📹 视频回放
- **视频列表**: `GET /api/records/videos/list`
- **播放视频**: `GET /api/records/videos/get`
- **缩略图**: `GET /api/records/videos/thumbnail/{folder}`

#### 👨‍👩‍👧‍👦 家庭共享
- **家庭组**: `GET /api/family-groups`
- **成员管理**: `GET /api/family-groups/{group_id}/members`
- **邀请功能**: `POST /api/family-groups/{group_id}/invitations`

## 🛠 技术实现建议

### 3. 数据管理策略

#### 本地缓存
```javascript
// 缓存用户信息
const userCache = {
  userInfo: null,
  cats: [],
  devices: [],
  lastUpdate: null
}

// 缓存策略
const cachePolicy = {
  userInfo: '1 hour',
  cats: '30 minutes', 
  devices: '5 minutes',
  records: '1 minute'
}
```

#### 数据同步
```javascript
// 启动时同步
async function syncOnAppStart() {
  await refreshUserInfo()
  await loadCats()
  await loadDevices()
  await checkPendingNotifications()
}

// 后台同步
function backgroundSync() {
  if (isOnline && tokenValid) {
    syncCriticalData()
  }
}
```

### 4. 实时更新机制

#### 轮询策略
```javascript
// 不同数据的轮询频率
const pollIntervals = {
  deviceStatus: 30000,    // 30秒
  recentRecords: 60000,   // 1分钟  
  notifications: 120000   // 2分钟
}
```

#### WebSocket (可选)
如果后端支持WebSocket，可以实现实时推送：
- 设备状态变化
- 新的如厕记录
- 实时通知

### 5. 推送通知集成

#### 客户端注册
```javascript
// 注册客户端
async function registerClient() {
  const clientInfo = {
    device_type: 'ios/android',
    app_version: '1.0.0',
    system_version: 'iOS 15.0'
  }
  
  const response = await api.post('/clients/register', clientInfo)
  return response.client_id
}

// 注册推送令牌
async function registerPushToken(clientId, token) {
  await api.post(`/clients/${clientId}/token`, {
    token: token,
    platform: 'apns/fcm'
  })
}
```

#### 通知处理
```javascript
// 处理收到的推送
function handleNotification(notification) {
  switch(notification.type) {
    case 'health_alert':
      navigateToHealthReport(notification.cat_id)
      break
    case 'device_offline':
      showDeviceAlert(notification.device_id)
      break
    case 'daily_report':
      showDailyReport()
      break
  }
}
```

### 6. 错误处理

#### 网络错误
```javascript
function handleApiError(error) {
  if (error.status === 401) {
    // Token过期，尝试刷新
    return refreshTokenAndRetry()
  } else if (error.status === 403) {
    // 权限不足
    showPermissionError()
  } else if (error.status >= 500) {
    // 服务器错误
    showServerError()
  }
}
```

#### 离线处理
```javascript
function handleOffline() {
  // 显示离线状态
  showOfflineIndicator()
  
  // 使用缓存数据
  loadFromCache()
  
  // 存储待同步操作
  queuePendingOperations()
}
```

## 📊 关键界面设计

### 7. 猫咪健康图表

#### 每日统计
```javascript
async function loadDailyMetrics(catId, date) {
  const metrics = await api.get(`/cats/${catId}/metrics/daily?date=${date}`)
  
  // 渲染图表数据
  renderChart({
    toiletVisits: metrics.toilet_count,
    avgDuration: metrics.avg_duration,
    weight: metrics.weight_data,
    alerts: metrics.health_alerts
  })
}
```

#### 趋势分析
```javascript
async function loadMonthlyTrends(catId) {
  const data = await api.get(`/cats/${catId}/metrics/monthly`)
  
  // 生成趋势图
  renderTrendChart({
    weightTrend: data.weight_trend,
    activityTrend: data.activity_trend,
    healthScore: data.health_score
  })
}
```

### 8. 实时监控界面

#### 设备状态卡片
```javascript
function DeviceStatusCard({ deviceId }) {
  const [status, setStatus] = useState(null)
  
  useEffect(() => {
    // 获取初始状态
    loadDeviceStatus(deviceId)
    
    // 定时更新
    const interval = setInterval(() => {
      updateDeviceStatus(deviceId)
    }, 30000)
    
    return () => clearInterval(interval)
  }, [deviceId])
  
  return (
    <Card>
      <StatusIndicator online={status.online} />
      <BatteryLevel level={status.battery} />
      <LastActivity time={status.lastActivity} />
    </Card>
  )
}
```

## 🔐 安全考虑

### 9. Token管理
```javascript
class TokenManager {
  constructor() {
    this.accessToken = null
    this.refreshToken = null
    this.expiresAt = null
  }
  
  async ensureValidToken() {
    if (this.isTokenExpiring()) {
      await this.refreshAccessToken()
    }
    return this.accessToken
  }
  
  isTokenExpiring() {
    return Date.now() > (this.expiresAt - 300000) // 5分钟前
  }
}
```

### 10. 数据加密
- 本地存储敏感数据时使用加密
- HTTPS通信
- 推送令牌安全处理

## 📱 用户体验优化

### 11. 性能优化
- 图片懒加载
- 分页加载历史记录
- 缓存常用数据
- 压缩网络请求

### 12. 交互设计
- 下拉刷新
- 上拉加载更多
- 骨架屏加载状态
- 空状态提示
- 错误状态处理

## 🧪 测试建议

### 13. API测试
```javascript
// 测试用例示例
describe('Cat Management', () => {
  test('should create cat successfully', async () => {
    const catData = {
      name: 'Fluffy',
      gender: 1,
      weight: 5.5
    }
    
    const response = await api.post('/cats', catData)
    expect(response.status).toBe(200)
    expect(response.data.cat_id).toBeDefined()
  })
})
```

### 14. 集成测试
- 认证流程完整测试
- 离线-在线切换测试
- 推送通知测试
- 错误恢复测试

## 📚 开发资源

### 15. 常用工具
- **API调试**: Postman/Insomnia
- **图表库**: Chart.js/D3.js
- **状态管理**: Redux/MobX/Zustand
- **网络库**: Axios/Fetch
- **本地存储**: AsyncStorage/SQLite

### 16. 参考实现
```
/src
  /api          - API调用封装
  /components   - 通用组件
  /screens      - 页面组件
  /store        - 状态管理
  /utils        - 工具函数
  /constants    - 常量定义
```

这份指南为APP开发提供了完整的技术路线图，结合API文档可以高效地构建智能猫砂盆监控应用。 