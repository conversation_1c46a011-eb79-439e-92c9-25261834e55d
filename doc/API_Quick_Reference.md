# API 快速参考手册

## 🔐 认证API
```
GET  /api/callback         - 登录回调
POST /api/refresh          - 刷新令牌  
GET  /api/user/info        - 用户信息 🔒
GET  /api/health           - 健康检查
```

## 👤 用户管理
```
GET  /api/users/by-username     - 按用户名查找 🔒
GET  /api/users/{user_id}       - 用户详情 🔒
GET  /api/users                 - 用户列表 🔒
GET  /api/users/{user_id}/devices        - 用户设备 🔒
GET  /api/users/{user_id}/profile        - 用户资料 🔒
PUT  /api/users/{user_id}/profile        - 更新资料 🔒
GET  /api/users/{user_id}/settings       - 用户设置 🔒
PUT  /api/users/{user_id}/settings       - 更新设置 🔒
GET  /api/users/{user_id}/notifications  - 用户通知 🔒
```

## 🐱 猫咪管理
```
POST   /api/cats              - 创建猫咪 🔒
GET    /api/cats/{cat_id}     - 猫咪详情 🔒
PUT    /api/cats/{cat_id}     - 更新猫咪 🔒
DELETE /api/cats/{cat_id}     - 删除猫咪 🔒
PUT    /api/cats/{cat_id}/hide     - 隐藏猫咪 🔒
PUT    /api/cats/{cat_id}/restore  - 恢复猫咪 🔒

GET    /api/cats              - 正常猫咪列表 🔒
GET    /api/cats/hidden       - 隐藏猫咪列表 🔒
GET    /api/cats/all          - 所有猫咪 🔒

GET    /api/cats/{cat_id}/metrics/daily    - 每日指标 🔒
GET    /api/cats/{cat_id}/metrics/monthly  - 月度指标 🔒
GET    /api/cats/{cat_id}/alerts          - 健康警报 🔒
```

## 📱 设备管理
```
POST /api/devices/register    - 设备注册
POST /api/devices/heartbeat   - 设备心跳
POST /api/devices/timezone    - 时区设置

GET  /api/devices/{device_id}              - 设备详情
GET  /api/devices                          - 设备列表
GET  /api/devices/{device_id}/status       - 设备状态
GET  /api/devices/{device_id}/statistics   - 设备统计

GET  /api/devices/{device_id}/config       - 设备配置 🔒
PUT  /api/devices/{device_id}/config       - 更新配置 🔒

GET  /api/devices/{device_id}/setting      - OTA设置 🔒
PUT  /api/devices/{device_id}/setting      - 更新OTA设置 🔒
GET  /api/devices/{device_id}/ota-status   - OTA状态 🔒
PUT  /api/devices/{device_id}/ota-status   - 更新OTA状态 🔒

POST /api/devices                          - 创建用户硬件关联
GET  /api/devices/users/{user_id}          - 用户硬件列表
GET  /api/devices/hardware/{hardware_sn}   - 硬件用户查询
GET  /api/devices/check                    - 关联检查
GET  /api/devices/accessible               - 可访问设备
```

## 📊 记录和视频
```
POST /api/records                    - 创建记录
GET  /api/records                    - 记录列表
GET  /api/records/device/{device_id} - 设备记录
GET  /api/records/cat/{cat_id}       - 猫咪记录

GET  /api/records/videos/list                     - 视频列表
GET  /api/records/videos/get                      - 播放列表
GET  /api/records/videos/{folder}/{filename}      - 视频片段
GET  /api/records/videos/thumbnail/{folder}       - 缩略图

POST /api/records/videos/static/{video_id}        - 视频状态检查 🔧
```

## 💻 客户端管理
```
POST   /api/clients/register         - 注册客户端 🔒
PUT    /api/clients/{client_id}      - 更新客户端 🔒
GET    /api/clients/{client_id}      - 客户端详情 🔒
GET    /api/clients                  - 客户端列表 🔒
DELETE /api/clients/{client_id}      - 删除客户端 🔒

POST   /api/clients/{client_id}/token      - 注册推送令牌 🔒
DELETE /api/clients/{client_id}/token      - 删除推送令牌 🔒
GET    /api/clients/{client_id}/token      - 获取推送令牌 🔒
POST   /api/clients/{client_id}/heartbeat  - 客户端心跳 🔒
PUT    /api/clients/{client_id}/status     - 更新客户端状态 🔒
```

## 👨‍👩‍👧‍👦 家庭组
```
POST   /api/family-groups                    - 创建家庭组 🔒
GET    /api/family-groups                    - 家庭组列表 🔒
GET    /api/family-groups/{group_id}         - 家庭组详情 🔒
PUT    /api/family-groups/{group_id}         - 更新家庭组 🔒
DELETE /api/family-groups/{group_id}         - 删除家庭组 🔒

GET    /api/family-groups/{group_id}/members           - 成员列表 🔒
POST   /api/family-groups/{group_id}/members           - 添加成员 🔒
PUT    /api/family-groups/{group_id}/members/{member_id} - 更新成员 🔒
DELETE /api/family-groups/{group_id}/members/{member_id} - 移除成员 🔒

GET    /api/family-groups/{group_id}/devices           - 共享设备列表 🔒
POST   /api/family-groups/{group_id}/devices           - 添加设备 🔒
DELETE /api/family-groups/{group_id}/devices/{device_id} - 移除设备 🔒

POST /api/family-groups/{group_id}/invitations       - 发送邀请 🔒
POST /api/family-groups/{group_id}/invitations/email - 邮箱邀请 🔒

GET    /api/family-groups/invitations/received          - 收到的邀请 🔒
GET    /api/family-groups/invitations/sent              - 发送的邀请 🔒
GET    /api/family-groups/invitations/{invitation_id}   - 邀请详情 🔒
PUT    /api/family-groups/invitations/{invitation_id}/process - 处理邀请 🔒
DELETE /api/family-groups/invitations/{invitation_id}/cancel  - 取消邀请 🔒
```

## 📈 统计指标
```
GET /api/metrics/cats/{cat_id}/daily    - 猫咪每日指标 🔒
GET /api/metrics/cats/{cat_id}/monthly  - 猫咪月度指标 🔒
```

## 🔔 通知管理
```
GET  /api/notifications/settings    - 通知设置 🔒
PUT  /api/notifications/settings    - 更新通知设置 🔒
POST /api/notifications             - 创建通知 🔒
GET  /api/notifications             - 通知列表 🔒
PUT  /api/notifications/{id}/read   - 标记已读 🔒
```

## 💾 存储服务
```
GET /api/storage/assets/*filepath   - 获取静态资源 🔒
```

---

## 图标说明
- 🔒 需要用户认证
- 🔧 需要服务认证
- 无图标：公开API

## 常用状态码
- `200` 成功
- `400` 参数错误  
- `401` 未认证
- `403` 权限不足
- `404` 资源不存在
- `500` 服务器错误

## 认证Header
```
Authorization: Bearer <access_token>
``` 