# 视频播放功能 API 详细文档

## 概述

本文档详细描述智能猫砂盆系统中视频播放功能的所有相关API接口。这些API提供了完整的视频录像查看、播放和管理功能，支持HLS流媒体播放。

**基础URL**: `http://your-server.com/api/records/videos`

## 认证机制

所有视频API都是公开接口，无需认证。但在生产环境中建议根据实际需求添加适当的权限控制。

---

## 1. 视频列表接口

### 1.1 基本信息
- **接口路径**: `GET /api/records/videos/list`
- **功能描述**: 获取指定时间范围内的视频记录列表
- **认证要求**: 无需认证
- **内容类型**: `application/json`

### 1.2 请求参数

#### Query Parameters (必填)
| 参数名 | 类型 | 是否必填 | 描述 | 示例 |
|--------|------|----------|------|------|
| `path` | string | 是 | 设备路径，用于识别设备ID | `device123` |
| `start` | string | 是 | 开始日期 | `2024-01-15` |
| `end` | string | 是 | 结束日期 | `2024-01-16` |

#### 参数说明
- **path**: 设备路径格式，系统会从中解析出设备ID
- **start**: 日期格式为 `YYYY-MM-DD`，表示查询的开始日期
- **end**: 日期格式为 `YYYY-MM-DD`，表示查询的结束日期（实际查询到该日期后48小时减1秒）

### 1.3 返回数据

#### 成功响应 (200 OK)
```json
[
  {
    "start": 1642204800,
    "duration": "45.5",
    "url": "https://your-server.com/api/records/videos/get?path=records/device123&start=2024-01-15T08:00:00Z&duration=45.5",
    "weight_litter": 2.5,
    "weight_cat": 4.2,
    "weight_waste": 0.3,
    "thumbnail_url": "https://your-server.com/api/records/videos/thumbnail/2024-01-15T08%3A00%3A00%2B08%3A00?bucket=records/device123",
    "animal_id": "cat_001"
  },
  {
    "start": 1642208400,
    "duration": "32.1",
    "url": "https://your-server.com/api/records/videos/get?path=records/device123&start=2024-01-15T09:00:00Z&duration=32.1",
    "weight_litter": 2.3,
    "weight_cat": 4.1,
    "weight_waste": 0.4,
    "thumbnail_url": "https://your-server.com/api/records/videos/thumbnail/2024-01-15T09%3A00%3A00%2B08%3A00?bucket=records/device123",
    "animal_id": "cat_002"
  }
]
```

#### 字段说明
| 字段名 | 类型 | 描述 |
|--------|------|------|
| `start` | int64 | 视频开始时间的Unix时间戳 |
| `duration` | string | 视频持续时间（秒），格式为小数字符串 |
| `url` | string | 视频播放URL，指向HLS播放列表 |
| `weight_litter` | float64 | 猫砂重量（公斤） |
| `weight_cat` | float64 | 猫咪重量（公斤） |
| `weight_waste` | float64 | 排泄物重量（公斤） |
| `thumbnail_url` | string | 缩略图URL |
| `animal_id` | string | 猫咪ID，可能为空 |

#### 错误响应
- **400 Bad Request**: 参数错误或时间格式错误
  ```json
  []
  ```
- **500 Internal Server Error**: 服务器内部错误
  ```json
  []
  ```

### 1.4 使用示例

```bash
# 获取2024年1月15日的视频列表
curl -X GET "http://your-server.com/api/records/videos/list?path=device123&start=2024-01-15&end=2024-01-15"

# 获取一周的视频列表
curl -X GET "http://your-server.com/api/records/videos/list?path=device123&start=2024-01-15&end=2024-01-22"
```

---

## 2. 获取播放列表接口

### 2.1 基本信息
- **接口路径**: `GET /api/records/videos/get`
- **功能描述**: 获取HLS播放列表文件(.m3u8)，用于视频播放
- **认证要求**: 无需认证
- **内容类型**: `application/vnd.apple.mpegurl`

### 2.2 请求参数

#### Query Parameters (必填)
| 参数名 | 类型 | 是否必填 | 描述 | 示例 |
|--------|------|----------|------|------|
| `path` | string | 是 | 设备路径 | `device123` |
| `start` | string | 是 | 视频开始时间 | `2024-01-15T08:00:00Z` |
| `duration` | string | 否 | 视频持续时间（秒） | `45.5` |

#### 参数说明
- **path**: 设备路径，用于识别设备ID
- **start**: ISO 8601格式的时间戳，表示视频开始时间
- **duration**: 视频持续时间，可选参数

### 2.3 返回数据

#### 成功响应 (200 OK)
```
#EXTM3U
#EXT-X-VERSION:3
#EXT-X-TARGETDURATION:10
#EXT-X-MEDIA-SEQUENCE:0
#EXTINF:10.0,
/api/records/videos/2024-01-15_08-00-00_hls/segment_0.ts?bucket=records/device123
#EXTINF:10.0,
/api/records/videos/2024-01-15_08-00-00_hls/segment_1.ts?bucket=records/device123
#EXTINF:10.0,
/api/records/videos/2024-01-15_08-00-00_hls/segment_2.ts?bucket=records/device123
#EXTINF:5.5,
/api/records/videos/2024-01-15_08-00-00_hls/segment_3.ts?bucket=records/device123
#EXT-X-ENDLIST
```

#### 响应头
- `Content-Type`: `application/vnd.apple.mpegurl`
- `Access-Control-Allow-Origin`: `*`
- `Cache-Control`: `max-age=3600`

#### 错误响应
- **400 Bad Request**: 参数错误
  ```
  参数错误
  ```
- **500 Internal Server Error**: 获取视频失败
  ```
  获取视频失败
  ```

### 2.4 使用示例

```bash
# 获取播放列表
curl -X GET "http://your-server.com/api/records/videos/get?path=records/device123&start=2024-01-15T08:00:00Z&duration=45.5"

# 在网页中使用HLS播放器
# <video id="video" controls>
#   <source src="http://your-server.com/api/records/videos/get?path=records/device123&start=2024-01-15T08:00:00Z" type="application/vnd.apple.mpegurl">
# </video>
```

---

## 3. 获取视频片段接口

### 3.1 基本信息
- **接口路径**: `GET /api/records/videos/{folder}/{filename}`
- **功能描述**: 获取视频的TS片段文件，用于HLS流媒体播放
- **认证要求**: 无需认证
- **内容类型**: `video/MP2T`

### 3.2 请求参数

#### Path Parameters (必填)
| 参数名 | 类型 | 是否必填 | 描述 | 示例 |
|--------|------|----------|------|------|
| `folder` | string | 是 | 视频文件夹名称 | `2024-01-15_08-00-00_hls` |
| `filename` | string | 是 | 片段文件名 | `segment_0.ts` |

#### Query Parameters (必填)
| 参数名 | 类型 | 是否必填 | 描述 | 示例 |
|--------|------|----------|------|------|
| `bucket` | string | 是 | 存储桶路径 | `records/device123` |

#### 参数说明
- **folder**: 视频文件夹名称，格式为 `YYYY-MM-DD_HH-MM-SS_hls`
- **filename**: TS片段文件名，格式为 `segment_N.ts`
- **bucket**: 存储桶路径，包含设备信息

### 3.3 返回数据

#### 成功响应 (200 OK)
- **内容类型**: `video/MP2T`
- **响应体**: 二进制视频数据

#### 响应头
- `Content-Type`: `video/MP2T`
- `Access-Control-Allow-Origin`: `*`
- `Cache-Control`: `max-age=3600`

#### 错误响应
- **404 Not Found**: 文件不存在或参数错误
- **400 Bad Request**: 参数格式错误

### 3.4 使用示例

```bash
# 获取视频片段
curl -X GET "http://your-server.com/api/records/videos/2024-01-15_08-00-00_hls/segment_0.ts?bucket=records/device123"

# 通常这个接口由HLS播放器自动调用，不需要手动调用
```

---

## 4. 获取缩略图接口

### 4.1 基本信息
- **接口路径**: `GET /api/records/videos/thumbnail/{folder}`
- **功能描述**: 获取视频的缩略图
- **认证要求**: 无需认证
- **内容类型**: `image/jpeg`

### 4.2 请求参数

#### Path Parameters (必填)
| 参数名 | 类型 | 是否必填 | 描述 | 示例 |
|--------|------|----------|------|------|
| `folder` | string | 是 | URL编码的时间戳 | `2024-01-15T08%3A00%3A00%2B08%3A00` |

#### Query Parameters (必填)
| 参数名 | 类型 | 是否必填 | 描述 | 示例 |
|--------|------|----------|------|------|
| `bucket` | string | 是 | 存储桶路径 | `records/device123` |

#### 参数说明
- **folder**: URL编码的时间戳，需要进行URL编码
- **bucket**: 存储桶路径，包含设备信息

### 4.3 支持的时间格式

系统支持多种时间格式，优先级从高到低：
1. `2006-01-02T15:04:05+08:00` (推荐)
2. `2006-01-02T15:04:05Z07:00`
3. `2006-01-02T15:04:05 08:00`
4. `2006-01-02T15:04:05-07:00`
5. `2006-01-02T15:04:05Z`

### 4.4 返回数据

#### 成功响应 (200 OK)
- **内容类型**: `image/jpeg`
- **响应体**: 二进制图片数据

#### 响应头
- `Content-Type`: `image/jpeg`
- `Access-Control-Allow-Origin`: `*`
- `Cache-Control`: `max-age=86400` (缓存1天)

#### 错误响应
- **400 Bad Request**: 参数错误或时间格式错误
- **404 Not Found**: 缩略图不存在

### 4.5 使用示例

```bash
# 获取缩略图
curl -X GET "http://your-server.com/api/records/videos/thumbnail/2024-01-15T08%3A00%3A00%2B08%3A00?bucket=records/device123"

# JavaScript中使用
const thumbnailUrl = `http://your-server.com/api/records/videos/thumbnail/${encodeURIComponent('2024-01-15T08:00:00+08:00')}?bucket=records/device123`;
```

---

## 5. 完整的视频播放流程

### 5.1 推荐的集成流程

1. **获取视频列表**
   ```javascript
   const videoList = await fetch(`/api/records/videos/list?path=device123&start=2024-01-15&end=2024-01-15`);
   const videos = await videoList.json();
   ```

2. **显示缩略图**
   ```javascript
   videos.forEach(video => {
     const img = document.createElement('img');
     img.src = video.thumbnail_url;
     img.onclick = () => playVideo(video.url);
   });
   ```

3. **播放视频**
   ```javascript
   function playVideo(videoUrl) {
     const video = document.getElementById('video-player');
     video.src = videoUrl;
     video.play();
   }
   ```

### 5.2 HLS播放器集成

#### 使用原生支持
```html
<video id="video" controls>
  <source src="http://your-server.com/api/records/videos/get?path=records/device123&start=2024-01-15T08:00:00Z" type="application/vnd.apple.mpegurl">
</video>
```

#### 使用hls.js库
```javascript
import Hls from 'hls.js';

const video = document.getElementById('video');
const videoSrc = 'http://your-server.com/api/records/videos/get?path=records/device123&start=2024-01-15T08:00:00Z';

if (Hls.isSupported()) {
  const hls = new Hls();
  hls.loadSource(videoSrc);
  hls.attachMedia(video);
} else if (video.canPlayType('application/vnd.apple.mpegurl')) {
  video.src = videoSrc;
}
```

---

## 6. 错误处理和最佳实践

### 6.1 错误处理

```javascript
async function getVideoList(devicePath, startDate, endDate) {
  try {
    const response = await fetch(`/api/records/videos/list?path=${devicePath}&start=${startDate}&end=${endDate}`);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const videos = await response.json();
    
    // 检查是否返回空数组
    if (videos.length === 0) {
      console.log('指定时间范围内没有找到视频');
      return [];
    }
    
    return videos;
  } catch (error) {
    console.error('获取视频列表失败:', error);
    throw error;
  }
}
```

### 6.2 性能优化建议

1. **缓存处理**
   - 缩略图缓存24小时
   - 播放列表缓存1小时
   - 视频片段缓存1小时

2. **分页加载**
   - 建议一次查询不超过7天的数据
   - 实现懒加载减少初始加载时间

3. **网络优化**
   - 使用CDN加速视频传输
   - 实现视频预加载机制

### 6.3 常见问题处理

1. **视频无法播放**
   - 检查浏览器是否支持HLS
   - 确认网络连接正常
   - 验证时间格式是否正确

2. **缩略图显示异常**
   - 检查时间戳是否正确编码
   - 确认设备路径格式正确

3. **播放卡顿**
   - 检查网络带宽
   - 考虑降低视频质量
   - 使用适当的缓存策略

---

## 7. 数据格式和约定

### 7.1 时间格式约定

- **输入时间**: 支持多种ISO 8601格式
- **输出时间**: Unix时间戳（秒）
- **URL编码**: 时间戳作为URL参数时需要进行URL编码

### 7.2 设备路径格式

- **格式**: `device{deviceID}` 或直接设备ID
- **示例**: `device123`, `ABC123456`

### 7.3 文件命名约定

- **视频文件夹**: `YYYY-MM-DD_HH-MM-SS_hls`
- **视频片段**: `segment_N.ts`
- **播放列表**: `playlist.m3u8`

---

## 8. 移动端优化

### 8.1 iOS集成

```swift
import AVKit
import AVFoundation

let player = AVPlayer(url: URL(string: "http://your-server.com/api/records/videos/get?path=records/device123&start=2024-01-15T08:00:00Z")!)
let playerViewController = AVPlayerViewController()
playerViewController.player = player
present(playerViewController, animated: true)
```

### 8.2 Android集成

```java
// 使用ExoPlayer
String videoUrl = "http://your-server.com/api/records/videos/get?path=records/device123&start=2024-01-15T08:00:00Z";
MediaItem mediaItem = MediaItem.fromUri(videoUrl);
player.setMediaItem(mediaItem);
player.prepare();
player.play();
```

这份文档提供了视频播放功能的完整API参考，包含了所有必要的技术细节和实用示例，足以支持另一个AI开发者完成视频播放功能的实现。 