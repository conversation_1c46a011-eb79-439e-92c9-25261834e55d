# 设备OTA状态管理API文档

## 功能概述

设备OTA状态管理功能允许设备端更新OTA升级状态，同时App端可以实时查看设备是否正在进行OTA升级。

### 主要特性

1. **实时状态更新**：设备端可以每分钟更新一次OTA状态
2. **自动超时处理**：如果3分钟没有更新，系统自动将状态重置为idle
3. **状态验证**：支持四种状态值：idle、updating、failed、completed
4. **权限控制**：App端只能查看有权限访问的设备OTA状态

## API接口

### 1. 获取设备OTA状态

**接口说明**：App端获取指定设备的OTA状态

**请求方式**：`GET /api/devices/{device_id}/ota-status`

**请求参数**：
- `device_id`（路径参数）：设备ID

**响应示例**：
```json
{
  "device_id": "CAT_20240301_0001",
  "status": "idle",
  "last_updated": "2024-03-01T12:00:00Z"
}
```

**状态码**：
- 200：获取成功
- 400：请求参数错误
- 403：无权限访问此设备
- 404：设备不存在
- 500：服务器内部错误

### 2. 更新设备OTA状态

**接口说明**：设备端更新OTA状态

**请求方式**：`PUT /api/devices/{device_id}/ota-status`

**请求参数**：
- `device_id`（路径参数）：设备ID

**请求体**：
```json
{
  "device_id": "CAT_20240301_0001",
  "status": "updating"
}
```

**状态值说明**：
- `idle`：空闲状态（默认）
- `updating`：正在更新
- `failed`：更新失败
- `completed`：更新完成

**响应示例**：
```json
{
  "status": "success",
  "message": "OTA状态更新成功"
}
```

**状态码**：
- 200：更新成功
- 400：请求参数错误或无效的状态值
- 404：设备不存在
- 500：服务器内部错误

## 数据库表结构

```sql
CREATE TABLE `device_ota_status` (
  `device_id` varchar(64) NOT NULL,
  `status` varchar(32) NOT NULL DEFAULT 'idle' COMMENT 'OTA状态: idle-空闲, updating-更新中, failed-失败, completed-完成',
  `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`device_id`),
  CONSTRAINT `device_ota_status_ibfk_1` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
```

## 使用场景

### 设备端使用

1. **开始OTA升级时**：
   ```bash
   curl -X PUT "/api/devices/{device_id}/ota-status" \
     -H "Content-Type: application/json" \
     -d '{"device_id": "CAT_20240301_0001", "status": "updating"}'
   ```

2. **升级过程中**（每分钟更新一次）：
   ```bash
   curl -X PUT "/api/devices/{device_id}/ota-status" \
     -H "Content-Type: application/json" \
     -d '{"device_id": "CAT_20240301_0001", "status": "updating"}'
   ```

3. **升级完成时**：
   ```bash
   curl -X PUT "/api/devices/{device_id}/ota-status" \
     -H "Content-Type: application/json" \
     -d '{"device_id": "CAT_20240301_0001", "status": "completed"}'
   ```

4. **升级失败时**：
   ```bash
   curl -X PUT "/api/devices/{device_id}/ota-status" \
     -H "Content-Type: application/json" \
     -d '{"device_id": "CAT_20240301_0001", "status": "failed"}'
   ```

### App端使用

App端可以定期查询设备OTA状态，了解设备是否正在进行升级：

```bash
curl -X GET "/api/devices/{device_id}/ota-status" \
  -H "Authorization: Bearer {access_token}"
```

## 自动监控机制

系统内置了自动监控机制，每分钟检查一次所有设备的OTA状态：
- 如果某个设备的OTA状态超过3分钟没有更新
- 且状态不是`idle`
- 系统会自动将其状态重置为`idle`

这确保了即使设备异常断开连接，OTA状态也不会一直处于`updating`状态。

## 测试

使用提供的测试脚本验证功能：

```bash
./test_ota_status.sh
```

测试脚本会：
1. 测试获取OTA状态
2. 测试更新为各种状态值
3. 测试无效状态值的处理
4. 验证状态变更是否生效

## 注意事项

1. **设备ID验证**：确保设备ID在数据库中存在
2. **状态值验证**：只接受预定义的四种状态值
3. **权限控制**：App端需要有对应设备的访问权限
4. **自动超时**：超过3分钟没有更新会自动重置为idle状态
5. **并发安全**：支持多个设备同时更新OTA状态 