# 猫砂盆智能监控系统 API 文档

## 概述

这是一个智能猫砂盆监控系统的后端API文档。系统提供猫咪如厕行为监控、设备管理、用户管理、家庭组协作等功能。

**基础URL**: `http://your-server.com/api`

## 认证机制

API分为三个级别：
1. **公开API** - 无需认证
2. **用户API** - 需要用户认证（Bearer Token）
3. **服务API** - 需要服务间认证

### 认证Headers
```
Authorization: Bearer <access_token>
```

---

## 1. 认证相关 API

### 1.1 处理登录回调
- **路径**: `GET /api/callback`
- **描述**: 处理第三方登录（Logto）的回调
- **认证**: 无需认证
- **参数**: 
  - `code` (query): 授权码
  - `state` (query): 状态参数
  - `code_verifier` (query): PKCE验证码
- **返回**: 
  ```json
  {
    "access_token": "string",
    "refresh_token": "string", 
    "user_id": "string",
    "message": "Login successful"
  }
  ```

### 1.2 刷新访问令牌
- **路径**: `POST /api/refresh`
- **描述**: 使用refresh token获取新的access token
- **认证**: 无需认证
- **请求体**:
  ```json
  {
    "refresh_token": "string"
  }
  ```
- **返回**: 新的token信息

### 1.3 获取用户信息
- **路径**: `GET /api/user/info`
- **描述**: 获取当前登录用户的详细信息
- **认证**: 需要用户认证
- **返回**: 用户详细信息

### 1.4 健康检查
- **路径**: `GET /api/health`
- **描述**: 检查服务器状态
- **认证**: 无需认证
- **返回**: `{"status": "ok"}`

---

## 2. 用户管理 API

### 2.1 通过用户名获取用户
- **路径**: `GET /api/users/by-username`
- **描述**: 根据用户名查找用户信息
- **认证**: 需要用户认证
- **参数**: `username` (query)

### 2.2 获取用户详情
- **路径**: `GET /api/users/{user_id}`
- **描述**: 获取指定用户的详细信息
- **认证**: 需要用户认证

### 2.3 获取用户列表
- **路径**: `GET /api/users`
- **描述**: 获取所有用户列表（管理员功能）
- **认证**: 需要用户认证

### 2.4 获取用户设备
- **路径**: `GET /api/users/{user_id}/devices`
- **描述**: 获取用户拥有的所有设备
- **认证**: 需要用户认证

### 2.5 用户资料管理
- **获取**: `GET /api/users/{user_id}/profile`
- **更新**: `PUT /api/users/{user_id}/profile`
- **描述**: 管理用户个人资料（昵称、头像、联系方式等）
- **认证**: 需要用户认证

### 2.6 用户设置管理
- **获取**: `GET /api/users/{user_id}/settings`
- **更新**: `PUT /api/users/{user_id}/settings`
- **描述**: 管理用户应用设置（语言、时区、通知偏好等）
- **认证**: 需要用户认证

### 2.7 获取用户通知
- **路径**: `GET /api/users/{user_id}/notifications`
- **描述**: 获取用户的通知消息
- **认证**: 需要用户认证

---

## 3. 猫咪管理 API

### 3.1 创建猫咪档案
- **路径**: `POST /api/cats`
- **描述**: 为用户创建新的猫咪档案
- **认证**: 需要用户认证
- **请求体**:
  ```json
  {
    "name": "string",
    "gender": 0,  // 0-未知, 1-公猫, 2-母猫
    "birthday": "2023-01-01",
    "weight": 5.5,
    "breed": "英短",
    "color": "银渐层",
    "avatar_base64": "base64_image_data"
  }
  ```

### 3.2 获取猫咪信息
- **路径**: `GET /api/cats/{cat_id}`
- **描述**: 获取指定猫咪的详细信息
- **认证**: 需要用户认证

### 3.3 更新猫咪信息
- **路径**: `PUT /api/cats/{cat_id}`
- **描述**: 更新猫咪的基本信息
- **认证**: 需要用户认证

### 3.4 猫咪状态管理
- **删除**: `DELETE /api/cats/{cat_id}` - 软删除猫咪
- **隐藏**: `PUT /api/cats/{cat_id}/hide` - 隐藏猫咪
- **恢复**: `PUT /api/cats/{cat_id}/restore` - 恢复隐藏的猫咪
- **认证**: 需要用户认证

### 3.5 猫咪列表查询
- **正常猫咪**: `GET /api/cats` - 获取用户的活跃猫咪列表
- **隐藏猫咪**: `GET /api/cats/hidden` - 获取隐藏的猫咪列表
- **所有猫咪**: `GET /api/cats/all` - 获取所有猫咪（包括隐藏的）
- **认证**: 需要用户认证

### 3.6 猫咪健康数据
- **每日指标**: `GET /api/cats/{cat_id}/metrics/daily`
- **月度指标**: `GET /api/cats/{cat_id}/metrics/monthly`
- **健康警报**: `GET /api/cats/{cat_id}/alerts`
- **描述**: 获取猫咪的健康监控数据和异常警报
- **认证**: 需要用户认证

---

## 4. 设备管理 API

### 4.1 设备注册和心跳
- **注册设备**: `POST /api/devices/register`
- **设备心跳**: `POST /api/devices/heartbeat`
- **时区设置**: `POST /api/devices/timezone`
- **描述**: 设备初始化和保活机制
- **认证**: 无需认证（公开API）

### 4.2 设备信息查询
- **获取设备**: `GET /api/devices/{device_id}`
- **用户设备列表**: `GET /api/devices`
- **设备状态**: `GET /api/devices/{device_id}/status`
- **设备统计**: `GET /api/devices/{device_id}/statistics`
- **认证**: 部分需要用户认证

### 4.3 设备配置管理
- **获取配置**: `GET /api/devices/{device_id}/config`
- **更新配置**: `PUT /api/devices/{device_id}/config`
- **描述**: 管理设备的工作参数（位置、安装角度、传感器配置等）
- **认证**: 需要用户认证

### 4.4 OTA更新管理
- **获取OTA设置**: `GET /api/devices/{device_id}/setting`
- **更新OTA设置**: `PUT /api/devices/{device_id}/setting`
- **OTA状态查询**: `GET /api/devices/{device_id}/ota-status`
- **OTA状态更新**: `PUT /api/devices/{device_id}/ota-status`
- **描述**: 管理设备的固件自动更新
- **认证**: 需要用户认证

### 4.5 用户硬件关联
- **创建关联**: `POST /api/devices`
- **用户硬件列表**: `GET /api/devices/users/{user_id}`
- **硬件用户查询**: `GET /api/devices/hardware/{hardware_sn}`
- **关联检查**: `GET /api/devices/check`
- **可访问设备**: `GET /api/devices/accessible`
- **描述**: 管理用户与物理设备的绑定关系
- **认证**: 部分需要用户认证

---

## 5. 记录和视频 API

### 5.1 如厕记录管理
- **创建记录**: `POST /api/records`
- **获取记录列表**: `GET /api/records`
- **设备记录**: `GET /api/records/device/{device_id}`
- **猫咪记录**: `GET /api/records/cat/{cat_id}`
- **描述**: 管理猫咪的如厕行为记录
- **认证**: 无需认证（公开API）

### 5.2 视频数据API
- **视频列表**: `GET /api/records/videos/list`
- **获取播放列表**: `GET /api/records/videos/get`
- **视频片段**: `GET /api/records/videos/{folder}/{filename}`
- **缩略图**: `GET /api/records/videos/thumbnail/{folder}`
- **描述**: 访问监控视频和相关媒体文件
- **认证**: 无需认证（公开API）

### 5.3 服务间API
- **视频状态检查**: `POST /api/records/videos/static/{video_id}`
- **描述**: 内部服务调用，检查视频处理状态
- **认证**: 需要服务认证

---

## 6. 客户端管理 API

### 6.1 客户端生命周期
- **注册客户端**: `POST /api/clients/register`
- **更新客户端**: `PUT /api/clients/{client_id}`
- **获取客户端**: `GET /api/clients/{client_id}`
- **客户端列表**: `GET /api/clients`
- **删除客户端**: `DELETE /api/clients/{client_id}`
- **描述**: 管理用户的APP客户端（手机、平板等）
- **认证**: 需要用户认证

### 6.2 推送通知管理
- **注册推送令牌**: `POST /api/clients/{client_id}/token`
- **删除推送令牌**: `DELETE /api/clients/{client_id}/token`
- **获取推送令牌**: `GET /api/clients/{client_id}/token`
- **描述**: 管理APNs/FCM推送通知的设备令牌
- **认证**: 需要用户认证

### 6.3 客户端状态
- **心跳**: `POST /api/clients/{client_id}/heartbeat`
- **状态更新**: `PUT /api/clients/{client_id}/status`
- **描述**: 维护客户端在线状态和活跃度
- **认证**: 需要用户认证

---

## 7. 家庭组协作 API

### 7.1 家庭组管理
- **创建家庭组**: `POST /api/family-groups`
- **获取家庭组列表**: `GET /api/family-groups`
- **获取家庭组详情**: `GET /api/family-groups/{group_id}`
- **更新家庭组**: `PUT /api/family-groups/{group_id}`
- **删除家庭组**: `DELETE /api/family-groups/{group_id}`
- **描述**: 创建和管理家庭共享群组
- **认证**: 需要用户认证

### 7.2 成员管理
- **获取成员列表**: `GET /api/family-groups/{group_id}/members`
- **添加成员**: `POST /api/family-groups/{group_id}/members`
- **更新成员**: `PUT /api/family-groups/{group_id}/members/{member_id}`
- **移除成员**: `DELETE /api/family-groups/{group_id}/members/{member_id}`
- **描述**: 管理家庭组的成员和权限
- **认证**: 需要用户认证

### 7.3 设备共享
- **获取共享设备**: `GET /api/family-groups/{group_id}/devices`
- **添加设备**: `POST /api/family-groups/{group_id}/devices`
- **移除设备**: `DELETE /api/family-groups/{group_id}/devices/{device_id}`
- **描述**: 在家庭组内共享监控设备
- **认证**: 需要用户认证

### 7.4 邀请管理
- **发送邀请**: `POST /api/family-groups/{group_id}/invitations`
- **邮箱邀请**: `POST /api/family-groups/{group_id}/invitations/email`
- **收到的邀请**: `GET /api/family-groups/invitations/received`
- **发送的邀请**: `GET /api/family-groups/invitations/sent`
- **邀请详情**: `GET /api/family-groups/invitations/{invitation_id}`
- **处理邀请**: `PUT /api/family-groups/invitations/{invitation_id}/process`
- **取消邀请**: `DELETE /api/family-groups/invitations/{invitation_id}/cancel`
- **描述**: 管理家庭组邀请流程
- **认证**: 需要用户认证

---

## 8. 统计指标 API

### 8.1 猫咪健康统计
- **每日指标**: `GET /api/metrics/cats/{cat_id}/daily`
- **月度指标**: `GET /api/metrics/cats/{cat_id}/monthly`
- **描述**: 获取猫咪的健康数据统计分析
- **参数**: 
  - `date` (query): 指定日期 (YYYY-MM-DD)
  - `year` (query): 年份
  - `month` (query): 月份
- **返回数据包括**:
  - 如厕频次统计
  - 体重变化趋势
  - 异常行为记录
  - 健康评分
- **认证**: 需要用户认证

### 8.2 设备统计 (预留)
- 设备的每日和月度运行统计
- 目前API已预留但未实现

---

## 9. 通知管理 API

### 9.1 通知设置
- **获取设置**: `GET /api/notifications/settings`
- **更新设置**: `PUT /api/notifications/settings`
- **描述**: 管理用户的通知偏好设置
- **设置项包括**:
  - 每日报告开关
  - 统计推送开关
  - 免打扰时间段
  - 时区设置
- **认证**: 需要用户认证

### 9.2 通知消息
- **创建通知**: `POST /api/notifications`
- **获取通知列表**: `GET /api/notifications`
- **标记已读**: `PUT /api/notifications/{id}/read`
- **描述**: 管理系统通知和用户消息
- **认证**: 需要用户认证

---

## 10. 存储服务 API

### 10.1 资源访问
- **获取文件**: `GET /api/storage/assets/*filepath`
- **描述**: 访问存储的静态资源（头像、图片等）
- **用途**:
  - 猫咪头像图片
  - 用户头像图片
  - 其他上传的媒体文件
- **认证**: 需要用户认证

---

## 数据模型概览

### 主要实体关系
```
User (用户)
├── Cat (猫咪) - 一对多
├── Device (设备) - 一对多  
├── Client (客户端) - 一对多
└── FamilyGroup (家庭组) - 多对多

Device (设备)
├── RecordShit (如厕记录) - 一对多
└── DeviceStatus (设备状态) - 一对一

Cat (猫咪)
├── CatMetricsDaily (每日统计) - 一对多
├── CatMetricsMonthly (月度统计) - 一对多
└── CatAlert (健康警报) - 一对多
```

### 关键状态值
- **用户状态**: 1-正常, 2-禁用
- **猫咪状态**: 1-正常, 0-隐藏, -1-删除
- **设备状态**: 1-正常, 2-离线, 3-故障
- **记录状态**: 1-正常, 2-删除
- **邀请状态**: 0-待处理, 1-已接受, 2-已拒绝, 3-已过期

---

## 错误码说明

### HTTP状态码
- **200** - 成功
- **400** - 请求参数错误
- **401** - 未认证或认证失败
- **403** - 权限不足
- **404** - 资源不存在
- **500** - 服务器内部错误

### 业务错误码
```json
{
  "code": 400,
  "message": "参数错误",
  "error": "具体错误信息"
}
```

---

## APP开发建议

### 1. 认证流程
1. 引导用户到Logto登录页面
2. 处理回调获取access_token
3. 使用token调用API
4. 定期使用refresh_token刷新

### 2. 核心功能实现
- **首页**: 显示用户的猫咪列表和最近记录
- **猫咪管理**: 添加、编辑猫咪档案
- **实时监控**: 查看设备状态和实时视频
- **健康报告**: 展示统计数据和趋势分析
- **家庭共享**: 邀请家人共同关注

### 3. 推送通知
- 注册客户端和推送令牌
- 处理异常行为警报
- 定期健康报告推送

### 4. 离线处理
- 缓存关键数据
- 支持离线查看历史记录
- 网络恢复时同步数据

这份文档涵盖了系统的所有API接口，为APP开发提供了完整的技术参考。 